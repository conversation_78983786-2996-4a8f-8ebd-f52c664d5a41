"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/products/page",{

/***/ "(app-pages-browser)/./src/components/ProductCard.tsx":
/*!****************************************!*\
  !*** ./src/components/ProductCard.tsx ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ProductCard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_Icons__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Icons */ \"(app-pages-browser)/./src/components/Icons.tsx\");\n/* harmony import */ var _context_UserContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/context/UserContext */ \"(app-pages-browser)/./src/context/UserContext.tsx\");\n/* harmony import */ var _Message__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Message */ \"(app-pages-browser)/./src/components/Message.tsx\");\n/* harmony import */ var _VerificationBadge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./VerificationBadge */ \"(app-pages-browser)/./src/components/VerificationBadge.tsx\");\n/* harmony import */ var _ReportButton__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ReportButton */ \"(app-pages-browser)/./src/components/ReportButton.tsx\");\n/* harmony import */ var _LocationDisplay__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./LocationDisplay */ \"(app-pages-browser)/./src/components/LocationDisplay.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction ProductCard(param) {\n    let { product, showDistance = false } = param;\n    _s();\n    const { addToWishlist, removeFromWishlist, isInWishlist } = (0,_context_UserContext__WEBPACK_IMPORTED_MODULE_4__.useUser)();\n    const [isHovered, setIsHovered] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleWishlistClick = (e)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        if (isInWishlist(product.id)) {\n            removeFromWishlist(product.id);\n        } else {\n            addToWishlist(product);\n        }\n    };\n    const handleReport = (data)=>{\n        // Here we would typically send the report to a backend\n        console.log(\"Report submitted:\", data);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 overflow-hidden\",\n        onMouseEnter: ()=>setIsHovered(true),\n        onMouseLeave: ()=>setIsHovered(false),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                href: \"/products/\".concat(product.id),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: \"/placeholder.png\",\n                                alt: product.name,\n                                className: \"w-full h-48 object-cover\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                                lineNumber: 50,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleWishlistClick,\n                                className: \"absolute top-2 right-2 p-2 rounded-full transition-colors \".concat(isInWishlist(product.id) ? \"bg-red-500 text-white\" : \"bg-white/80 text-slate-600 hover:bg-white\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Icons__WEBPACK_IMPORTED_MODULE_3__.HeartIcon, {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"font-semibold text-lg mb-1\",\n                                children: product.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-slate-600 mb-2\",\n                                children: typeof product.price === \"number\" ? \"$\".concat(product.price.toFixed(2)) : product.price\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 11\n                            }, this),\n                            product.location && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LocationDisplay__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                location: product.location,\n                                showDistance: showDistance,\n                                className: \"mb-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 13\n                            }, this),\n                            product.seller && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-slate-600\",\n                                            children: product.seller.name\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_VerificationBadge__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            status: product.seller.verificationStatus,\n                                            size: \"sm\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 pb-4 flex items-center justify-between\",\n                children: [\n                    product.seller && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Message__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        productId: product.id,\n                        sellerId: product.seller.id,\n                        productName: product.name,\n                        sellerName: product.seller.name || \"\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ReportButton__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        itemId: product.id,\n                        itemType: \"product\",\n                        onReport: handleReport\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                lineNumber: 91,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\n_s(ProductCard, \"fmUKcEfUJ8CcATBAvqdC0qcB1Do=\", false, function() {\n    return [\n        _context_UserContext__WEBPACK_IMPORTED_MODULE_4__.useUser\n    ];\n});\n_c = ProductCard;\nvar _c;\n$RefreshReg$(_c, \"ProductCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ProductCard.tsx\n"));

/***/ })

});