"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/products/page",{

/***/ "(app-pages-browser)/./src/components/ProductCard.tsx":
/*!****************************************!*\
  !*** ./src/components/ProductCard.tsx ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ProductCard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_Icons__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Icons */ \"(app-pages-browser)/./src/components/Icons.tsx\");\n/* harmony import */ var _context_UserContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/context/UserContext */ \"(app-pages-browser)/./src/context/UserContext.tsx\");\n/* harmony import */ var _Message__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Message */ \"(app-pages-browser)/./src/components/Message.tsx\");\n/* harmony import */ var _VerificationBadge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./VerificationBadge */ \"(app-pages-browser)/./src/components/VerificationBadge.tsx\");\n/* harmony import */ var _ReportButton__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ReportButton */ \"(app-pages-browser)/./src/components/ReportButton.tsx\");\n/* harmony import */ var _LocationDisplay__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./LocationDisplay */ \"(app-pages-browser)/./src/components/LocationDisplay.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction ProductCard(param) {\n    let { product, showDistance = false } = param;\n    _s();\n    const { addToWishlist, removeFromWishlist, isInWishlist } = (0,_context_UserContext__WEBPACK_IMPORTED_MODULE_4__.useUser)();\n    const [isHovered, setIsHovered] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleWishlistClick = (e)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        if (isInWishlist(product.id)) {\n            removeFromWishlist(product.id);\n        } else {\n            addToWishlist(product);\n        }\n    };\n    const handleReport = (data)=>{\n        // Here we would typically send the report to a backend\n        console.log(\"Report submitted:\", data);\n    };\n    const getAvailabilityColor = (status)=>{\n        switch(status){\n            case \"Available\":\n                return \"text-green-600\";\n            case \"Sold\":\n                return \"text-red-600\";\n            case \"Under Contract\":\n                return \"text-yellow-600\";\n            default:\n                return \"text-slate-600\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 overflow-hidden\",\n        onMouseEnter: ()=>setIsHovered(true),\n        onMouseLeave: ()=>setIsHovered(false),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                href: \"/products/\".concat(product.id),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: product.image,\n                                alt: product.name,\n                                className: \"w-full h-48 object-cover\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleWishlistClick,\n                                className: \"absolute top-2 right-2 p-2 rounded-full transition-colors \".concat(isInWishlist(product.id) ? \"bg-red-500 text-white\" : \"bg-white/80 text-slate-600 hover:bg-white\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Icons__WEBPACK_IMPORTED_MODULE_3__.HeartIcon, {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 11\n                            }, this),\n                            product.availability && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-2 left-2 px-2 py-1 rounded-full text-sm font-medium bg-white/90 \".concat(getAvailabilityColor(product.availability)),\n                                children: product.availability\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"font-semibold text-lg mb-1\",\n                                children: product.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-slate-600 mb-2\",\n                                children: product.price\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, this),\n                            product.location && product.location.latitude && product.location.longitude ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LocationDisplay__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                location: product.location,\n                                showDistance: showDistance,\n                                className: \"mb-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 13\n                            }, this) : product.location && typeof product.location === \"string\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Icons__WEBPACK_IMPORTED_MODULE_3__.LocationIcon, {\n                                        className: \"w-4 h-4 text-slate-500 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-slate-500\",\n                                        children: product.location\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 13\n                            }, this) : null,\n                            product.seller && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-slate-600\",\n                                            children: product.seller.name\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_VerificationBadge__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            status: product.seller.verificationStatus || \"Unverified\",\n                                            size: \"sm\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 pb-4 flex items-center justify-between\",\n                children: [\n                    product.seller && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Message__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        productId: product.id,\n                        sellerId: product.seller.id,\n                        productName: product.name,\n                        sellerName: product.seller.name\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ReportButton__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        itemId: product.id,\n                        itemType: \"product\",\n                        onReport: handleReport\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                lineNumber: 109,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n        lineNumber: 56,\n        columnNumber: 5\n    }, this);\n}\n_s(ProductCard, \"fmUKcEfUJ8CcATBAvqdC0qcB1Do=\", false, function() {\n    return [\n        _context_UserContext__WEBPACK_IMPORTED_MODULE_4__.useUser\n    ];\n});\n_c = ProductCard;\nvar _c;\n$RefreshReg$(_c, \"ProductCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ProductCard.tsx\n"));

/***/ })

});