"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/products/page",{

/***/ "(app-pages-browser)/./src/app/products/page.tsx":
/*!***********************************!*\
  !*** ./src/app/products/page.tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ProductsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Header */ \"(app-pages-browser)/./src/components/Header.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Footer */ \"(app-pages-browser)/./src/components/Footer.tsx\");\n/* harmony import */ var _components_ProductCard__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ProductCard */ \"(app-pages-browser)/./src/components/ProductCard.tsx\");\n/* harmony import */ var _components_FilterSidebar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/FilterSidebar */ \"(app-pages-browser)/./src/components/FilterSidebar.tsx\");\n/* harmony import */ var _lib_data__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/data */ \"(app-pages-browser)/./src/lib/data.ts\");\n/* harmony import */ var _barrel_optimize_names_HiFilter_HiViewGrid_HiViewList_react_icons_hi__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=HiFilter,HiViewGrid,HiViewList!=!react-icons/hi */ \"(app-pages-browser)/./node_modules/react-icons/hi/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction ProductsPage() {\n    _s();\n    const [cart, setCart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"grid\");\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showMobileFilters, setShowMobileFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        category: \"all\",\n        condition: \"all\",\n        location: \"all\",\n        priceRange: [\n            0,\n            1000000\n        ],\n        sortBy: \"newest\",\n        attributes: {}\n    });\n    // Check if we're on mobile\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleResize = ()=>{\n            setIsMobile(window.innerWidth < 768);\n        };\n        handleResize();\n        window.addEventListener(\"resize\", handleResize);\n        return ()=>window.removeEventListener(\"resize\", handleResize);\n    }, []);\n    const parsePrice = (price)=>{\n        return parseInt(price.replace(/[^0-9]/g, \"\"));\n    };\n    const filteredProducts = _lib_data__WEBPACK_IMPORTED_MODULE_6__.products.filter((product)=>{\n        const matchesSearch = product.name.toLowerCase().includes(searchQuery.toLowerCase());\n        const matchesCategory = filters.category === \"all\" || product.category === filters.category;\n        const matchesCondition = filters.condition === \"all\" || product.condition === filters.condition;\n        const matchesLocation = filters.location === \"all\" || product.location === filters.location;\n        const productPrice = parsePrice(product.price);\n        const matchesPrice = productPrice >= filters.priceRange[0] && productPrice <= filters.priceRange[1];\n        return matchesSearch && matchesCategory && matchesCondition && matchesLocation && matchesPrice;\n    }).sort((a, b)=>{\n        const priceA = parsePrice(a.price);\n        const priceB = parsePrice(b.price);\n        switch(filters.sortBy){\n            case \"price_low\":\n                return priceA - priceB;\n            case \"price_high\":\n                return priceB - priceA;\n            case \"oldest\":\n                return Number(a.id) - Number(b.id);\n            case \"popular\":\n                return Number(b.id) - Number(a.id);\n            case \"newest\":\n            default:\n                return Number(b.id) - Number(a.id);\n        }\n    });\n    // Define filter options\n    const conditionOptions = [\n        {\n            id: \"all\",\n            name: \"All Conditions\"\n        },\n        {\n            id: \"new\",\n            name: \"New\"\n        },\n        {\n            id: \"used\",\n            name: \"Used\"\n        },\n        {\n            id: \"refurbished\",\n            name: \"Refurbished\"\n        }\n    ];\n    const locationOptions = [\n        {\n            id: \"all\",\n            name: \"All Locations\"\n        },\n        {\n            id: \"lagos\",\n            name: \"Lagos\"\n        },\n        {\n            id: \"abuja\",\n            name: \"Abuja\"\n        },\n        {\n            id: \"port-harcourt\",\n            name: \"Port Harcourt\"\n        }\n    ];\n    const categoryOptions = [\n        {\n            id: \"all\",\n            name: \"All Categories\"\n        },\n        ..._lib_data__WEBPACK_IMPORTED_MODULE_6__.categories.map((cat)=>({\n                id: cat.slug,\n                name: cat.name\n            }))\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                searchQuery: searchQuery,\n                setSearchQuery: setSearchQuery,\n                cart: cart,\n                isMenuOpen: isMenuOpen,\n                setIsMenuOpen: setIsMenuOpen\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"container mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold mb-2\",\n                                children: \"All Products\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-slate-600\",\n                                children: [\n                                    _lib_data__WEBPACK_IMPORTED_MODULE_6__.products.length,\n                                    \" items available\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4\",\n                                children: [\n                                    isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowMobileFilters(!showMobileFilters),\n                                        className: \"flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiFilter_HiViewGrid_HiViewList_react_icons_hi__WEBPACK_IMPORTED_MODULE_7__.HiFilter, {\n                                                className: \"h-5 w-5 text-gray-500\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                                                lineNumber: 115,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-gray-700\",\n                                                children: \"Filters\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                                                lineNumber: 116,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center border border-gray-300 rounded-lg overflow-hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setViewMode(\"grid\"),\n                                                className: \"p-2 \".concat(viewMode === \"grid\" ? \"bg-indigo-100 text-indigo-600\" : \"bg-white text-gray-500\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiFilter_HiViewGrid_HiViewList_react_icons_hi__WEBPACK_IMPORTED_MODULE_7__.HiViewGrid, {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                                                    lineNumber: 127,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setViewMode(\"list\"),\n                                                className: \"p-2 \".concat(viewMode === \"list\" ? \"bg-indigo-100 text-indigo-600\" : \"bg-white text-gray-500\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiFilter_HiViewGrid_HiViewList_react_icons_hi__WEBPACK_IMPORTED_MODULE_7__.HiViewList, {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                                                    lineNumber: 135,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-500\",\n                                children: [\n                                    \"Showing \",\n                                    filteredProducts.length,\n                                    \" of \",\n                                    _lib_data__WEBPACK_IMPORTED_MODULE_6__.products.length,\n                                    \" products\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"\".concat(isMobile && !showMobileFilters ? \"hidden\" : \"block\", \" md:block md:w-64 flex-shrink-0\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FilterSidebar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    categories: categoryOptions,\n                                    conditions: conditionOptions,\n                                    locations: locationOptions,\n                                    priceRange: {\n                                        min: 0,\n                                        max: 1000000\n                                    },\n                                    onFilterChange: (newFilters)=>setFilters({\n                                            ...filters,\n                                            ...newFilters\n                                        }),\n                                    isMobile: isMobile\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                        className: \"mb-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-xl font-semibold mb-4\",\n                                                children: \"Browse by Category\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4\",\n                                                children: _lib_data__WEBPACK_IMPORTED_MODULE_6__.categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"/categories/\".concat(category.slug),\n                                                        className: \"group p-4 bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-slate-100 text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-3xl mb-2 group-hover:scale-110 transition-transform\",\n                                                                children: category.icon\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                                                                lineNumber: 170,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-medium text-sm\",\n                                                                children: category.name\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                                                                lineNumber: 171,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-slate-500 mt-1\",\n                                                                children: [\n                                                                    category.count,\n                                                                    \" items\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                                                                lineNumber: 172,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, category.id, true, {\n                                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                                                        lineNumber: 165,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 13\n                                    }, this),\n                                    filteredProducts.length > 0 ? viewMode === \"grid\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6\",\n                                        children: filteredProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProductCard__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                product: product\n                                            }, product.id, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: filteredProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col md:flex-row bg-white rounded-lg shadow-sm overflow-hidden\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"md:w-1/4 h-48 md:h-auto\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: product.image,\n                                                            alt: product.name,\n                                                            className: \"w-full h-full object-cover\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                                                            lineNumber: 194,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                                                        lineNumber: 193,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 p-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold mb-2\",\n                                                                children: product.name\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                                                                lineNumber: 201,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600 mb-4\",\n                                                                children: product.price\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                                                                lineNumber: 202,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-end\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>setCart([\n                                                                                ...cart,\n                                                                                product\n                                                                            ]),\n                                                                        className: \"px-4 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700 transition\",\n                                                                        children: \"Add to Cart\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                                                                        lineNumber: 204,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: product.condition && \"Condition: \".concat(product.condition)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                                                                        lineNumber: 210,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                                                                lineNumber: 203,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                                                        lineNumber: 200,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, product.id, true, {\n                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                                                lineNumber: 192,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-12\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xl text-slate-600\",\n                                            children: \"No products found matching your criteria\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                                            lineNumber: 221,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                lineNumber: 102,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                categories: _lib_data__WEBPACK_IMPORTED_MODULE_6__.categories\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                lineNumber: 227,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n        lineNumber: 94,\n        columnNumber: 5\n    }, this);\n}\n_s(ProductsPage, \"DWAGjZdoq9ssEgmIkQg+pH7TK60=\");\n_c = ProductsPage;\nvar _c;\n$RefreshReg$(_c, \"ProductsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/products/page.tsx\n"));

/***/ })

});