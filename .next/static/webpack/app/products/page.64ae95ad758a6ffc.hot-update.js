"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/products/page",{

/***/ "(app-pages-browser)/./src/app/products/page.tsx":
/*!***********************************!*\
  !*** ./src/app/products/page.tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ProductsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Header */ \"(app-pages-browser)/./src/components/Header.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Footer */ \"(app-pages-browser)/./src/components/Footer.tsx\");\n/* harmony import */ var _components_ProductCard__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ProductCard */ \"(app-pages-browser)/./src/components/ProductCard.tsx\");\n/* harmony import */ var _components_FilterSidebar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/FilterSidebar */ \"(app-pages-browser)/./src/components/FilterSidebar.tsx\");\n/* harmony import */ var _lib_data__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/data */ \"(app-pages-browser)/./src/lib/data.ts\");\n/* harmony import */ var _barrel_optimize_names_HiFilter_HiViewGrid_HiViewList_react_icons_hi__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=HiFilter,HiViewGrid,HiViewList!=!react-icons/hi */ \"(app-pages-browser)/./node_modules/react-icons/hi/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction ProductsPage() {\n    _s();\n    const [cart, setCart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"grid\");\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showMobileFilters, setShowMobileFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        category: \"all\",\n        condition: \"all\",\n        location: \"all\",\n        priceRange: [\n            0,\n            1000000\n        ],\n        sortBy: \"newest\",\n        attributes: {}\n    });\n    // Check if we're on mobile\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleResize = ()=>{\n            setIsMobile(window.innerWidth < 768);\n        };\n        handleResize();\n        window.addEventListener(\"resize\", handleResize);\n        return ()=>window.removeEventListener(\"resize\", handleResize);\n    }, []);\n    const parsePrice = (price)=>{\n        return parseInt(price.replace(/[^0-9]/g, \"\"));\n    };\n    const filteredProducts = _lib_data__WEBPACK_IMPORTED_MODULE_6__.products.filter((product)=>{\n        const matchesSearch = product.name.toLowerCase().includes(searchQuery.toLowerCase());\n        const matchesCategory = filters.category === \"all\" || product.category === filters.category;\n        const matchesCondition = filters.condition === \"all\" || product.condition === filters.condition;\n        const matchesLocation = filters.location === \"all\" || product.location === filters.location;\n        const productPrice = parsePrice(product.price);\n        const matchesPrice = productPrice >= filters.priceRange[0] && productPrice <= filters.priceRange[1];\n        return matchesSearch && matchesCategory && matchesCondition && matchesLocation && matchesPrice;\n    }).sort((a, b)=>{\n        const priceA = parsePrice(a.price);\n        const priceB = parsePrice(b.price);\n        switch(filters.sortBy){\n            case \"price_low\":\n                return priceA - priceB;\n            case \"price_high\":\n                return priceB - priceA;\n            case \"oldest\":\n                return a.id - b.id;\n            case \"popular\":\n                // This would ideally use a popularity metric, but for now we'll use ID as a proxy\n                return b.id - a.id;\n            case \"newest\":\n            default:\n                return b.id - a.id;\n        }\n    });\n    // Define filter options\n    const conditionOptions = [\n        {\n            id: \"all\",\n            name: \"All Conditions\"\n        },\n        {\n            id: \"new\",\n            name: \"New\"\n        },\n        {\n            id: \"used\",\n            name: \"Used\"\n        },\n        {\n            id: \"refurbished\",\n            name: \"Refurbished\"\n        }\n    ];\n    const locationOptions = [\n        {\n            id: \"all\",\n            name: \"All Locations\"\n        },\n        {\n            id: \"lagos\",\n            name: \"Lagos\"\n        },\n        {\n            id: \"abuja\",\n            name: \"Abuja\"\n        },\n        {\n            id: \"port-harcourt\",\n            name: \"Port Harcourt\"\n        }\n    ];\n    const categoryOptions = [\n        {\n            id: \"all\",\n            name: \"All Categories\"\n        },\n        ..._lib_data__WEBPACK_IMPORTED_MODULE_6__.categories.map((cat)=>({\n                id: cat.slug,\n                name: cat.name\n            }))\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                searchQuery: searchQuery,\n                setSearchQuery: setSearchQuery,\n                cart: cart,\n                isMenuOpen: isMenuOpen,\n                setIsMenuOpen: setIsMenuOpen\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"container mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold mb-2\",\n                                children: \"All Products\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-slate-600\",\n                                children: [\n                                    _lib_data__WEBPACK_IMPORTED_MODULE_6__.products.length,\n                                    \" items available\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4\",\n                                children: [\n                                    isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowMobileFilters(!showMobileFilters),\n                                        className: \"flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiFilter_HiViewGrid_HiViewList_react_icons_hi__WEBPACK_IMPORTED_MODULE_7__.HiFilter, {\n                                                className: \"h-5 w-5 text-gray-500\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                                                lineNumber: 116,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-gray-700\",\n                                                children: \"Filters\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center border border-gray-300 rounded-lg overflow-hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setViewMode(\"grid\"),\n                                                className: \"p-2 \".concat(viewMode === \"grid\" ? \"bg-indigo-100 text-indigo-600\" : \"bg-white text-gray-500\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiFilter_HiViewGrid_HiViewList_react_icons_hi__WEBPACK_IMPORTED_MODULE_7__.HiViewGrid, {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                                                    lineNumber: 128,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setViewMode(\"list\"),\n                                                className: \"p-2 \".concat(viewMode === \"list\" ? \"bg-indigo-100 text-indigo-600\" : \"bg-white text-gray-500\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiFilter_HiViewGrid_HiViewList_react_icons_hi__WEBPACK_IMPORTED_MODULE_7__.HiViewList, {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                                                    lineNumber: 136,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                                                lineNumber: 130,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                                lineNumber: 110,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-500\",\n                                children: [\n                                    \"Showing \",\n                                    filteredProducts.length,\n                                    \" of \",\n                                    _lib_data__WEBPACK_IMPORTED_MODULE_6__.products.length,\n                                    \" products\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"\".concat(isMobile && !showMobileFilters ? \"hidden\" : \"block\", \" md:block md:w-64 flex-shrink-0\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FilterSidebar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    categories: categoryOptions,\n                                    conditions: conditionOptions,\n                                    locations: locationOptions,\n                                    priceRange: {\n                                        min: 0,\n                                        max: 1000000\n                                    },\n                                    onFilterChange: (newFilters)=>setFilters({\n                                            ...filters,\n                                            ...newFilters\n                                        }),\n                                    isMobile: isMobile\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                        className: \"mb-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-xl font-semibold mb-4\",\n                                                children: \"Browse by Category\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4\",\n                                                children: _lib_data__WEBPACK_IMPORTED_MODULE_6__.categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"/categories/\".concat(category.slug),\n                                                        className: \"group p-4 bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-slate-100 text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-3xl mb-2 group-hover:scale-110 transition-transform\",\n                                                                children: category.icon\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                                                                lineNumber: 171,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-medium text-sm\",\n                                                                children: category.name\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                                                                lineNumber: 172,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-slate-500 mt-1\",\n                                                                children: [\n                                                                    category.count,\n                                                                    \" items\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                                                                lineNumber: 173,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, category.id, true, {\n                                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                                                        lineNumber: 166,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 13\n                                    }, this),\n                                    filteredProducts.length > 0 ? viewMode === \"grid\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6\",\n                                        children: filteredProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProductCard__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                product: product\n                                            }, product.id, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: filteredProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col md:flex-row bg-white rounded-lg shadow-sm overflow-hidden\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"md:w-1/4 h-48 md:h-auto\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: product.image,\n                                                            alt: product.name,\n                                                            className: \"w-full h-full object-cover\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                                                            lineNumber: 195,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                                                        lineNumber: 194,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 p-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold mb-2\",\n                                                                children: product.name\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                                                                lineNumber: 202,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600 mb-4\",\n                                                                children: product.price\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                                                                lineNumber: 203,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-end\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>setCart([\n                                                                                ...cart,\n                                                                                product\n                                                                            ]),\n                                                                        className: \"px-4 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700 transition\",\n                                                                        children: \"Add to Cart\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                                                                        lineNumber: 205,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: product.condition && \"Condition: \".concat(product.condition)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                                                                        lineNumber: 211,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                                                                lineNumber: 204,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                                                        lineNumber: 201,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, product.id, true, {\n                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-12\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xl text-slate-600\",\n                                            children: \"No products found matching your criteria\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                                            lineNumber: 222,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                lineNumber: 103,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                categories: _lib_data__WEBPACK_IMPORTED_MODULE_6__.categories\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n                lineNumber: 228,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/page.tsx\",\n        lineNumber: 95,\n        columnNumber: 5\n    }, this);\n}\n_s(ProductsPage, \"DWAGjZdoq9ssEgmIkQg+pH7TK60=\");\n_c = ProductsPage;\nvar _c;\n$RefreshReg$(_c, \"ProductsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/products/page.tsx\n"));

/***/ })

});