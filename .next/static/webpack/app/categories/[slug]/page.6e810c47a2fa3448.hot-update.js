"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/categories/[slug]/page",{

/***/ "(app-pages-browser)/./src/components/ProductCard.tsx":
/*!****************************************!*\
  !*** ./src/components/ProductCard.tsx ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ProductCard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_Icons__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Icons */ \"(app-pages-browser)/./src/components/Icons.tsx\");\n/* harmony import */ var _context_UserContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/context/UserContext */ \"(app-pages-browser)/./src/context/UserContext.tsx\");\n/* harmony import */ var _Message__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Message */ \"(app-pages-browser)/./src/components/Message.tsx\");\n/* harmony import */ var _VerificationBadge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./VerificationBadge */ \"(app-pages-browser)/./src/components/VerificationBadge.tsx\");\n/* harmony import */ var _ReportButton__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ReportButton */ \"(app-pages-browser)/./src/components/ReportButton.tsx\");\n/* harmony import */ var _LocationDisplay__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./LocationDisplay */ \"(app-pages-browser)/./src/components/LocationDisplay.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction ProductCard(param) {\n    let { product, showDistance = false } = param;\n    _s();\n    const { addToWishlist, removeFromWishlist, isInWishlist } = (0,_context_UserContext__WEBPACK_IMPORTED_MODULE_4__.useUser)();\n    const [isHovered, setIsHovered] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleWishlistClick = (e)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        if (isInWishlist(product.id)) {\n            removeFromWishlist(product.id);\n        } else {\n            addToWishlist(product);\n        }\n    };\n    const handleReport = (data)=>{\n        // Here we would typically send the report to a backend\n        console.log(\"Report submitted:\", data);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 overflow-hidden\",\n        onMouseEnter: ()=>setIsHovered(true),\n        onMouseLeave: ()=>setIsHovered(false),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                href: \"/products/\".concat(product.id),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: \"/placeholder.png\",\n                                alt: product.name,\n                                className: \"w-full h-48 object-cover\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                                lineNumber: 50,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleWishlistClick,\n                                className: \"absolute top-2 right-2 p-2 rounded-full transition-colors \".concat(isInWishlist(product.id) ? \"bg-red-500 text-white\" : \"bg-white/80 text-slate-600 hover:bg-white\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Icons__WEBPACK_IMPORTED_MODULE_3__.HeartIcon, {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"font-semibold text-lg mb-1\",\n                                children: product.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-slate-600 mb-2\",\n                                children: [\n                                    \"$\",\n                                    product.price.toFixed(2)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 11\n                            }, this),\n                            product.location && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LocationDisplay__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                location: product.location,\n                                showDistance: showDistance,\n                                className: \"mb-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 13\n                            }, this),\n                            product.seller && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-slate-600\",\n                                            children: product.seller.name\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_VerificationBadge__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            status: product.seller.verificationStatus,\n                                            size: \"sm\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 pb-4 flex items-center justify-between\",\n                children: [\n                    product.seller && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Message__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        productId: product.id,\n                        sellerId: product.seller.id,\n                        productName: product.name,\n                        sellerName: product.seller.name || \"\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ReportButton__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        itemId: product.id,\n                        itemType: \"product\",\n                        onReport: handleReport\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                lineNumber: 91,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\n_s(ProductCard, \"fmUKcEfUJ8CcATBAvqdC0qcB1Do=\", false, function() {\n    return [\n        _context_UserContext__WEBPACK_IMPORTED_MODULE_4__.useUser\n    ];\n});\n_c = ProductCard;\nvar _c;\n$RefreshReg$(_c, \"ProductCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ProductCard.tsx\n"));

/***/ })

});