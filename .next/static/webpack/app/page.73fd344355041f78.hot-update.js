"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/ProductCard.tsx":
/*!****************************************!*\
  !*** ./src/components/ProductCard.tsx ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ProductCard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_Icons__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Icons */ \"(app-pages-browser)/./src/components/Icons.tsx\");\n/* harmony import */ var _context_UserContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/context/UserContext */ \"(app-pages-browser)/./src/context/UserContext.tsx\");\n/* harmony import */ var _Message__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Message */ \"(app-pages-browser)/./src/components/Message.tsx\");\n/* harmony import */ var _VerificationBadge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./VerificationBadge */ \"(app-pages-browser)/./src/components/VerificationBadge.tsx\");\n/* harmony import */ var _ReportButton__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ReportButton */ \"(app-pages-browser)/./src/components/ReportButton.tsx\");\n/* harmony import */ var _LocationDisplay__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./LocationDisplay */ \"(app-pages-browser)/./src/components/LocationDisplay.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction ProductCard(param) {\n    let { product, showDistance = false } = param;\n    _s();\n    const { addToWishlist, removeFromWishlist, isInWishlist } = (0,_context_UserContext__WEBPACK_IMPORTED_MODULE_4__.useUser)();\n    const [isHovered, setIsHovered] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleWishlistClick = (e)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        if (isInWishlist(product.id)) {\n            removeFromWishlist(product.id);\n        } else {\n            addToWishlist(product);\n        }\n    };\n    const handleReport = (data)=>{\n        // Here we would typically send the report to a backend\n        console.log(\"Report submitted:\", data);\n    };\n    const getAvailabilityColor = (status)=>{\n        switch(status){\n            case \"Available\":\n                return \"text-green-600\";\n            case \"Sold\":\n                return \"text-red-600\";\n            case \"Under Contract\":\n                return \"text-yellow-600\";\n            default:\n                return \"text-slate-600\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 overflow-hidden\",\n        onMouseEnter: ()=>setIsHovered(true),\n        onMouseLeave: ()=>setIsHovered(false),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                href: \"/products/\".concat(product.id),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: product.image,\n                                alt: product.name,\n                                className: \"w-full h-48 object-cover\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleWishlistClick,\n                                className: \"absolute top-2 right-2 p-2 rounded-full transition-colors \".concat(isInWishlist(product.id) ? \"bg-red-500 text-white\" : \"bg-white/80 text-slate-600 hover:bg-white\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Icons__WEBPACK_IMPORTED_MODULE_3__.HeartIcon, {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 11\n                            }, this),\n                            product.availability && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-2 left-2 px-2 py-1 rounded-full text-sm font-medium bg-white/90 \".concat(getAvailabilityColor(product.availability)),\n                                children: product.availability\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"font-semibold text-lg mb-1\",\n                                children: product.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-slate-600 mb-2\",\n                                children: product.price\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, this),\n                            product.location && product.location.latitude && product.location.longitude ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LocationDisplay__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                location: product.location,\n                                showDistance: showDistance,\n                                className: \"mb-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 13\n                            }, this) : product.location && typeof product.location === \"string\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Icons__WEBPACK_IMPORTED_MODULE_3__.LocationIcon, {\n                                        className: \"w-4 h-4 text-slate-500 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-slate-500\",\n                                        children: product.location\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 13\n                            }, this) : null,\n                            product.seller && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-slate-600\",\n                                            children: product.seller.name\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_VerificationBadge__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            status: product.seller.verificationStatus || \"Unverified\",\n                                            size: \"sm\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 pb-4 flex items-center justify-between\",\n                children: [\n                    product.seller && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Message__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        productId: product.id,\n                        sellerId: product.seller.id,\n                        productName: product.name,\n                        sellerName: product.seller.name\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ReportButton__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        itemId: product.id,\n                        itemType: \"product\",\n                        onReport: handleReport\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                lineNumber: 109,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n        lineNumber: 56,\n        columnNumber: 5\n    }, this);\n}\n_s(ProductCard, \"fmUKcEfUJ8CcATBAvqdC0qcB1Do=\", false, function() {\n    return [\n        _context_UserContext__WEBPACK_IMPORTED_MODULE_4__.useUser\n    ];\n});\n_c = ProductCard;\nvar _c;\n$RefreshReg$(_c, \"ProductCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL1Byb2R1Y3RDYXJkLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7O0FBRWlDO0FBQ0o7QUFFZ0M7QUFDYjtBQUNoQjtBQUNvQjtBQUNWO0FBQ007QUFPakMsU0FBU1MsWUFBWSxLQUFtRDtRQUFuRCxFQUFFQyxPQUFPLEVBQUVDLGVBQWUsS0FBSyxFQUFvQixHQUFuRDs7SUFDbEMsTUFBTSxFQUFFQyxhQUFhLEVBQUVDLGtCQUFrQixFQUFFQyxZQUFZLEVBQUUsR0FBR1YsNkRBQU9BO0lBQ25FLE1BQU0sQ0FBQ1csV0FBV0MsYUFBYSxHQUFHaEIsK0NBQVFBLENBQUM7SUFFM0MsTUFBTWlCLHNCQUFzQixDQUFDQztRQUMzQkEsRUFBRUMsY0FBYztRQUNoQkQsRUFBRUUsZUFBZTtRQUNqQixJQUFJTixhQUFhSixRQUFRVyxFQUFFLEdBQUc7WUFDNUJSLG1CQUFtQkgsUUFBUVcsRUFBRTtRQUMvQixPQUFPO1lBQ0xULGNBQWNGO1FBQ2hCO0lBQ0Y7SUFFQSxNQUFNWSxlQUFlLENBQUNDO1FBTXBCLHVEQUF1RDtRQUN2REMsUUFBUUMsR0FBRyxDQUFDLHFCQUFxQkY7SUFDbkM7SUFFQSxNQUFNRyx1QkFBdUIsQ0FBQ0M7UUFDNUIsT0FBUUE7WUFDTixLQUFLO2dCQUNILE9BQU87WUFDVCxLQUFLO2dCQUNILE9BQU87WUFDVCxLQUFLO2dCQUNILE9BQU87WUFDVDtnQkFDRSxPQUFPO1FBQ1g7SUFDRjtJQUVBLHFCQUNFLDhEQUFDQztRQUNDQyxXQUFVO1FBQ1ZDLGNBQWMsSUFBTWQsYUFBYTtRQUNqQ2UsY0FBYyxJQUFNZixhQUFhOzswQkFFakMsOERBQUNmLGlEQUFJQTtnQkFBQytCLE1BQU0sYUFBd0IsT0FBWHRCLFFBQVFXLEVBQUU7O2tDQUNqQyw4REFBQ087d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDSTtnQ0FDQ0MsS0FBS3hCLFFBQVF5QixLQUFLO2dDQUNsQkMsS0FBSzFCLFFBQVEyQixJQUFJO2dDQUNqQlIsV0FBVTs7Ozs7OzBDQUVaLDhEQUFDUztnQ0FDQ0MsU0FBU3RCO2dDQUNUWSxXQUFXLDZEQUlWLE9BSENmLGFBQWFKLFFBQVFXLEVBQUUsSUFDbkIsMEJBQ0E7MENBR04sNEVBQUNuQix3REFBU0E7b0NBQUMyQixXQUFVOzs7Ozs7Ozs7Ozs0QkFFdEJuQixRQUFROEIsWUFBWSxrQkFDbkIsOERBQUNaO2dDQUFJQyxXQUFXLG1GQUE4SCxPQUEzQ0gscUJBQXFCaEIsUUFBUThCLFlBQVk7MENBQ3pJOUIsUUFBUThCLFlBQVk7Ozs7Ozs7Ozs7OztrQ0FJM0IsOERBQUNaO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ1k7Z0NBQUdaLFdBQVU7MENBQThCbkIsUUFBUTJCLElBQUk7Ozs7OzswQ0FDeEQsOERBQUNLO2dDQUFFYixXQUFVOzBDQUF1Qm5CLFFBQVFpQyxLQUFLOzs7Ozs7NEJBQ2hEakMsUUFBUWtDLFFBQVEsSUFBSWxDLFFBQVFrQyxRQUFRLENBQUNDLFFBQVEsSUFBSW5DLFFBQVFrQyxRQUFRLENBQUNFLFNBQVMsaUJBQzFFLDhEQUFDdEMsd0RBQWVBO2dDQUNkb0MsVUFBVWxDLFFBQVFrQyxRQUFRO2dDQUMxQmpDLGNBQWNBO2dDQUNka0IsV0FBVTs7Ozs7dUNBRVZuQixRQUFRa0MsUUFBUSxJQUFJLE9BQU9sQyxRQUFRa0MsUUFBUSxLQUFLLHlCQUNsRCw4REFBQ2hCO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQzFCLDJEQUFZQTt3Q0FBQzBCLFdBQVU7Ozs7OztrREFDeEIsOERBQUNhO3dDQUFFYixXQUFVO2tEQUEwQm5CLFFBQVFrQyxRQUFROzs7Ozs7Ozs7Ozt1Q0FFdkQ7NEJBQ0hsQyxRQUFRcUMsTUFBTSxrQkFDYiw4REFBQ25CO2dDQUFJQyxXQUFVOzBDQUNiLDRFQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNtQjs0Q0FBS25CLFdBQVU7c0RBQTBCbkIsUUFBUXFDLE1BQU0sQ0FBQ1YsSUFBSTs7Ozs7O3NEQUM3RCw4REFBQy9CLDBEQUFpQkE7NENBQUNxQixRQUFRakIsUUFBUXFDLE1BQU0sQ0FBQ0Usa0JBQWtCLElBQUk7NENBQWNDLE1BQUs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU03Riw4REFBQ3RCO2dCQUFJQyxXQUFVOztvQkFDWm5CLFFBQVFxQyxNQUFNLGtCQUNiLDhEQUFDMUMsZ0RBQU9BO3dCQUNOOEMsV0FBV3pDLFFBQVFXLEVBQUU7d0JBQ3JCK0IsVUFBVTFDLFFBQVFxQyxNQUFNLENBQUMxQixFQUFFO3dCQUMzQmdDLGFBQWEzQyxRQUFRMkIsSUFBSTt3QkFDekJpQixZQUFZNUMsUUFBUXFDLE1BQU0sQ0FBQ1YsSUFBSTs7Ozs7O2tDQUduQyw4REFBQzlCLHFEQUFZQTt3QkFDWGdELFFBQVE3QyxRQUFRVyxFQUFFO3dCQUNsQm1DLFVBQVM7d0JBQ1RDLFVBQVVuQzs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBS3BCO0dBNUd3QmI7O1FBQ3NDTCx5REFBT0E7OztLQUQ3Q0siLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2NvbXBvbmVudHMvUHJvZHVjdENhcmQudHN4PzYwYjQiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCBMaW5rIGZyb20gJ25leHQvbGluayc7XG5pbXBvcnQgeyBEYlByb2R1Y3QgfSBmcm9tICdAL3R5cGVzJztcbmltcG9ydCB7IEhlYXJ0SWNvbiwgTG9jYXRpb25JY29uIH0gZnJvbSAnQC9jb21wb25lbnRzL0ljb25zJztcbmltcG9ydCB7IHVzZVVzZXIgfSBmcm9tICdAL2NvbnRleHQvVXNlckNvbnRleHQnO1xuaW1wb3J0IE1lc3NhZ2UgZnJvbSAnLi9NZXNzYWdlJztcbmltcG9ydCBWZXJpZmljYXRpb25CYWRnZSBmcm9tICcuL1ZlcmlmaWNhdGlvbkJhZGdlJztcbmltcG9ydCBSZXBvcnRCdXR0b24gZnJvbSAnLi9SZXBvcnRCdXR0b24nO1xuaW1wb3J0IExvY2F0aW9uRGlzcGxheSBmcm9tICcuL0xvY2F0aW9uRGlzcGxheSc7XG5cbmludGVyZmFjZSBQcm9kdWN0Q2FyZFByb3BzIHtcbiAgcHJvZHVjdDogRGJQcm9kdWN0O1xuICBzaG93RGlzdGFuY2U/OiBib29sZWFuO1xufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBQcm9kdWN0Q2FyZCh7IHByb2R1Y3QsIHNob3dEaXN0YW5jZSA9IGZhbHNlIH06IFByb2R1Y3RDYXJkUHJvcHMpIHtcbiAgY29uc3QgeyBhZGRUb1dpc2hsaXN0LCByZW1vdmVGcm9tV2lzaGxpc3QsIGlzSW5XaXNobGlzdCB9ID0gdXNlVXNlcigpO1xuICBjb25zdCBbaXNIb3ZlcmVkLCBzZXRJc0hvdmVyZWRdID0gdXNlU3RhdGUoZmFsc2UpO1xuXG4gIGNvbnN0IGhhbmRsZVdpc2hsaXN0Q2xpY2sgPSAoZTogUmVhY3QuTW91c2VFdmVudCkgPT4ge1xuICAgIGUucHJldmVudERlZmF1bHQoKTtcbiAgICBlLnN0b3BQcm9wYWdhdGlvbigpO1xuICAgIGlmIChpc0luV2lzaGxpc3QocHJvZHVjdC5pZCkpIHtcbiAgICAgIHJlbW92ZUZyb21XaXNobGlzdChwcm9kdWN0LmlkKTtcbiAgICB9IGVsc2Uge1xuICAgICAgYWRkVG9XaXNobGlzdChwcm9kdWN0KTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlUmVwb3J0ID0gKGRhdGE6IHtcbiAgICBpdGVtSWQ6IG51bWJlcjtcbiAgICBpdGVtVHlwZTogc3RyaW5nO1xuICAgIHJlYXNvbjogc3RyaW5nO1xuICAgIGRldGFpbHM6IHN0cmluZztcbiAgfSkgPT4ge1xuICAgIC8vIEhlcmUgd2Ugd291bGQgdHlwaWNhbGx5IHNlbmQgdGhlIHJlcG9ydCB0byBhIGJhY2tlbmRcbiAgICBjb25zb2xlLmxvZygnUmVwb3J0IHN1Ym1pdHRlZDonLCBkYXRhKTtcbiAgfTtcblxuICBjb25zdCBnZXRBdmFpbGFiaWxpdHlDb2xvciA9IChzdGF0dXM/OiBzdHJpbmcpID0+IHtcbiAgICBzd2l0Y2ggKHN0YXR1cykge1xuICAgICAgY2FzZSAnQXZhaWxhYmxlJzpcbiAgICAgICAgcmV0dXJuICd0ZXh0LWdyZWVuLTYwMCc7XG4gICAgICBjYXNlICdTb2xkJzpcbiAgICAgICAgcmV0dXJuICd0ZXh0LXJlZC02MDAnO1xuICAgICAgY2FzZSAnVW5kZXIgQ29udHJhY3QnOlxuICAgICAgICByZXR1cm4gJ3RleHQteWVsbG93LTYwMCc7XG4gICAgICBkZWZhdWx0OlxuICAgICAgICByZXR1cm4gJ3RleHQtc2xhdGUtNjAwJztcbiAgICB9XG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2XG4gICAgICBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLXhsIHNoYWRvdy1zbSBob3ZlcjpzaGFkb3ctbWQgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwIG92ZXJmbG93LWhpZGRlblwiXG4gICAgICBvbk1vdXNlRW50ZXI9eygpID0+IHNldElzSG92ZXJlZCh0cnVlKX1cbiAgICAgIG9uTW91c2VMZWF2ZT17KCkgPT4gc2V0SXNIb3ZlcmVkKGZhbHNlKX1cbiAgICA+XG4gICAgICA8TGluayBocmVmPXtgL3Byb2R1Y3RzLyR7cHJvZHVjdC5pZH1gfT5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiPlxuICAgICAgICAgIDxpbWdcbiAgICAgICAgICAgIHNyYz17cHJvZHVjdC5pbWFnZX1cbiAgICAgICAgICAgIGFsdD17cHJvZHVjdC5uYW1lfVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGgtNDggb2JqZWN0LWNvdmVyXCJcbiAgICAgICAgICAvPlxuICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZVdpc2hsaXN0Q2xpY2t9XG4gICAgICAgICAgICBjbGFzc05hbWU9e2BhYnNvbHV0ZSB0b3AtMiByaWdodC0yIHAtMiByb3VuZGVkLWZ1bGwgdHJhbnNpdGlvbi1jb2xvcnMgJHtcbiAgICAgICAgICAgICAgaXNJbldpc2hsaXN0KHByb2R1Y3QuaWQpXG4gICAgICAgICAgICAgICAgPyAnYmctcmVkLTUwMCB0ZXh0LXdoaXRlJ1xuICAgICAgICAgICAgICAgIDogJ2JnLXdoaXRlLzgwIHRleHQtc2xhdGUtNjAwIGhvdmVyOmJnLXdoaXRlJ1xuICAgICAgICAgICAgfWB9XG4gICAgICAgICAgPlxuICAgICAgICAgICAgPEhlYXJ0SWNvbiBjbGFzc05hbWU9XCJ3LTUgaC01XCIgLz5cbiAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICB7cHJvZHVjdC5hdmFpbGFiaWxpdHkgJiYgKFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2BhYnNvbHV0ZSBib3R0b20tMiBsZWZ0LTIgcHgtMiBweS0xIHJvdW5kZWQtZnVsbCB0ZXh0LXNtIGZvbnQtbWVkaXVtIGJnLXdoaXRlLzkwICR7Z2V0QXZhaWxhYmlsaXR5Q29sb3IocHJvZHVjdC5hdmFpbGFiaWxpdHkpfWB9PlxuICAgICAgICAgICAgICB7cHJvZHVjdC5hdmFpbGFiaWxpdHl9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApfVxuICAgICAgICA8L2Rpdj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTRcIj5cbiAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwiZm9udC1zZW1pYm9sZCB0ZXh0LWxnIG1iLTFcIj57cHJvZHVjdC5uYW1lfTwvaDM+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbGF0ZS02MDAgbWItMlwiPntwcm9kdWN0LnByaWNlfTwvcD5cbiAgICAgICAgICB7cHJvZHVjdC5sb2NhdGlvbiAmJiBwcm9kdWN0LmxvY2F0aW9uLmxhdGl0dWRlICYmIHByb2R1Y3QubG9jYXRpb24ubG9uZ2l0dWRlID8gKFxuICAgICAgICAgICAgPExvY2F0aW9uRGlzcGxheVxuICAgICAgICAgICAgICBsb2NhdGlvbj17cHJvZHVjdC5sb2NhdGlvbn1cbiAgICAgICAgICAgICAgc2hvd0Rpc3RhbmNlPXtzaG93RGlzdGFuY2V9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm1iLTJcIlxuICAgICAgICAgICAgLz5cbiAgICAgICAgICApIDogcHJvZHVjdC5sb2NhdGlvbiAmJiB0eXBlb2YgcHJvZHVjdC5sb2NhdGlvbiA9PT0gJ3N0cmluZycgPyAoXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIG1iLTJcIj5cbiAgICAgICAgICAgICAgPExvY2F0aW9uSWNvbiBjbGFzc05hbWU9XCJ3LTQgaC00IHRleHQtc2xhdGUtNTAwIG1yLTFcIiAvPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtc2xhdGUtNTAwXCI+e3Byb2R1Y3QubG9jYXRpb259PC9wPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKSA6IG51bGx9XG4gICAgICAgICAge3Byb2R1Y3Quc2VsbGVyICYmIChcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIG1iLTJcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1zbGF0ZS02MDBcIj57cHJvZHVjdC5zZWxsZXIubmFtZX08L3NwYW4+XG4gICAgICAgICAgICAgICAgPFZlcmlmaWNhdGlvbkJhZGdlIHN0YXR1cz17cHJvZHVjdC5zZWxsZXIudmVyaWZpY2F0aW9uU3RhdHVzIHx8ICdVbnZlcmlmaWVkJ30gc2l6ZT1cInNtXCIgLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApfVxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvTGluaz5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHgtNCBwYi00IGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICB7cHJvZHVjdC5zZWxsZXIgJiYgKFxuICAgICAgICAgIDxNZXNzYWdlXG4gICAgICAgICAgICBwcm9kdWN0SWQ9e3Byb2R1Y3QuaWR9XG4gICAgICAgICAgICBzZWxsZXJJZD17cHJvZHVjdC5zZWxsZXIuaWR9XG4gICAgICAgICAgICBwcm9kdWN0TmFtZT17cHJvZHVjdC5uYW1lfVxuICAgICAgICAgICAgc2VsbGVyTmFtZT17cHJvZHVjdC5zZWxsZXIubmFtZX1cbiAgICAgICAgICAvPlxuICAgICAgICApfVxuICAgICAgICA8UmVwb3J0QnV0dG9uXG4gICAgICAgICAgaXRlbUlkPXtwcm9kdWN0LmlkfVxuICAgICAgICAgIGl0ZW1UeXBlPVwicHJvZHVjdFwiXG4gICAgICAgICAgb25SZXBvcnQ9e2hhbmRsZVJlcG9ydH1cbiAgICAgICAgLz5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApO1xufSJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsIkxpbmsiLCJIZWFydEljb24iLCJMb2NhdGlvbkljb24iLCJ1c2VVc2VyIiwiTWVzc2FnZSIsIlZlcmlmaWNhdGlvbkJhZGdlIiwiUmVwb3J0QnV0dG9uIiwiTG9jYXRpb25EaXNwbGF5IiwiUHJvZHVjdENhcmQiLCJwcm9kdWN0Iiwic2hvd0Rpc3RhbmNlIiwiYWRkVG9XaXNobGlzdCIsInJlbW92ZUZyb21XaXNobGlzdCIsImlzSW5XaXNobGlzdCIsImlzSG92ZXJlZCIsInNldElzSG92ZXJlZCIsImhhbmRsZVdpc2hsaXN0Q2xpY2siLCJlIiwicHJldmVudERlZmF1bHQiLCJzdG9wUHJvcGFnYXRpb24iLCJpZCIsImhhbmRsZVJlcG9ydCIsImRhdGEiLCJjb25zb2xlIiwibG9nIiwiZ2V0QXZhaWxhYmlsaXR5Q29sb3IiLCJzdGF0dXMiLCJkaXYiLCJjbGFzc05hbWUiLCJvbk1vdXNlRW50ZXIiLCJvbk1vdXNlTGVhdmUiLCJocmVmIiwiaW1nIiwic3JjIiwiaW1hZ2UiLCJhbHQiLCJuYW1lIiwiYnV0dG9uIiwib25DbGljayIsImF2YWlsYWJpbGl0eSIsImgzIiwicCIsInByaWNlIiwibG9jYXRpb24iLCJsYXRpdHVkZSIsImxvbmdpdHVkZSIsInNlbGxlciIsInNwYW4iLCJ2ZXJpZmljYXRpb25TdGF0dXMiLCJzaXplIiwicHJvZHVjdElkIiwic2VsbGVySWQiLCJwcm9kdWN0TmFtZSIsInNlbGxlck5hbWUiLCJpdGVtSWQiLCJpdGVtVHlwZSIsIm9uUmVwb3J0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ProductCard.tsx\n"));

/***/ })

});