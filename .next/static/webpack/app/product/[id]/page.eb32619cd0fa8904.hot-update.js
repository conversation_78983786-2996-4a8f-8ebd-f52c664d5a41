"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/product/[id]/page",{

/***/ "(app-pages-browser)/./src/app/product/[id]/page.tsx":
/*!***************************************!*\
  !*** ./src/app/product/[id]/page.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ProductPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Header */ \"(app-pages-browser)/./src/components/Header.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Footer */ \"(app-pages-browser)/./src/components/Footer.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-auth */ \"(app-pages-browser)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _app_api_auth_nextauth_route__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/api/auth/[...nextauth]/route */ \"(app-pages-browser)/./src/app/api/auth/[...nextauth]/route.ts\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/prisma */ \"(app-pages-browser)/./src/lib/prisma.ts\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _components_StartChatButton__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/StartChatButton */ \"(app-pages-browser)/./src/components/StartChatButton.tsx\");\n/* harmony import */ var _components_FollowButtonClient__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/FollowButtonClient */ \"(app-pages-browser)/./src/components/FollowButtonClient.tsx\");\n/* harmony import */ var _components_ShareButtonClient__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ShareButtonClient */ \"(app-pages-browser)/./src/components/ShareButtonClient.tsx\");\n/* harmony import */ var _barrel_optimize_names_HiExclamationCircle_HiShieldCheck_react_icons_hi__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=HiExclamationCircle,HiShieldCheck!=!react-icons/hi */ \"(app-pages-browser)/./node_modules/react-icons/hi/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\n\nasync function getProduct(id) {\n    return _lib_prisma__WEBPACK_IMPORTED_MODULE_6__.prisma.product.findUnique({\n        where: {\n            id\n        },\n        include: {\n            seller: {\n                select: {\n                    id: true,\n                    name: true,\n                    image: true,\n                    rating: true,\n                    verificationStatus: true\n                }\n            },\n            category: true,\n            reviews: {\n                include: {\n                    reviewer: {\n                        select: {\n                            id: true,\n                            name: true,\n                            image: true\n                        }\n                    }\n                },\n                orderBy: {\n                    createdAt: \"desc\"\n                }\n            }\n        }\n    });\n}\nasync function getCategories() {\n    return _lib_prisma__WEBPACK_IMPORTED_MODULE_6__.prisma.category.findMany({\n        where: {\n            parentId: null\n        },\n        include: {\n            subcategories: true\n        }\n    });\n}\nasync function ProductPage(param) {\n    let { params } = param;\n    var _product_images, _product_images1;\n    const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_4__.getServerSession)(_app_api_auth_nextauth_route__WEBPACK_IMPORTED_MODULE_5__.authOptions);\n    const [product, categories] = await Promise.all([\n        getProduct(params.id),\n        getCategories()\n    ]);\n    if (!product) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    searchQuery: \"\",\n                    setSearchQuery: ()=>{},\n                    isMenuOpen: false,\n                    setIsMenuOpen: ()=>{}\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                    lineNumber: 80,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"container mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold text-center\",\n                        children: \"Product not found\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    categories: categories\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n            lineNumber: 79,\n            columnNumber: 7\n        }, this);\n    }\n    const averageRating = product.reviews.length > 0 ? product.reviews.reduce((acc, review)=>acc + review.rating, 0) / product.reviews.length : 0;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                searchQuery: \"\",\n                setSearchQuery: ()=>{},\n                isMenuOpen: false,\n                setIsMenuOpen: ()=>{}\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                lineNumber: 100,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-6xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative aspect-square\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                src: ((_product_images = product.images) === null || _product_images === void 0 ? void 0 : _product_images[0]) || \"/placeholder.png\",\n                                                alt: product.name,\n                                                fill: true,\n                                                className: \"object-cover rounded-lg\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                lineNumber: 112,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-4 gap-4\",\n                                            children: (_product_images1 = product.images) === null || _product_images1 === void 0 ? void 0 : _product_images1.slice(1).map((image, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative aspect-square\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        src: image,\n                                                        alt: \"\".concat(product.name, \" - Image \").concat(index + 2),\n                                                        fill: true,\n                                                        className: \"object-cover rounded-lg\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                        lineNumber: 122,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, index, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 121,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-3xl font-bold\",\n                                                    children: product.name\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 136,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-semibold text-indigo-600 mt-2\",\n                                                    children: [\n                                                        \"$\",\n                                                        product.price.toFixed(2)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 137,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative w-12 h-12\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                src: product.seller.image || \"/default-avatar.png\",\n                                                                alt: product.seller.name || \"Seller\",\n                                                                fill: true,\n                                                                className: \"rounded-full object-cover\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                lineNumber: 145,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 144,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"font-medium\",\n                                                                    children: product.seller.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                    lineNumber: 153,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: [\n                                                                                \"Rating: \",\n                                                                                product.seller.rating.toFixed(1)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                            lineNumber: 155,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        product.seller.verificationStatus === \"VERIFIED\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs bg-green-100 text-green-800 px-2 py-1 rounded\",\n                                                                            children: \"Verified\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                            lineNumber: 159,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                    lineNumber: 154,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 152,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 143,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FollowButtonClient__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    sellerId: product.seller.id\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 166,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"prose max-w-none\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: product.description\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium mb-2\",\n                                                            children: \"Category\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 175,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600\",\n                                                            children: product.category.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 176,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 174,\n                                                    columnNumber: 17\n                                                }, this),\n                                                product.location && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium mb-2\",\n                                                            children: \"Location\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 181,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600\",\n                                                            children: product.location.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 182,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 180,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-4 space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 text-yellow-800\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiExclamationCircle_HiShieldCheck_react_icons_hi__WEBPACK_IMPORTED_MODULE_11__.HiShieldCheck, {\n                                                            className: \"h-5 w-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 190,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold\",\n                                                            children: \"Safety Tips\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 191,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 189,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"space-y-2 text-sm text-yellow-700\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-start gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiExclamationCircle_HiShieldCheck_react_icons_hi__WEBPACK_IMPORTED_MODULE_11__.HiExclamationCircle, {\n                                                                    className: \"h-5 w-5 flex-shrink-0 mt-0.5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                    lineNumber: 195,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Never send advance payments or deposits before meeting the seller\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                    lineNumber: 196,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 194,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-start gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiExclamationCircle_HiShieldCheck_react_icons_hi__WEBPACK_IMPORTED_MODULE_11__.HiExclamationCircle, {\n                                                                    className: \"h-5 w-5 flex-shrink-0 mt-0.5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                    lineNumber: 199,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Always meet in a safe, public location with good lighting\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                    lineNumber: 200,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 198,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-start gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiExclamationCircle_HiShieldCheck_react_icons_hi__WEBPACK_IMPORTED_MODULE_11__.HiExclamationCircle, {\n                                                                    className: \"h-5 w-5 flex-shrink-0 mt-0.5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                    lineNumber: 203,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Thoroughly inspect the item before making any payment\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                    lineNumber: 204,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 202,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-start gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiExclamationCircle_HiShieldCheck_react_icons_hi__WEBPACK_IMPORTED_MODULE_11__.HiExclamationCircle, {\n                                                                    className: \"h-5 w-5 flex-shrink-0 mt-0.5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                    lineNumber: 207,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Verify all documentation and authenticity before completing the transaction\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                    lineNumber: 208,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 206,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"pt-6 space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_StartChatButton__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            productId: product.id,\n                                                            sellerId: product.seller.id,\n                                                            className: \"flex-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 215,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ShareButtonClient__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            title: product.name,\n                                                            description: product.description || undefined\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 220,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 214,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-500 flex items-center justify-end\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            product.shareCount || 0,\n                                                            \" shares\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                        lineNumber: 226,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 225,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: \"Reviews & Ratings\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-3xl font-bold\",\n                                                    children: averageRating.toFixed(1)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        1,\n                                                        2,\n                                                        3,\n                                                        4,\n                                                        5\n                                                    ].map((star)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-5 h-5 \".concat(star <= Math.round(averageRating) ? \"text-yellow-400\" : \"text-gray-300\"),\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                lineNumber: 250,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, star, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 240,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 238,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: [\n                                                        \"(\",\n                                                        product.reviews.length,\n                                                        \" reviews)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 13\n                                }, this),\n                                session && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-8 p-4 bg-white rounded-lg shadow-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold mb-4\",\n                                            children: \"Write a Review\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                            lineNumber: 262,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"Rating\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 265,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                1,\n                                                                2,\n                                                                3,\n                                                                4,\n                                                                5\n                                                            ].map((star)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    type: \"button\",\n                                                                    className: \"text-gray-300 hover:text-yellow-400 focus:outline-none\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-8 h-8\",\n                                                                        fill: \"currentColor\",\n                                                                        viewBox: \"0 0 20 20\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                            lineNumber: 280,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                        lineNumber: 275,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, star, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                    lineNumber: 270,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 268,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"comment\",\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"Your Review\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 287,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            id: \"comment\",\n                                                            rows: 4,\n                                                            className: \"w-full rounded-lg border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500\",\n                                                            placeholder: \"Share your experience with this product...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 293,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 286,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"submit\",\n                                                    className: \"px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2\",\n                                                    children: \"Submit Review\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 300,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                    lineNumber: 261,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: product.reviews.map((review)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white rounded-lg shadow-sm p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative w-10 h-10\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                        src: review.reviewer.image || \"/default-avatar.png\",\n                                                                        alt: review.reviewer.name || \"Reviewer\",\n                                                                        fill: true,\n                                                                        className: \"rounded-full object-cover\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                        lineNumber: 316,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                    lineNumber: 315,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                            className: \"font-medium\",\n                                                                            children: review.reviewer.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                            lineNumber: 324,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center\",\n                                                                            children: [\n                                                                                1,\n                                                                                2,\n                                                                                3,\n                                                                                4,\n                                                                                5\n                                                                            ].map((star)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                    className: \"w-4 h-4 \".concat(star <= review.rating ? \"text-yellow-400\" : \"text-gray-300\"),\n                                                                                    fill: \"currentColor\",\n                                                                                    viewBox: \"0 0 20 20\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                        d: \"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                                        lineNumber: 337,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                }, star, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                                    lineNumber: 327,\n                                                                                    columnNumber: 29\n                                                                                }, this))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                            lineNumber: 325,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                    lineNumber: 323,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 314,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: new Date(review.createdAt).toLocaleDateString()\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 343,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 313,\n                                                    columnNumber: 19\n                                                }, this),\n                                                review.comment && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: review.comment\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 348,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, review.id, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                            lineNumber: 312,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                    lineNumber: 310,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                            lineNumber: 233,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                    lineNumber: 107,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                lineNumber: 106,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                categories: categories\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                lineNumber: 356,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n        lineNumber: 99,\n        columnNumber: 5\n    }, this);\n}\n_c = ProductPage;\nvar _c;\n$RefreshReg$(_c, \"ProductPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/product/[id]/page.tsx\n"));

/***/ })

});