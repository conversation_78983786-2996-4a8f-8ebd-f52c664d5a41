"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/product/[id]/page",{

/***/ "(app-pages-browser)/./src/app/product/[id]/page.tsx":
/*!***************************************!*\
  !*** ./src/app/product/[id]/page.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ProductPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Header */ \"(app-pages-browser)/./src/components/Header.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Footer */ \"(app-pages-browser)/./src/components/Footer.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-auth */ \"(app-pages-browser)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _app_api_auth_nextauth_route__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/api/auth/[...nextauth]/route */ \"(app-pages-browser)/./src/app/api/auth/[...nextauth]/route.ts\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/prisma */ \"(app-pages-browser)/./src/lib/prisma.ts\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _components_StartChatButton__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/StartChatButton */ \"(app-pages-browser)/./src/components/StartChatButton.tsx\");\n/* harmony import */ var _components_FollowButtonClient__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/FollowButtonClient */ \"(app-pages-browser)/./src/components/FollowButtonClient.tsx\");\n/* harmony import */ var _components_ShareButtonClient__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ShareButtonClient */ \"(app-pages-browser)/./src/components/ShareButtonClient.tsx\");\n/* harmony import */ var _barrel_optimize_names_HiExclamationCircle_HiShieldCheck_react_icons_hi__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=HiExclamationCircle,HiShieldCheck!=!react-icons/hi */ \"(app-pages-browser)/./node_modules/react-icons/hi/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\n\nasync function getProduct(id) {\n    const product = await _lib_prisma__WEBPACK_IMPORTED_MODULE_6__.prisma.product.findUnique({\n        where: {\n            id\n        },\n        include: {\n            seller: true,\n            category: true,\n            location: true,\n            reviews: {\n                include: {\n                    reviewer: true\n                },\n                orderBy: {\n                    createdAt: \"desc\"\n                }\n            }\n        }\n    });\n    if (!product) return null;\n    return {\n        ...product,\n        seller: {\n            id: product.seller.id,\n            name: product.seller.name,\n            image: product.seller.image || null,\n            rating: product.seller.rating || 0,\n            verificationStatus: product.seller.verificationStatus || \"PENDING\"\n        },\n        location: product.location ? {\n            id: product.location.id,\n            name: product.location.name || product.location.city || \"\"\n        } : undefined,\n        reviews: product.reviews.map((review)=>({\n                id: review.id,\n                rating: review.rating,\n                comment: review.comment,\n                createdAt: review.createdAt,\n                reviewer: {\n                    id: review.reviewer.id,\n                    name: review.reviewer.name,\n                    image: review.reviewer.image || null\n                }\n            }))\n    };\n}\nasync function getCategories() {\n    const categories = await _lib_prisma__WEBPACK_IMPORTED_MODULE_6__.prisma.category.findMany({\n        where: {\n            parentId: null\n        },\n        include: {\n            subcategories: true\n        }\n    });\n    return categories.map((category)=>({\n            ...category,\n            count: 0\n        }));\n}\nasync function ProductPage(param) {\n    let { params } = param;\n    const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_4__.getServerSession)(_app_api_auth_nextauth_route__WEBPACK_IMPORTED_MODULE_5__.authOptions);\n    const [product, categories] = await Promise.all([\n        getProduct(params.id),\n        getCategories()\n    ]);\n    if (!product) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    searchQuery: \"\",\n                    setSearchQuery: ()=>{},\n                    isMenuOpen: false,\n                    setIsMenuOpen: ()=>{}\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                    lineNumber: 102,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"container mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold text-center\",\n                        children: \"Product not found\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                    lineNumber: 108,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    categories: categories\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                    lineNumber: 111,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n            lineNumber: 101,\n            columnNumber: 7\n        }, this);\n    }\n    const averageRating = product.reviews.length > 0 ? product.reviews.reduce((acc, review)=>acc + review.rating, 0) / product.reviews.length : 0;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                searchQuery: \"\",\n                setSearchQuery: ()=>{},\n                isMenuOpen: false,\n                setIsMenuOpen: ()=>{}\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                lineNumber: 122,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-6xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative aspect-square\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            src: \"/placeholder.png\",\n                                            alt: product.name,\n                                            fill: true,\n                                            className: \"object-cover rounded-lg\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-3xl font-bold\",\n                                                    children: product.name\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 146,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-semibold text-indigo-600 mt-2\",\n                                                    children: [\n                                                        \"$\",\n                                                        product.price.toFixed(2)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 147,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative w-12 h-12\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                src: product.seller.image || \"/default-avatar.png\",\n                                                                alt: product.seller.name || \"Seller\",\n                                                                fill: true,\n                                                                className: \"rounded-full object-cover\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                lineNumber: 155,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 154,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"font-medium\",\n                                                                    children: product.seller.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                    lineNumber: 163,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: [\n                                                                                \"Rating: \",\n                                                                                product.seller.rating.toFixed(1)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                            lineNumber: 165,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        product.seller.verificationStatus === \"VERIFIED\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs bg-green-100 text-green-800 px-2 py-1 rounded\",\n                                                                            children: \"Verified\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                            lineNumber: 169,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                    lineNumber: 164,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 162,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 153,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FollowButtonClient__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    sellerId: product.seller.id\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 176,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"prose max-w-none\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: product.description\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium mb-2\",\n                                                            children: \"Category\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 185,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600\",\n                                                            children: product.category.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 186,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 17\n                                                }, this),\n                                                product.location && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium mb-2\",\n                                                            children: \"Location\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 191,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600\",\n                                                            children: product.location.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 192,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 190,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-4 space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 text-yellow-800\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiExclamationCircle_HiShieldCheck_react_icons_hi__WEBPACK_IMPORTED_MODULE_11__.HiShieldCheck, {\n                                                            className: \"h-5 w-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 200,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold\",\n                                                            children: \"Safety Tips\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 201,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 199,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"space-y-2 text-sm text-yellow-700\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-start gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiExclamationCircle_HiShieldCheck_react_icons_hi__WEBPACK_IMPORTED_MODULE_11__.HiExclamationCircle, {\n                                                                    className: \"h-5 w-5 flex-shrink-0 mt-0.5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                    lineNumber: 205,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Never send advance payments or deposits before meeting the seller\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                    lineNumber: 206,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 204,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-start gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiExclamationCircle_HiShieldCheck_react_icons_hi__WEBPACK_IMPORTED_MODULE_11__.HiExclamationCircle, {\n                                                                    className: \"h-5 w-5 flex-shrink-0 mt-0.5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                    lineNumber: 209,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Always meet in a safe, public location with good lighting\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                    lineNumber: 210,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 208,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-start gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiExclamationCircle_HiShieldCheck_react_icons_hi__WEBPACK_IMPORTED_MODULE_11__.HiExclamationCircle, {\n                                                                    className: \"h-5 w-5 flex-shrink-0 mt-0.5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                    lineNumber: 213,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Thoroughly inspect the item before making any payment\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                    lineNumber: 214,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 212,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-start gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiExclamationCircle_HiShieldCheck_react_icons_hi__WEBPACK_IMPORTED_MODULE_11__.HiExclamationCircle, {\n                                                                    className: \"h-5 w-5 flex-shrink-0 mt-0.5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                    lineNumber: 217,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Verify all documentation and authenticity before completing the transaction\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                    lineNumber: 218,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 216,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 203,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"pt-6 space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_StartChatButton__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            productId: product.id,\n                                                            sellerId: product.seller.id,\n                                                            className: \"flex-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 225,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ShareButtonClient__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            title: product.name,\n                                                            description: product.description || undefined\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 230,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 224,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-500 flex items-center justify-end\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            product.shareCount || 0,\n                                                            \" shares\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                        lineNumber: 236,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 235,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: \"Reviews & Ratings\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                            lineNumber: 245,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-3xl font-bold\",\n                                                    children: averageRating.toFixed(1)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 247,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        1,\n                                                        2,\n                                                        3,\n                                                        4,\n                                                        5\n                                                    ].map((star)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-5 h-5 \".concat(star <= Math.round(averageRating) ? \"text-yellow-400\" : \"text-gray-300\"),\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                lineNumber: 260,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, star, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 250,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 248,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: [\n                                                        \"(\",\n                                                        product.reviews.length,\n                                                        \" reviews)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 13\n                                }, this),\n                                session && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-8 p-4 bg-white rounded-lg shadow-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold mb-4\",\n                                            children: \"Write a Review\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                            lineNumber: 272,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"Rating\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 275,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                1,\n                                                                2,\n                                                                3,\n                                                                4,\n                                                                5\n                                                            ].map((star)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    type: \"button\",\n                                                                    className: \"text-gray-300 hover:text-yellow-400 focus:outline-none\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-8 h-8\",\n                                                                        fill: \"currentColor\",\n                                                                        viewBox: \"0 0 20 20\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                            lineNumber: 290,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                        lineNumber: 285,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, star, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                    lineNumber: 280,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 278,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 274,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"comment\",\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"Your Review\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 297,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            id: \"comment\",\n                                                            rows: 4,\n                                                            className: \"w-full rounded-lg border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500\",\n                                                            placeholder: \"Share your experience with this product...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 303,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 296,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"submit\",\n                                                    className: \"px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2\",\n                                                    children: \"Submit Review\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 310,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                    lineNumber: 271,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: product.reviews.map((review)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white rounded-lg shadow-sm p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative w-10 h-10\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                        src: review.reviewer.image || \"/default-avatar.png\",\n                                                                        alt: review.reviewer.name || \"Reviewer\",\n                                                                        fill: true,\n                                                                        className: \"rounded-full object-cover\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                        lineNumber: 326,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                    lineNumber: 325,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                            className: \"font-medium\",\n                                                                            children: review.reviewer.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                            lineNumber: 334,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center\",\n                                                                            children: [\n                                                                                1,\n                                                                                2,\n                                                                                3,\n                                                                                4,\n                                                                                5\n                                                                            ].map((star)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                    className: \"w-4 h-4 \".concat(star <= review.rating ? \"text-yellow-400\" : \"text-gray-300\"),\n                                                                                    fill: \"currentColor\",\n                                                                                    viewBox: \"0 0 20 20\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                        d: \"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                                        lineNumber: 347,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                }, star, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                                    lineNumber: 337,\n                                                                                    columnNumber: 29\n                                                                                }, this))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                            lineNumber: 335,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                    lineNumber: 333,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 324,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: new Date(review.createdAt).toLocaleDateString()\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 353,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 323,\n                                                    columnNumber: 19\n                                                }, this),\n                                                review.comment && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: review.comment\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 358,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, review.id, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                            lineNumber: 322,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                    lineNumber: 320,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                            lineNumber: 243,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                    lineNumber: 129,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                lineNumber: 128,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                categories: categories\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                lineNumber: 366,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n        lineNumber: 121,\n        columnNumber: 5\n    }, this);\n}\n_c = ProductPage;\nvar _c;\n$RefreshReg$(_c, \"ProductPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/product/[id]/page.tsx\n"));

/***/ })

});