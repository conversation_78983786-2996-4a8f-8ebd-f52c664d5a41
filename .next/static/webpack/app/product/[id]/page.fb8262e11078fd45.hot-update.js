"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/product/[id]/page",{

/***/ "(app-pages-browser)/./src/app/product/[id]/page.tsx":
/*!***************************************!*\
  !*** ./src/app/product/[id]/page.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ProductPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Header */ \"(app-pages-browser)/./src/components/Header.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Footer */ \"(app-pages-browser)/./src/components/Footer.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-auth */ \"(app-pages-browser)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _app_api_auth_nextauth_route__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/api/auth/[...nextauth]/route */ \"(app-pages-browser)/./src/app/api/auth/[...nextauth]/route.ts\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/prisma */ \"(app-pages-browser)/./src/lib/prisma.ts\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _components_StartChatButton__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/StartChatButton */ \"(app-pages-browser)/./src/components/StartChatButton.tsx\");\n/* harmony import */ var _components_FollowButtonClient__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/FollowButtonClient */ \"(app-pages-browser)/./src/components/FollowButtonClient.tsx\");\n/* harmony import */ var _components_ShareButtonClient__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ShareButtonClient */ \"(app-pages-browser)/./src/components/ShareButtonClient.tsx\");\n/* harmony import */ var _barrel_optimize_names_HiExclamationCircle_HiShieldCheck_react_icons_hi__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=HiExclamationCircle,HiShieldCheck!=!react-icons/hi */ \"(app-pages-browser)/./node_modules/react-icons/hi/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\n\nasync function getProduct(id) {\n    const product = await _lib_prisma__WEBPACK_IMPORTED_MODULE_6__.prisma.product.findUnique({\n        where: {\n            id\n        },\n        include: {\n            seller: {\n                select: {\n                    id: true,\n                    name: true,\n                    image: true,\n                    rating: true,\n                    verificationStatus: true\n                }\n            },\n            category: true,\n            location: true,\n            reviews: {\n                include: {\n                    reviewer: {\n                        select: {\n                            id: true,\n                            name: true,\n                            image: true\n                        }\n                    }\n                },\n                orderBy: {\n                    createdAt: \"desc\"\n                }\n            }\n        }\n    });\n    if (!product) return null;\n    return {\n        ...product,\n        seller: {\n            ...product.seller,\n            rating: product.seller.rating || 0,\n            verificationStatus: product.seller.verificationStatus || \"PENDING\"\n        }\n    };\n}\nasync function getCategories() {\n    const categories = await _lib_prisma__WEBPACK_IMPORTED_MODULE_6__.prisma.category.findMany({\n        where: {\n            parentId: null\n        },\n        include: {\n            subcategories: true\n        }\n    });\n    return categories.map((category)=>({\n            ...category,\n            count: 0\n        }));\n}\nasync function ProductPage(param) {\n    let { params } = param;\n    var _product_images, _product_images1;\n    const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_4__.getServerSession)(_app_api_auth_nextauth_route__WEBPACK_IMPORTED_MODULE_5__.authOptions);\n    const [product, categories] = await Promise.all([\n        getProduct(params.id),\n        getCategories()\n    ]);\n    if (!product) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    searchQuery: \"\",\n                    setSearchQuery: ()=>{},\n                    isMenuOpen: false,\n                    setIsMenuOpen: ()=>{}\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                    lineNumber: 98,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"container mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold text-center\",\n                        children: \"Product not found\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                    lineNumber: 104,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    categories: categories\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                    lineNumber: 107,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n            lineNumber: 97,\n            columnNumber: 7\n        }, this);\n    }\n    const averageRating = product.reviews.length > 0 ? product.reviews.reduce((acc, review)=>acc + review.rating, 0) / product.reviews.length : 0;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                searchQuery: \"\",\n                setSearchQuery: ()=>{},\n                isMenuOpen: false,\n                setIsMenuOpen: ()=>{}\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                lineNumber: 118,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-6xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative aspect-square\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                src: ((_product_images = product.images) === null || _product_images === void 0 ? void 0 : _product_images[0]) || \"/placeholder.png\",\n                                                alt: product.name,\n                                                fill: true,\n                                                className: \"object-cover rounded-lg\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                lineNumber: 130,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-4 gap-4\",\n                                            children: (_product_images1 = product.images) === null || _product_images1 === void 0 ? void 0 : _product_images1.slice(1).map((image, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative aspect-square\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        src: image,\n                                                        alt: \"\".concat(product.name, \" - Image \").concat(index + 2),\n                                                        fill: true,\n                                                        className: \"object-cover rounded-lg\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                        lineNumber: 140,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, index, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 139,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-3xl font-bold\",\n                                                    children: product.name\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 154,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-semibold text-indigo-600 mt-2\",\n                                                    children: [\n                                                        \"$\",\n                                                        product.price.toFixed(2)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 155,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative w-12 h-12\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                src: product.seller.image || \"/default-avatar.png\",\n                                                                alt: product.seller.name || \"Seller\",\n                                                                fill: true,\n                                                                className: \"rounded-full object-cover\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                lineNumber: 163,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 162,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"font-medium\",\n                                                                    children: product.seller.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                    lineNumber: 171,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: [\n                                                                                \"Rating: \",\n                                                                                product.seller.rating.toFixed(1)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                            lineNumber: 173,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        product.seller.verificationStatus === \"VERIFIED\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs bg-green-100 text-green-800 px-2 py-1 rounded\",\n                                                                            children: \"Verified\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                            lineNumber: 177,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                    lineNumber: 172,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 170,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 161,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FollowButtonClient__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    sellerId: product.seller.id\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"prose max-w-none\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: product.description\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium mb-2\",\n                                                            children: \"Category\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 193,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600\",\n                                                            children: product.category.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 194,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 192,\n                                                    columnNumber: 17\n                                                }, this),\n                                                product.location && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium mb-2\",\n                                                            children: \"Location\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 199,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600\",\n                                                            children: product.location.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 200,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-4 space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 text-yellow-800\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiExclamationCircle_HiShieldCheck_react_icons_hi__WEBPACK_IMPORTED_MODULE_11__.HiShieldCheck, {\n                                                            className: \"h-5 w-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 208,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold\",\n                                                            children: \"Safety Tips\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 209,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 207,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"space-y-2 text-sm text-yellow-700\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-start gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiExclamationCircle_HiShieldCheck_react_icons_hi__WEBPACK_IMPORTED_MODULE_11__.HiExclamationCircle, {\n                                                                    className: \"h-5 w-5 flex-shrink-0 mt-0.5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                    lineNumber: 213,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Never send advance payments or deposits before meeting the seller\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                    lineNumber: 214,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 212,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-start gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiExclamationCircle_HiShieldCheck_react_icons_hi__WEBPACK_IMPORTED_MODULE_11__.HiExclamationCircle, {\n                                                                    className: \"h-5 w-5 flex-shrink-0 mt-0.5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                    lineNumber: 217,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Always meet in a safe, public location with good lighting\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                    lineNumber: 218,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 216,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-start gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiExclamationCircle_HiShieldCheck_react_icons_hi__WEBPACK_IMPORTED_MODULE_11__.HiExclamationCircle, {\n                                                                    className: \"h-5 w-5 flex-shrink-0 mt-0.5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                    lineNumber: 221,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Thoroughly inspect the item before making any payment\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                    lineNumber: 222,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 220,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-start gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiExclamationCircle_HiShieldCheck_react_icons_hi__WEBPACK_IMPORTED_MODULE_11__.HiExclamationCircle, {\n                                                                    className: \"h-5 w-5 flex-shrink-0 mt-0.5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                    lineNumber: 225,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Verify all documentation and authenticity before completing the transaction\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                    lineNumber: 226,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 224,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 211,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"pt-6 space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_StartChatButton__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            productId: product.id,\n                                                            sellerId: product.seller.id,\n                                                            className: \"flex-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 233,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ShareButtonClient__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            title: product.name,\n                                                            description: product.description || undefined\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 238,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 232,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-500 flex items-center justify-end\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            product.shareCount || 0,\n                                                            \" shares\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                        lineNumber: 244,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 243,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: \"Reviews & Ratings\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-3xl font-bold\",\n                                                    children: averageRating.toFixed(1)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 255,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        1,\n                                                        2,\n                                                        3,\n                                                        4,\n                                                        5\n                                                    ].map((star)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-5 h-5 \".concat(star <= Math.round(averageRating) ? \"text-yellow-400\" : \"text-gray-300\"),\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                lineNumber: 268,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, star, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 258,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 256,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: [\n                                                        \"(\",\n                                                        product.reviews.length,\n                                                        \" reviews)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 13\n                                }, this),\n                                session && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-8 p-4 bg-white rounded-lg shadow-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold mb-4\",\n                                            children: \"Write a Review\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"Rating\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 283,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                1,\n                                                                2,\n                                                                3,\n                                                                4,\n                                                                5\n                                                            ].map((star)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    type: \"button\",\n                                                                    className: \"text-gray-300 hover:text-yellow-400 focus:outline-none\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-8 h-8\",\n                                                                        fill: \"currentColor\",\n                                                                        viewBox: \"0 0 20 20\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                            lineNumber: 298,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                        lineNumber: 293,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, star, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                    lineNumber: 288,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 286,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 282,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"comment\",\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"Your Review\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 305,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            id: \"comment\",\n                                                            rows: 4,\n                                                            className: \"w-full rounded-lg border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500\",\n                                                            placeholder: \"Share your experience with this product...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 311,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 304,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"submit\",\n                                                    className: \"px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2\",\n                                                    children: \"Submit Review\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 318,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                            lineNumber: 281,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: product.reviews.map((review)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white rounded-lg shadow-sm p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative w-10 h-10\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                        src: review.reviewer.image || \"/default-avatar.png\",\n                                                                        alt: review.reviewer.name || \"Reviewer\",\n                                                                        fill: true,\n                                                                        className: \"rounded-full object-cover\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                        lineNumber: 334,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                    lineNumber: 333,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                            className: \"font-medium\",\n                                                                            children: review.reviewer.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                            lineNumber: 342,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center\",\n                                                                            children: [\n                                                                                1,\n                                                                                2,\n                                                                                3,\n                                                                                4,\n                                                                                5\n                                                                            ].map((star)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                    className: \"w-4 h-4 \".concat(star <= review.rating ? \"text-yellow-400\" : \"text-gray-300\"),\n                                                                                    fill: \"currentColor\",\n                                                                                    viewBox: \"0 0 20 20\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                        d: \"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                                        lineNumber: 355,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                }, star, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                                    lineNumber: 345,\n                                                                                    columnNumber: 29\n                                                                                }, this))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                            lineNumber: 343,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                    lineNumber: 341,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 332,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: new Date(review.createdAt).toLocaleDateString()\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 361,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 331,\n                                                    columnNumber: 19\n                                                }, this),\n                                                review.comment && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: review.comment\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 366,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, review.id, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                            lineNumber: 330,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                    lineNumber: 328,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                            lineNumber: 251,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                    lineNumber: 125,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                categories: categories\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                lineNumber: 374,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n        lineNumber: 117,\n        columnNumber: 5\n    }, this);\n}\n_c = ProductPage;\nvar _c;\n$RefreshReg$(_c, \"ProductPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/product/[id]/page.tsx\n"));

/***/ })

});