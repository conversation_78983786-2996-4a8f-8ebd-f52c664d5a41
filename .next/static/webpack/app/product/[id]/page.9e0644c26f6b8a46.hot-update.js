"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/product/[id]/page",{

/***/ "(app-pages-browser)/./src/app/product/[id]/page.tsx":
/*!***************************************!*\
  !*** ./src/app/product/[id]/page.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ProductPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Header */ \"(app-pages-browser)/./src/components/Header.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Footer */ \"(app-pages-browser)/./src/components/Footer.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-auth */ \"(app-pages-browser)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _app_api_auth_nextauth_route__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/api/auth/[...nextauth]/route */ \"(app-pages-browser)/./src/app/api/auth/[...nextauth]/route.ts\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/prisma */ \"(app-pages-browser)/./src/lib/prisma.ts\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _components_StartChatButton__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/StartChatButton */ \"(app-pages-browser)/./src/components/StartChatButton.tsx\");\n/* harmony import */ var _components_FollowButtonClient__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/FollowButtonClient */ \"(app-pages-browser)/./src/components/FollowButtonClient.tsx\");\n/* harmony import */ var _components_ShareButtonClient__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ShareButtonClient */ \"(app-pages-browser)/./src/components/ShareButtonClient.tsx\");\n/* harmony import */ var _barrel_optimize_names_HiExclamationCircle_HiShieldCheck_react_icons_hi__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=HiExclamationCircle,HiShieldCheck!=!react-icons/hi */ \"(app-pages-browser)/./node_modules/react-icons/hi/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\n\nasync function getProduct(id) {\n    const product = await _lib_prisma__WEBPACK_IMPORTED_MODULE_6__.prisma.product.findUnique({\n        where: {\n            id\n        },\n        include: {\n            seller: true,\n            category: true,\n            location: true,\n            reviews: {\n                include: {\n                    reviewer: true\n                },\n                orderBy: {\n                    createdAt: \"desc\"\n                }\n            }\n        }\n    });\n    if (!product) return null;\n    return {\n        ...product,\n        seller: {\n            id: product.seller.id,\n            name: product.seller.name,\n            image: product.seller.image || null,\n            rating: product.seller.rating || 0,\n            verificationStatus: product.seller.verificationStatus || \"PENDING\"\n        },\n        reviews: product.reviews.map((review)=>({\n                id: review.id,\n                rating: review.rating,\n                comment: review.comment,\n                createdAt: review.createdAt,\n                reviewer: {\n                    id: review.reviewer.id,\n                    name: review.reviewer.name,\n                    image: review.reviewer.image || null\n                }\n            }))\n    };\n}\nasync function getCategories() {\n    const categories = await _lib_prisma__WEBPACK_IMPORTED_MODULE_6__.prisma.category.findMany({\n        where: {\n            parentId: null\n        },\n        include: {\n            subcategories: true\n        }\n    });\n    return categories.map((category)=>({\n            ...category,\n            count: 0\n        }));\n}\nasync function ProductPage(param) {\n    let { params } = param;\n    const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_4__.getServerSession)(_app_api_auth_nextauth_route__WEBPACK_IMPORTED_MODULE_5__.authOptions);\n    const [product, categories] = await Promise.all([\n        getProduct(params.id),\n        getCategories()\n    ]);\n    if (!product) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    searchQuery: \"\",\n                    setSearchQuery: ()=>{},\n                    isMenuOpen: false,\n                    setIsMenuOpen: ()=>{}\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"container mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold text-center\",\n                        children: \"Product not found\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                    lineNumber: 102,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    categories: categories\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                    lineNumber: 105,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n            lineNumber: 95,\n            columnNumber: 7\n        }, this);\n    }\n    const averageRating = product.reviews.length > 0 ? product.reviews.reduce((acc, review)=>acc + review.rating, 0) / product.reviews.length : 0;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                searchQuery: \"\",\n                setSearchQuery: ()=>{},\n                isMenuOpen: false,\n                setIsMenuOpen: ()=>{}\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                lineNumber: 116,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-6xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative aspect-square\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            src: \"/placeholder.png\",\n                                            alt: product.name,\n                                            fill: true,\n                                            className: \"object-cover rounded-lg\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-3xl font-bold\",\n                                                    children: product.name\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 140,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-semibold text-indigo-600 mt-2\",\n                                                    children: [\n                                                        \"$\",\n                                                        product.price.toFixed(2)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 141,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative w-12 h-12\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                src: product.seller.image || \"/default-avatar.png\",\n                                                                alt: product.seller.name || \"Seller\",\n                                                                fill: true,\n                                                                className: \"rounded-full object-cover\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                lineNumber: 149,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 148,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"font-medium\",\n                                                                    children: product.seller.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                    lineNumber: 157,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: [\n                                                                                \"Rating: \",\n                                                                                product.seller.rating.toFixed(1)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                            lineNumber: 159,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        product.seller.verificationStatus === \"VERIFIED\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs bg-green-100 text-green-800 px-2 py-1 rounded\",\n                                                                            children: \"Verified\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                            lineNumber: 163,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                    lineNumber: 158,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 156,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 147,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FollowButtonClient__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    sellerId: product.seller.id\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"prose max-w-none\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: product.description\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium mb-2\",\n                                                            children: \"Category\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 179,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600\",\n                                                            children: product.category.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 180,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 178,\n                                                    columnNumber: 17\n                                                }, this),\n                                                product.location && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium mb-2\",\n                                                            children: \"Location\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 185,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600\",\n                                                            children: product.location.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 186,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-4 space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 text-yellow-800\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiExclamationCircle_HiShieldCheck_react_icons_hi__WEBPACK_IMPORTED_MODULE_11__.HiShieldCheck, {\n                                                            className: \"h-5 w-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 194,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold\",\n                                                            children: \"Safety Tips\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 195,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"space-y-2 text-sm text-yellow-700\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-start gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiExclamationCircle_HiShieldCheck_react_icons_hi__WEBPACK_IMPORTED_MODULE_11__.HiExclamationCircle, {\n                                                                    className: \"h-5 w-5 flex-shrink-0 mt-0.5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                    lineNumber: 199,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Never send advance payments or deposits before meeting the seller\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                    lineNumber: 200,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 198,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-start gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiExclamationCircle_HiShieldCheck_react_icons_hi__WEBPACK_IMPORTED_MODULE_11__.HiExclamationCircle, {\n                                                                    className: \"h-5 w-5 flex-shrink-0 mt-0.5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                    lineNumber: 203,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Always meet in a safe, public location with good lighting\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                    lineNumber: 204,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 202,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-start gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiExclamationCircle_HiShieldCheck_react_icons_hi__WEBPACK_IMPORTED_MODULE_11__.HiExclamationCircle, {\n                                                                    className: \"h-5 w-5 flex-shrink-0 mt-0.5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                    lineNumber: 207,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Thoroughly inspect the item before making any payment\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                    lineNumber: 208,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 206,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-start gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiExclamationCircle_HiShieldCheck_react_icons_hi__WEBPACK_IMPORTED_MODULE_11__.HiExclamationCircle, {\n                                                                    className: \"h-5 w-5 flex-shrink-0 mt-0.5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                    lineNumber: 211,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Verify all documentation and authenticity before completing the transaction\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                    lineNumber: 212,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 210,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 197,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"pt-6 space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_StartChatButton__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            productId: product.id,\n                                                            sellerId: product.seller.id,\n                                                            className: \"flex-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 219,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ShareButtonClient__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            title: product.name,\n                                                            description: product.description || undefined\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 224,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-500 flex items-center justify-end\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            product.shareCount || 0,\n                                                            \" shares\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                        lineNumber: 230,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 229,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: \"Reviews & Ratings\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                            lineNumber: 239,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-3xl font-bold\",\n                                                    children: averageRating.toFixed(1)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 241,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        1,\n                                                        2,\n                                                        3,\n                                                        4,\n                                                        5\n                                                    ].map((star)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-5 h-5 \".concat(star <= Math.round(averageRating) ? \"text-yellow-400\" : \"text-gray-300\"),\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                lineNumber: 254,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, star, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 244,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 242,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: [\n                                                        \"(\",\n                                                        product.reviews.length,\n                                                        \" reviews)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 258,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                            lineNumber: 240,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                    lineNumber: 238,\n                                    columnNumber: 13\n                                }, this),\n                                session && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-8 p-4 bg-white rounded-lg shadow-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold mb-4\",\n                                            children: \"Write a Review\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                            lineNumber: 266,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"Rating\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 269,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                1,\n                                                                2,\n                                                                3,\n                                                                4,\n                                                                5\n                                                            ].map((star)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    type: \"button\",\n                                                                    className: \"text-gray-300 hover:text-yellow-400 focus:outline-none\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-8 h-8\",\n                                                                        fill: \"currentColor\",\n                                                                        viewBox: \"0 0 20 20\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                            lineNumber: 284,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                        lineNumber: 279,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, star, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                    lineNumber: 274,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 272,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"comment\",\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"Your Review\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 291,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            id: \"comment\",\n                                                            rows: 4,\n                                                            className: \"w-full rounded-lg border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500\",\n                                                            placeholder: \"Share your experience with this product...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 297,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 290,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"submit\",\n                                                    className: \"px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2\",\n                                                    children: \"Submit Review\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 304,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                    lineNumber: 265,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: product.reviews.map((review)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white rounded-lg shadow-sm p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative w-10 h-10\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                        src: review.reviewer.image || \"/default-avatar.png\",\n                                                                        alt: review.reviewer.name || \"Reviewer\",\n                                                                        fill: true,\n                                                                        className: \"rounded-full object-cover\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                        lineNumber: 320,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                    lineNumber: 319,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                            className: \"font-medium\",\n                                                                            children: review.reviewer.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                            lineNumber: 328,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center\",\n                                                                            children: [\n                                                                                1,\n                                                                                2,\n                                                                                3,\n                                                                                4,\n                                                                                5\n                                                                            ].map((star)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                    className: \"w-4 h-4 \".concat(star <= review.rating ? \"text-yellow-400\" : \"text-gray-300\"),\n                                                                                    fill: \"currentColor\",\n                                                                                    viewBox: \"0 0 20 20\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                        d: \"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                                        lineNumber: 341,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                }, star, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                                    lineNumber: 331,\n                                                                                    columnNumber: 29\n                                                                                }, this))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                            lineNumber: 329,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                    lineNumber: 327,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 318,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: new Date(review.createdAt).toLocaleDateString()\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 347,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 317,\n                                                    columnNumber: 19\n                                                }, this),\n                                                review.comment && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: review.comment\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 352,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, review.id, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                    lineNumber: 314,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                            lineNumber: 237,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                    lineNumber: 123,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                lineNumber: 122,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                categories: categories\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                lineNumber: 360,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n        lineNumber: 115,\n        columnNumber: 5\n    }, this);\n}\n_c = ProductPage;\nvar _c;\n$RefreshReg$(_c, \"ProductPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/product/[id]/page.tsx\n"));

/***/ })

});