/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/product/[id]/page";
exports.ids = ["app/product/[id]/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fproduct%2F%5Bid%5D%2Fpage&page=%2Fproduct%2F%5Bid%5D%2Fpage&appPaths=%2Fproduct%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fproduct%2F%5Bid%5D%2Fpage.tsx&appDir=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fproduct%2F%5Bid%5D%2Fpage&page=%2Fproduct%2F%5Bid%5D%2Fpage&appPaths=%2Fproduct%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fproduct%2F%5Bid%5D%2Fpage.tsx&appDir=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'product',\n        {\n        children: [\n        '[id]',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/product/[id]/page.tsx */ \"(rsc)/./src/app/product/[id]/page.tsx\")), \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/Users/<USER>/Documents/AI Development/marketplace/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/product/[id]/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/product/[id]/page\",\n        pathname: \"/product/[id]\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkZwcm9kdWN0JTJGJTVCaWQlNUQlMkZwYWdlJnBhZ2U9JTJGcHJvZHVjdCUyRiU1QmlkJTVEJTJGcGFnZSZhcHBQYXRocz0lMkZwcm9kdWN0JTJGJTVCaWQlNUQlMkZwYWdlJnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGcHJvZHVjdCUyRiU1QmlkJTVEJTJGcGFnZS50c3gmYXBwRGlyPSUyRlVzZXJzJTJGbWFjJTJGRG9jdW1lbnRzJTJGQUklMjBEZXZlbG9wbWVudCUyRm1hcmtldHBsYWNlJTJGc3JjJTJGYXBwJnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMmcm9vdERpcj0lMkZVc2VycyUyRm1hYyUyRkRvY3VtZW50cyUyRkFJJTIwRGV2ZWxvcG1lbnQlMkZtYXJrZXRwbGFjZSZpc0Rldj10cnVlJnRzY29uZmlnUGF0aD10c2NvbmZpZy5qc29uJmJhc2VQYXRoPSZhc3NldFByZWZpeD0mbmV4dENvbmZpZ091dHB1dD0mcHJlZmVycmVkUmVnaW9uPSZtaWRkbGV3YXJlQ29uZmlnPWUzMCUzRCEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLGFBQWEsc0JBQXNCO0FBQ2lFO0FBQ3JDO0FBQy9EO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQztBQUNqQyx1QkFBdUIsMEtBQWtIO0FBQ3pJO0FBQ0EsU0FBUztBQUNULE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQSx5QkFBeUIsb0pBQXVHO0FBQ2hJLG9CQUFvQiwwTkFBZ0Y7QUFDcEc7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ3VCO0FBQzZEO0FBQ3BGLDZCQUE2QixtQkFBbUI7QUFDaEQ7QUFDTztBQUNBO0FBQ1A7QUFDQTtBQUNBO0FBQ3VEO0FBQ3ZEO0FBQ08sd0JBQXdCLDhHQUFrQjtBQUNqRDtBQUNBLGNBQWMseUVBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLENBQUM7O0FBRUQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJrZXRwbGFjZS8/MGVjMCJdLCJzb3VyY2VzQ29udGVudCI6WyJcIlRVUkJPUEFDSyB7IHRyYW5zaXRpb246IG5leHQtc3NyIH1cIjtcbmltcG9ydCB7IEFwcFBhZ2VSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1tb2R1bGVzL2FwcC1wYWdlL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLWtpbmRcIjtcbi8vIFdlIGluamVjdCB0aGUgdHJlZSBhbmQgcGFnZXMgaGVyZSBzbyB0aGF0IHdlIGNhbiB1c2UgdGhlbSBpbiB0aGUgcm91dGVcbi8vIG1vZHVsZS5cbmNvbnN0IHRyZWUgPSB7XG4gICAgICAgIGNoaWxkcmVuOiBbXG4gICAgICAgICcnLFxuICAgICAgICB7XG4gICAgICAgIGNoaWxkcmVuOiBbXG4gICAgICAgICdwcm9kdWN0JyxcbiAgICAgICAge1xuICAgICAgICBjaGlsZHJlbjogW1xuICAgICAgICAnW2lkXScsXG4gICAgICAgIHtcbiAgICAgICAgY2hpbGRyZW46IFsnX19QQUdFX18nLCB7fSwge1xuICAgICAgICAgIHBhZ2U6IFsoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9tYWMvRG9jdW1lbnRzL0FJIERldmVsb3BtZW50L21hcmtldHBsYWNlL3NyYy9hcHAvcHJvZHVjdC9baWRdL3BhZ2UudHN4XCIpLCBcIi9Vc2Vycy9tYWMvRG9jdW1lbnRzL0FJIERldmVsb3BtZW50L21hcmtldHBsYWNlL3NyYy9hcHAvcHJvZHVjdC9baWRdL3BhZ2UudHN4XCJdLFxuICAgICAgICAgIFxuICAgICAgICB9XVxuICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICBcbiAgICAgICAgXG4gICAgICB9XG4gICAgICBdXG4gICAgICB9LFxuICAgICAgICB7XG4gICAgICAgIFxuICAgICAgICBcbiAgICAgIH1cbiAgICAgIF1cbiAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgJ2xheW91dCc6IFsoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9tYWMvRG9jdW1lbnRzL0FJIERldmVsb3BtZW50L21hcmtldHBsYWNlL3NyYy9hcHAvbGF5b3V0LnRzeFwiKSwgXCIvVXNlcnMvbWFjL0RvY3VtZW50cy9BSSBEZXZlbG9wbWVudC9tYXJrZXRwbGFjZS9zcmMvYXBwL2xheW91dC50c3hcIl0sXG4nbm90LWZvdW5kJzogWygpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL25vdC1mb3VuZC1lcnJvclwiKSwgXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbm90LWZvdW5kLWVycm9yXCJdLFxuICAgICAgICBcbiAgICAgIH1cbiAgICAgIF1cbiAgICAgIH0uY2hpbGRyZW47XG5jb25zdCBwYWdlcyA9IFtcIi9Vc2Vycy9tYWMvRG9jdW1lbnRzL0FJIERldmVsb3BtZW50L21hcmtldHBsYWNlL3NyYy9hcHAvcHJvZHVjdC9baWRdL3BhZ2UudHN4XCJdO1xuZXhwb3J0IHsgdHJlZSwgcGFnZXMgfTtcbmV4cG9ydCB7IGRlZmF1bHQgYXMgR2xvYmFsRXJyb3IgfSBmcm9tIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2Vycm9yLWJvdW5kYXJ5XCI7XG5jb25zdCBfX25leHRfYXBwX3JlcXVpcmVfXyA9IF9fd2VicGFja19yZXF1aXJlX19cbmNvbnN0IF9fbmV4dF9hcHBfbG9hZF9jaHVua19fID0gKCkgPT4gUHJvbWlzZS5yZXNvbHZlKClcbmV4cG9ydCBjb25zdCBvcmlnaW5hbFBhdGhuYW1lID0gXCIvcHJvZHVjdC9baWRdL3BhZ2VcIjtcbmV4cG9ydCBjb25zdCBfX25leHRfYXBwX18gPSB7XG4gICAgcmVxdWlyZTogX19uZXh0X2FwcF9yZXF1aXJlX18sXG4gICAgbG9hZENodW5rOiBfX25leHRfYXBwX2xvYWRfY2h1bmtfX1xufTtcbmV4cG9ydCAqIGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2FwcC1yZW5kZXIvZW50cnktYmFzZVwiO1xuLy8gQ3JlYXRlIGFuZCBleHBvcnQgdGhlIHJvdXRlIG1vZHVsZSB0aGF0IHdpbGwgYmUgY29uc3VtZWQuXG5leHBvcnQgY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgQXBwUGFnZVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5BUFBfUEFHRSxcbiAgICAgICAgcGFnZTogXCIvcHJvZHVjdC9baWRdL3BhZ2VcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL3Byb2R1Y3QvW2lkXVwiLFxuICAgICAgICAvLyBUaGUgZm9sbG93aW5nIGFyZW4ndCB1c2VkIGluIHByb2R1Y3Rpb24uXG4gICAgICAgIGJ1bmRsZVBhdGg6IFwiXCIsXG4gICAgICAgIGZpbGVuYW1lOiBcIlwiLFxuICAgICAgICBhcHBQYXRoczogW11cbiAgICB9LFxuICAgIHVzZXJsYW5kOiB7XG4gICAgICAgIGxvYWRlclRyZWU6IHRyZWVcbiAgICB9XG59KTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXBwLXBhZ2UuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fproduct%2F%5Bid%5D%2Fpage&page=%2Fproduct%2F%5Bid%5D%2Fpage&appPaths=%2Fproduct%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fproduct%2F%5Bid%5D%2Fpage.tsx&appDir=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fsrc%2Fapp%2Fglobals.css&modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fsrc%2Fapp%2Fproviders.tsx&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fsrc%2Fapp%2Fglobals.css&modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fsrc%2Fapp%2Fproviders.tsx&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/providers.tsx */ \"(ssr)/./src/app/providers.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZtYWMlMkZEb2N1bWVudHMlMkZBSSUyMERldmVsb3BtZW50JTJGbWFya2V0cGxhY2UlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZm9udCUyRmdvb2dsZSUyRnRhcmdldC5jc3MlM0YlN0IlMjJwYXRoJTIyJTNBJTIyc3JjJTJGYXBwJTJGbGF5b3V0LnRzeCUyMiUyQyUyMmltcG9ydCUyMiUzQSUyMkludGVyJTIyJTJDJTIyYXJndW1lbnRzJTIyJTNBJTVCJTdCJTIyc3Vic2V0cyUyMiUzQSU1QiUyMmxhdGluJTIyJTVEJTdEJTVEJTJDJTIydmFyaWFibGVOYW1lJTIyJTNBJTIyaW50ZXIlMjIlN0QmbW9kdWxlcz0lMkZVc2VycyUyRm1hYyUyRkRvY3VtZW50cyUyRkFJJTIwRGV2ZWxvcG1lbnQlMkZtYXJrZXRwbGFjZSUyRnNyYyUyRmFwcCUyRmdsb2JhbHMuY3NzJm1vZHVsZXM9JTJGVXNlcnMlMkZtYWMlMkZEb2N1bWVudHMlMkZBSSUyMERldmVsb3BtZW50JTJGbWFya2V0cGxhY2UlMkZzcmMlMkZhcHAlMkZwcm92aWRlcnMudHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL21hcmtldHBsYWNlLz9lMzdkIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL21hYy9Eb2N1bWVudHMvQUkgRGV2ZWxvcG1lbnQvbWFya2V0cGxhY2Uvc3JjL2FwcC9wcm92aWRlcnMudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fsrc%2Fapp%2Fglobals.css&modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fsrc%2Fapp%2Fproviders.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fsrc%2Fapp%2Fproduct%2F%5Bid%5D%2Fpage.tsx&server=true!":
/*!**********************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fsrc%2Fapp%2Fproduct%2F%5Bid%5D%2Fpage.tsx&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/product/[id]/page.tsx */ \"(ssr)/./src/app/product/[id]/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZtYWMlMkZEb2N1bWVudHMlMkZBSSUyMERldmVsb3BtZW50JTJGbWFya2V0cGxhY2UlMkZzcmMlMkZhcHAlMkZwcm9kdWN0JTJGJTVCaWQlNUQlMkZwYWdlLnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJrZXRwbGFjZS8/ZDZjMSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9tYWMvRG9jdW1lbnRzL0FJIERldmVsb3BtZW50L21hcmtldHBsYWNlL3NyYy9hcHAvcHJvZHVjdC9baWRdL3BhZ2UudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fsrc%2Fapp%2Fproduct%2F%5Bid%5D%2Fpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/product/[id]/page.tsx":
/*!***************************************!*\
  !*** ./src/app/product/[id]/page.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProductDetail)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Header */ \"(ssr)/./src/components/Header.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Footer */ \"(ssr)/./src/components/Footer.tsx\");\n/* harmony import */ var _lib_data__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/data */ \"(ssr)/./src/lib/data.ts\");\n/* harmony import */ var _components_Icons__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/Icons */ \"(ssr)/./src/components/Icons.tsx\");\n/* harmony import */ var _context_UserContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/context/UserContext */ \"(ssr)/./src/context/UserContext.tsx\");\n/* harmony import */ var _components_RecentlyViewed__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/RecentlyViewed */ \"(ssr)/./src/components/RecentlyViewed.tsx\");\n/* harmony import */ var _components_ProductCard__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ProductCard */ \"(ssr)/./src/components/ProductCard.tsx\");\n/* harmony import */ var _components_SafetyTips__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/SafetyTips */ \"(ssr)/./src/components/SafetyTips.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n // Added import\n\nfunction ProductDetail() {\n    const { id } = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useParams)();\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [cart, setCart] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [selectedImageIndex, setSelectedImageIndex] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const { addToRecentlyViewed, addToWishlist, removeFromWishlist, isInWishlist } = (0,_context_UserContext__WEBPACK_IMPORTED_MODULE_7__.useUser)();\n    const product = _lib_data__WEBPACK_IMPORTED_MODULE_5__.products.find((p)=>p.id === Number(id));\n    // Mock product images array - in a real app, this would come from the product data\n    const productImages = [\n        product?.image,\n        \"https://placehold.co/800x600/3b82f6/ffffff?text=Product+Image+2\",\n        \"https://placehold.co/800x600/10b981/ffffff?text=Product+Image+3\",\n        \"https://placehold.co/800x600/8b5cf6/ffffff?text=Product+Image+4\"\n    ].filter(Boolean);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (product) {\n            // Load existing recently viewed products\n            const stored = localStorage.getItem(\"recentlyViewed\");\n            let recentProducts = [];\n            if (stored) {\n                try {\n                    recentProducts = JSON.parse(stored);\n                } catch (error) {\n                    console.error(\"Error parsing recently viewed products:\", error);\n                }\n            }\n            // Remove the current product if it exists\n            recentProducts = recentProducts.filter((p)=>p.id !== product.id);\n            // Add the current product to the beginning\n            recentProducts.unshift(product);\n            // Keep only the most recent 10 products\n            recentProducts = recentProducts.slice(0, 10);\n            // Save back to localStorage\n            localStorage.setItem(\"recentlyViewed\", JSON.stringify(recentProducts));\n        }\n    }, [\n        product\n    ]);\n    const handlePreviousImage = ()=>{\n        setSelectedImageIndex((prev)=>prev === 0 ? productImages.length - 1 : prev - 1);\n    };\n    const handleNextImage = ()=>{\n        setSelectedImageIndex((prev)=>prev === productImages.length - 1 ? 0 : prev + 1);\n    };\n    const findSimilarItems = (currentProduct)=>{\n        if (!currentProduct) return [];\n        return _lib_data__WEBPACK_IMPORTED_MODULE_5__.products.filter((p)=>p.id !== currentProduct.id && (p.category === currentProduct.category || p.condition === currentProduct.condition || p.name.split(\" \")[0] === currentProduct.name.split(\" \")[0])).slice(0, 4); // Show up to 4 similar items\n    };\n    if (!product) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    searchQuery: searchQuery,\n                    setSearchQuery: setSearchQuery,\n                    isMenuOpen: isMenuOpen,\n                    setIsMenuOpen: setIsMenuOpen\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"container mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold text-center\",\n                        children: \"Product not found\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                    lineNumber: 88,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    categories: _lib_data__WEBPACK_IMPORTED_MODULE_5__.categories\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n            lineNumber: 81,\n            columnNumber: 7\n        }, this);\n    }\n    const handleWishlistClick = ()=>{\n        if (isInWishlist(product.id)) {\n            removeFromWishlist(product.id);\n        } else {\n            addToWishlist(product);\n        }\n    };\n    const getAvailabilityColor = (status)=>{\n        switch(status){\n            case \"Available\":\n                return \"bg-green-100 text-green-700\";\n            case \"Sold\":\n                return \"bg-red-100 text-red-700\";\n            case \"Under Contract\":\n                return \"bg-yellow-100 text-yellow-700\";\n            default:\n                return \"bg-slate-100 text-slate-700\";\n        }\n    };\n    const getConditionColor = (condition)=>{\n        switch(condition?.toLowerCase()){\n            case \"new\":\n                return \"bg-green-100 text-green-700\";\n            case \"like new\":\n                return \"bg-emerald-100 text-emerald-700\";\n            case \"good\":\n                return \"bg-blue-100 text-blue-700\";\n            case \"fair\":\n                return \"bg-yellow-100 text-yellow-700\";\n            case \"poor\":\n                return \"bg-red-100 text-red-700\";\n            default:\n                return \"bg-slate-100 text-slate-700\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                searchQuery: searchQuery,\n                setSearchQuery: setSearchQuery,\n                isMenuOpen: isMenuOpen,\n                setIsMenuOpen: setIsMenuOpen\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                lineNumber: 136,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"container mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-8 mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-xl shadow-sm overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: productImages[selectedImageIndex],\n                                                    alt: product.name,\n                                                    className: \"w-full h-96 object-cover\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 148,\n                                                    columnNumber: 17\n                                                }, this),\n                                                productImages.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: handlePreviousImage,\n                                                            className: \"absolute left-2 top-1/2 -translate-y-1/2 p-2 rounded-full bg-white/80 hover:bg-white text-slate-600 transition-colors\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Icons__WEBPACK_IMPORTED_MODULE_6__.ChevronLeftIcon, {\n                                                                className: \"w-6 h-6\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                lineNumber: 159,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 155,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: handleNextImage,\n                                                            className: \"absolute right-2 top-1/2 -translate-y-1/2 p-2 rounded-full bg-white/80 hover:bg-white text-slate-600 transition-colors\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Icons__WEBPACK_IMPORTED_MODULE_6__.ChevronRightIcon, {\n                                                                className: \"w-6 h-6\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                lineNumber: 165,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 161,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-4 flex gap-2 overflow-x-auto\",\n                                            children: productImages.map((image, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setSelectedImageIndex(index),\n                                                    className: `flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden border-2 transition-colors ${selectedImageIndex === index ? \"border-indigo-600\" : \"border-transparent hover:border-slate-200\"}`,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: image,\n                                                        alt: `${product.name} - Image ${index + 1}`,\n                                                        className: \"w-full h-full object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                        lineNumber: 181,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, index, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-xl shadow-sm p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-start mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-3xl font-bold\",\n                                                    children: product.name\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handleWishlistClick,\n                                                    className: `p-2 rounded-full transition-colors ${isInWishlist(product.id) ? \"bg-red-500 text-white\" : \"bg-slate-100 text-slate-600 hover:bg-slate-200\"}`,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Icons__WEBPACK_IMPORTED_MODULE_6__.HeartIcon, {\n                                                        className: \"w-6 h-6\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                        lineNumber: 203,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 195,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-indigo-600 mb-4\",\n                                            children: product.price\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2 mb-6\",\n                                            children: [\n                                                product.availability && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: `px-3 py-1 rounded-full text-sm font-medium ${getAvailabilityColor(product.availability)}`,\n                                                    children: product.availability\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 210,\n                                                    columnNumber: 19\n                                                }, this),\n                                                product.condition && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: `px-3 py-1 rounded-full text-sm font-medium ${getConditionColor(product.condition)}`,\n                                                    children: product.condition\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4 mb-6\",\n                                            children: [\n                                                product.category && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center text-slate-600\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium w-24\",\n                                                            children: \"Category:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 224,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: product.category\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 225,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 223,\n                                                    columnNumber: 19\n                                                }, this),\n                                                product.location && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center text-slate-600\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Icons__WEBPACK_IMPORTED_MODULE_6__.LocationIcon, {\n                                                            className: \"w-5 h-5 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 230,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: product.location\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 231,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 229,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                            lineNumber: 221,\n                                            columnNumber: 15\n                                        }, this),\n                                        product.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-xl font-semibold mb-2\",\n                                                    children: \"Description\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 238,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"prose prose-slate max-w-none\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-slate-600 whitespace-pre-line\",\n                                                        children: product.description\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                        lineNumber: 240,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-xl font-semibold mb-2\",\n                                                    children: \"Product Details\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 247,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-slate-50 p-3 rounded-lg\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-slate-500\",\n                                                                    children: \"Brand\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                    lineNumber: 250,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: product.brand || \"Not specified\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                    lineNumber: 251,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 249,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-slate-50 p-3 rounded-lg\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-slate-500\",\n                                                                    children: \"Model\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                    lineNumber: 254,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: product.model || \"Not specified\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                    lineNumber: 255,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 253,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-slate-50 p-3 rounded-lg\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-slate-500\",\n                                                                    children: \"Year\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                    lineNumber: 258,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: product.year || \"Not specified\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                    lineNumber: 259,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 257,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-slate-50 p-3 rounded-lg\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-slate-500\",\n                                                                    children: \"Warranty\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                    lineNumber: 262,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: product.warranty || \"Not specified\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                                    lineNumber: 263,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 261,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 248,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"flex-1 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition\",\n                                                    children: \"Contact Seller\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 269,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"flex items-center justify-center gap-2 px-6 py-3 bg-white border border-slate-200 rounded-lg hover:bg-slate-50 transition\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Icons__WEBPACK_IMPORTED_MODULE_6__.ChatIcon, {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 273,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Message\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                            lineNumber: 274,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                            lineNumber: 144,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                            className: \"mb-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SafetyTips__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                lineNumber: 282,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                            lineNumber: 281,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                            className: \"mb-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-xl shadow-sm p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold mb-4\",\n                                        children: \"Reviews & Feedback\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8 text-gray-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Reviews are available for products in the database.\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm mt-2\",\n                                                children: \"This is a demo product using mock data.\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                                lineNumber: 291,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                lineNumber: 287,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                            lineNumber: 286,\n                            columnNumber: 11\n                        }, this),\n                        product && findSimilarItems(product).length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                            className: \"mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold mb-6\",\n                                    children: \"Similar Items\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                    lineNumber: 299,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6\",\n                                    children: findSimilarItems(product).map((similarProduct)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProductCard__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            product: similarProduct\n                                        }, similarProduct.id, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                            lineNumber: 298,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_RecentlyViewed__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                            lineNumber: 308,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                    lineNumber: 143,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                lineNumber: 142,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                categories: _lib_data__WEBPACK_IMPORTED_MODULE_5__.categories\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n                lineNumber: 311,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx\",\n        lineNumber: 135,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL3Byb2R1Y3QvW2lkXS9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFNEM7QUFDQTtBQUNIO0FBQ0E7QUFDUztBQUNrRTtBQUNwRTtBQUNTO0FBQ04sQ0FBQyxlQUFlO0FBQ2xCO0FBR2xDLFNBQVNnQjtJQUN0QixNQUFNLEVBQUVDLEVBQUUsRUFBRSxHQUFHakIsMERBQVNBO0lBQ3hCLE1BQU0sQ0FBQ2tCLGFBQWFDLGVBQWUsR0FBR2xCLCtDQUFRQSxDQUFDO0lBQy9DLE1BQU0sQ0FBQ21CLE1BQU1DLFFBQVEsR0FBR3BCLCtDQUFRQSxDQUFZLEVBQUU7SUFDOUMsTUFBTSxDQUFDcUIsWUFBWUMsY0FBYyxHQUFHdEIsK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxDQUFDdUIsb0JBQW9CQyxzQkFBc0IsR0FBR3hCLCtDQUFRQSxDQUFDO0lBQzdELE1BQU0sRUFBRXlCLG1CQUFtQixFQUFFQyxhQUFhLEVBQUVDLGtCQUFrQixFQUFFQyxZQUFZLEVBQUUsR0FBR2pCLDZEQUFPQTtJQUV4RixNQUFNa0IsVUFBVXpCLCtDQUFRQSxDQUFDMEIsSUFBSSxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFZixFQUFFLEtBQUtnQixPQUFPaEI7SUFFbkQsbUZBQW1GO0lBQ25GLE1BQU1pQixnQkFBZ0I7UUFDcEJKLFNBQVNLO1FBQ1Q7UUFDQTtRQUNBO0tBQ0QsQ0FBQ0MsTUFBTSxDQUFDQztJQUVUbkMsZ0RBQVNBLENBQUM7UUFDUixJQUFJNEIsU0FBUztZQUNYLHlDQUF5QztZQUN6QyxNQUFNUSxTQUFTQyxhQUFhQyxPQUFPLENBQUM7WUFDcEMsSUFBSUMsaUJBQTRCLEVBQUU7WUFFbEMsSUFBSUgsUUFBUTtnQkFDVixJQUFJO29CQUNGRyxpQkFBaUJDLEtBQUtDLEtBQUssQ0FBQ0w7Z0JBQzlCLEVBQUUsT0FBT00sT0FBTztvQkFDZEMsUUFBUUQsS0FBSyxDQUFDLDJDQUEyQ0E7Z0JBQzNEO1lBQ0Y7WUFFQSwwQ0FBMEM7WUFDMUNILGlCQUFpQkEsZUFBZUwsTUFBTSxDQUFDSixDQUFBQSxJQUFLQSxFQUFFZixFQUFFLEtBQUthLFFBQVFiLEVBQUU7WUFFL0QsMkNBQTJDO1lBQzNDd0IsZUFBZUssT0FBTyxDQUFDaEI7WUFFdkIsd0NBQXdDO1lBQ3hDVyxpQkFBaUJBLGVBQWVNLEtBQUssQ0FBQyxHQUFHO1lBRXpDLDRCQUE0QjtZQUM1QlIsYUFBYVMsT0FBTyxDQUFDLGtCQUFrQk4sS0FBS08sU0FBUyxDQUFDUjtRQUN4RDtJQUNGLEdBQUc7UUFBQ1g7S0FBUTtJQUVaLE1BQU1vQixzQkFBc0I7UUFDMUJ6QixzQkFBc0IsQ0FBQzBCLE9BQVVBLFNBQVMsSUFBSWpCLGNBQWNrQixNQUFNLEdBQUcsSUFBSUQsT0FBTztJQUNsRjtJQUVBLE1BQU1FLGtCQUFrQjtRQUN0QjVCLHNCQUFzQixDQUFDMEIsT0FBVUEsU0FBU2pCLGNBQWNrQixNQUFNLEdBQUcsSUFBSSxJQUFJRCxPQUFPO0lBQ2xGO0lBRUEsTUFBTUcsbUJBQW1CLENBQUNDO1FBQ3hCLElBQUksQ0FBQ0EsZ0JBQWdCLE9BQU8sRUFBRTtRQUM5QixPQUFPbEQsK0NBQVFBLENBQ1orQixNQUFNLENBQUNKLENBQUFBLElBQ05BLEVBQUVmLEVBQUUsS0FBS3NDLGVBQWV0QyxFQUFFLElBQ3pCZSxDQUFBQSxFQUFFd0IsUUFBUSxLQUFLRCxlQUFlQyxRQUFRLElBQUl4QixFQUFFeUIsU0FBUyxLQUFLRixlQUFlRSxTQUFTLElBQUl6QixFQUFFMEIsSUFBSSxDQUFDQyxLQUFLLENBQUMsSUFBSSxDQUFDLEVBQUUsS0FBS0osZUFBZUcsSUFBSSxDQUFDQyxLQUFLLENBQUMsSUFBSSxDQUFDLEVBQUUsR0FFbEpaLEtBQUssQ0FBQyxHQUFHLElBQUksNkJBQTZCO0lBQy9DO0lBRUEsSUFBSSxDQUFDakIsU0FBUztRQUNaLHFCQUNFLDhEQUFDOEI7WUFBSUMsV0FBVTs7OEJBQ2IsOERBQUMxRCwwREFBTUE7b0JBQ0xlLGFBQWFBO29CQUNiQyxnQkFBZ0JBO29CQUNoQkcsWUFBWUE7b0JBQ1pDLGVBQWVBOzs7Ozs7OEJBRWpCLDhEQUFDdUM7b0JBQUtELFdBQVU7OEJBQ2QsNEVBQUNFO3dCQUFHRixXQUFVO2tDQUFpQzs7Ozs7Ozs7Ozs7OEJBRWpELDhEQUFDekQsMERBQU1BO29CQUFDRSxZQUFZQSxpREFBVUE7Ozs7Ozs7Ozs7OztJQUdwQztJQUVBLE1BQU0wRCxzQkFBc0I7UUFDMUIsSUFBSW5DLGFBQWFDLFFBQVFiLEVBQUUsR0FBRztZQUM1QlcsbUJBQW1CRSxRQUFRYixFQUFFO1FBQy9CLE9BQU87WUFDTFUsY0FBY0c7UUFDaEI7SUFDRjtJQUVBLE1BQU1tQyx1QkFBdUIsQ0FBQ0M7UUFDNUIsT0FBUUE7WUFDTixLQUFLO2dCQUNILE9BQU87WUFDVCxLQUFLO2dCQUNILE9BQU87WUFDVCxLQUFLO2dCQUNILE9BQU87WUFDVDtnQkFDRSxPQUFPO1FBQ1g7SUFDRjtJQUVBLE1BQU1DLG9CQUFvQixDQUFDVjtRQUN6QixPQUFRQSxXQUFXVztZQUNqQixLQUFLO2dCQUNILE9BQU87WUFDVCxLQUFLO2dCQUNILE9BQU87WUFDVCxLQUFLO2dCQUNILE9BQU87WUFDVCxLQUFLO2dCQUNILE9BQU87WUFDVCxLQUFLO2dCQUNILE9BQU87WUFDVDtnQkFDRSxPQUFPO1FBQ1g7SUFDRjtJQUVBLHFCQUNFLDhEQUFDUjtRQUFJQyxXQUFVOzswQkFDYiw4REFBQzFELDBEQUFNQTtnQkFDTGUsYUFBYUE7Z0JBQ2JDLGdCQUFnQkE7Z0JBQ2hCRyxZQUFZQTtnQkFDWkMsZUFBZUE7Ozs7OzswQkFFakIsOERBQUN1QztnQkFBS0QsV0FBVTswQkFDZCw0RUFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBRWIsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDUTtvREFDQ0MsS0FBS3BDLGFBQWEsQ0FBQ1YsbUJBQW1CO29EQUN0QytDLEtBQUt6QyxRQUFRNEIsSUFBSTtvREFDakJHLFdBQVU7Ozs7OztnREFFWDNCLGNBQWNrQixNQUFNLEdBQUcsbUJBQ3RCOztzRUFDRSw4REFBQ29COzREQUNDQyxTQUFTdkI7NERBQ1RXLFdBQVU7c0VBRVYsNEVBQUNuRCw4REFBZUE7Z0VBQUNtRCxXQUFVOzs7Ozs7Ozs7OztzRUFFN0IsOERBQUNXOzREQUNDQyxTQUFTcEI7NERBQ1RRLFdBQVU7c0VBRVYsNEVBQUNsRCwrREFBZ0JBO2dFQUFDa0QsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzREFLcEMsOERBQUNEOzRDQUFJQyxXQUFVO3NEQUNaM0IsY0FBY3dDLEdBQUcsQ0FBQyxDQUFDdkMsT0FBT3dDLHNCQUN6Qiw4REFBQ0g7b0RBRUNDLFNBQVMsSUFBTWhELHNCQUFzQmtEO29EQUNyQ2QsV0FBVyxDQUFDLDhFQUE4RSxFQUN4RnJDLHVCQUF1Qm1ELFFBQ25CLHNCQUNBLDRDQUNMLENBQUM7OERBRUYsNEVBQUNOO3dEQUNDQyxLQUFLbkM7d0RBQ0xvQyxLQUFLLENBQUMsRUFBRXpDLFFBQVE0QixJQUFJLENBQUMsU0FBUyxFQUFFaUIsUUFBUSxFQUFFLENBQUM7d0RBQzNDZCxXQUFVOzs7Ozs7bURBWFBjOzs7Ozs7Ozs7Ozs7Ozs7OzhDQW1CYiw4REFBQ2Y7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNFO29EQUFHRixXQUFVOzhEQUFzQi9CLFFBQVE0QixJQUFJOzs7Ozs7OERBQ2hELDhEQUFDYztvREFDQ0MsU0FBU1Q7b0RBQ1RILFdBQVcsQ0FBQyxtQ0FBbUMsRUFDN0NoQyxhQUFhQyxRQUFRYixFQUFFLElBQ25CLDBCQUNBLGlEQUNMLENBQUM7OERBRUYsNEVBQUNWLHdEQUFTQTt3REFBQ3NELFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7O3NEQUd6Qiw4REFBQzdCOzRDQUFFNkIsV0FBVTtzREFBMkMvQixRQUFROEMsS0FBSzs7Ozs7O3NEQUVyRSw4REFBQ2hCOzRDQUFJQyxXQUFVOztnREFDWi9CLFFBQVErQyxZQUFZLGtCQUNuQiw4REFBQ2pCO29EQUFJQyxXQUFXLENBQUMsMkNBQTJDLEVBQUVJLHFCQUFxQm5DLFFBQVErQyxZQUFZLEVBQUUsQ0FBQzs4REFDdkcvQyxRQUFRK0MsWUFBWTs7Ozs7O2dEQUd4Qi9DLFFBQVEyQixTQUFTLGtCQUNoQiw4REFBQ0c7b0RBQUlDLFdBQVcsQ0FBQywyQ0FBMkMsRUFBRU0sa0JBQWtCckMsUUFBUTJCLFNBQVMsRUFBRSxDQUFDOzhEQUNqRzNCLFFBQVEyQixTQUFTOzs7Ozs7Ozs7Ozs7c0RBS3hCLDhEQUFDRzs0Q0FBSUMsV0FBVTs7Z0RBQ1ovQixRQUFRMEIsUUFBUSxrQkFDZiw4REFBQ0k7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDaUI7NERBQUtqQixXQUFVO3NFQUFtQjs7Ozs7O3NFQUNuQyw4REFBQ2lCO3NFQUFNaEQsUUFBUTBCLFFBQVE7Ozs7Ozs7Ozs7OztnREFHMUIxQixRQUFRaUQsUUFBUSxrQkFDZiw4REFBQ25CO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ3BELDJEQUFZQTs0REFBQ29ELFdBQVU7Ozs7OztzRUFDeEIsOERBQUNpQjtzRUFBTWhELFFBQVFpRCxRQUFROzs7Ozs7Ozs7Ozs7Ozs7Ozs7d0NBSzVCakQsUUFBUWtELFdBQVcsa0JBQ2xCLDhEQUFDcEI7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDb0I7b0RBQUdwQixXQUFVOzhEQUE2Qjs7Ozs7OzhEQUMzQyw4REFBQ0Q7b0RBQUlDLFdBQVU7OERBQ2IsNEVBQUM3Qjt3REFBRTZCLFdBQVU7a0VBQXNDL0IsUUFBUWtELFdBQVc7Ozs7Ozs7Ozs7Ozs7Ozs7O3NEQU01RSw4REFBQ3BCOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ29CO29EQUFHcEIsV0FBVTs4REFBNkI7Ozs7Ozs4REFDM0MsOERBQUNEO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ0Q7NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDN0I7b0VBQUU2QixXQUFVOzhFQUF5Qjs7Ozs7OzhFQUN0Qyw4REFBQzdCO29FQUFFNkIsV0FBVTs4RUFBZS9CLFFBQVFvRCxLQUFLLElBQUk7Ozs7Ozs7Ozs7OztzRUFFL0MsOERBQUN0Qjs0REFBSUMsV0FBVTs7OEVBQ2IsOERBQUM3QjtvRUFBRTZCLFdBQVU7OEVBQXlCOzs7Ozs7OEVBQ3RDLDhEQUFDN0I7b0VBQUU2QixXQUFVOzhFQUFlL0IsUUFBUXFELEtBQUssSUFBSTs7Ozs7Ozs7Ozs7O3NFQUUvQyw4REFBQ3ZCOzREQUFJQyxXQUFVOzs4RUFDYiw4REFBQzdCO29FQUFFNkIsV0FBVTs4RUFBeUI7Ozs7Ozs4RUFDdEMsOERBQUM3QjtvRUFBRTZCLFdBQVU7OEVBQWUvQixRQUFRc0QsSUFBSSxJQUFJOzs7Ozs7Ozs7Ozs7c0VBRTlDLDhEQUFDeEI7NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDN0I7b0VBQUU2QixXQUFVOzhFQUF5Qjs7Ozs7OzhFQUN0Qyw4REFBQzdCO29FQUFFNkIsV0FBVTs4RUFBZS9CLFFBQVF1RCxRQUFRLElBQUk7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzREFLdEQsOERBQUN6Qjs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNXO29EQUFPWCxXQUFVOzhEQUFpRjs7Ozs7OzhEQUduRyw4REFBQ1c7b0RBQU9YLFdBQVU7O3NFQUNoQiw4REFBQ3JELHVEQUFRQTs0REFBQ3FELFdBQVU7Ozs7OztzRUFDcEIsOERBQUNpQjtzRUFBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQU9kLDhEQUFDUTs0QkFBUXpCLFdBQVU7c0NBQ2pCLDRFQUFDOUMsK0RBQVVBOzs7Ozs7Ozs7O3NDQUliLDhEQUFDdUU7NEJBQVF6QixXQUFVO3NDQUNqQiw0RUFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDb0I7d0NBQUdwQixXQUFVO2tEQUEwQjs7Ozs7O2tEQUN4Qyw4REFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDN0I7MERBQUU7Ozs7OzswREFDSCw4REFBQ0E7Z0RBQUU2QixXQUFVOzBEQUFlOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozt3QkFNakMvQixXQUFXd0IsaUJBQWlCeEIsU0FBU3NCLE1BQU0sR0FBRyxtQkFDN0MsOERBQUNrQzs0QkFBUXpCLFdBQVU7OzhDQUNqQiw4REFBQ29CO29DQUFHcEIsV0FBVTs4Q0FBMEI7Ozs7Ozs4Q0FDeEMsOERBQUNEO29DQUFJQyxXQUFVOzhDQUNaUCxpQkFBaUJ4QixTQUFTNEMsR0FBRyxDQUFDLENBQUNhLCtCQUM5Qiw4REFBQ3pFLCtEQUFXQTs0Q0FBeUJnQixTQUFTeUQ7MkNBQTVCQSxlQUFldEUsRUFBRTs7Ozs7Ozs7Ozs7Ozs7OztzQ0FNM0MsOERBQUNKLGtFQUFjQTs7Ozs7Ozs7Ozs7Ozs7OzswQkFHbkIsOERBQUNULDBEQUFNQTtnQkFBQ0UsWUFBWUEsaURBQVVBOzs7Ozs7Ozs7Ozs7QUFHcEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJrZXRwbGFjZS8uL3NyYy9hcHAvcHJvZHVjdC9baWRdL3BhZ2UudHN4P2FiZjYiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyB1c2VQYXJhbXMgfSBmcm9tICduZXh0L25hdmlnYXRpb24nO1xuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCBIZWFkZXIgZnJvbSAnQC9jb21wb25lbnRzL0hlYWRlcic7XG5pbXBvcnQgRm9vdGVyIGZyb20gJ0AvY29tcG9uZW50cy9Gb290ZXInO1xuaW1wb3J0IHsgcHJvZHVjdHMsIGNhdGVnb3JpZXMgfSBmcm9tICdAL2xpYi9kYXRhJztcbmltcG9ydCB7IFN0YXJJY29uLCBIZWFydEljb24sIENoYXRJY29uLCBMb2NhdGlvbkljb24sIENoZXZyb25MZWZ0SWNvbiwgQ2hldnJvblJpZ2h0SWNvbiB9IGZyb20gJ0AvY29tcG9uZW50cy9JY29ucyc7XG5pbXBvcnQgeyB1c2VVc2VyIH0gZnJvbSAnQC9jb250ZXh0L1VzZXJDb250ZXh0JztcbmltcG9ydCBSZWNlbnRseVZpZXdlZCBmcm9tICdAL2NvbXBvbmVudHMvUmVjZW50bHlWaWV3ZWQnO1xuaW1wb3J0IFByb2R1Y3RDYXJkIGZyb20gJ0AvY29tcG9uZW50cy9Qcm9kdWN0Q2FyZCc7IC8vIEFkZGVkIGltcG9ydFxuaW1wb3J0IFNhZmV0eVRpcHMgZnJvbSAnQC9jb21wb25lbnRzL1NhZmV0eVRpcHMnO1xuaW1wb3J0IHsgUHJvZHVjdCB9IGZyb20gJ0AvdHlwZXMnO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBQcm9kdWN0RGV0YWlsKCkge1xuICBjb25zdCB7IGlkIH0gPSB1c2VQYXJhbXMoKTtcbiAgY29uc3QgW3NlYXJjaFF1ZXJ5LCBzZXRTZWFyY2hRdWVyeV0gPSB1c2VTdGF0ZSgnJyk7XG4gIGNvbnN0IFtjYXJ0LCBzZXRDYXJ0XSA9IHVzZVN0YXRlPFByb2R1Y3RbXT4oW10pO1xuICBjb25zdCBbaXNNZW51T3Blbiwgc2V0SXNNZW51T3Blbl0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtzZWxlY3RlZEltYWdlSW5kZXgsIHNldFNlbGVjdGVkSW1hZ2VJbmRleF0gPSB1c2VTdGF0ZSgwKTtcbiAgY29uc3QgeyBhZGRUb1JlY2VudGx5Vmlld2VkLCBhZGRUb1dpc2hsaXN0LCByZW1vdmVGcm9tV2lzaGxpc3QsIGlzSW5XaXNobGlzdCB9ID0gdXNlVXNlcigpO1xuXG4gIGNvbnN0IHByb2R1Y3QgPSBwcm9kdWN0cy5maW5kKHAgPT4gcC5pZCA9PT0gTnVtYmVyKGlkKSk7XG5cbiAgLy8gTW9jayBwcm9kdWN0IGltYWdlcyBhcnJheSAtIGluIGEgcmVhbCBhcHAsIHRoaXMgd291bGQgY29tZSBmcm9tIHRoZSBwcm9kdWN0IGRhdGFcbiAgY29uc3QgcHJvZHVjdEltYWdlcyA9IFtcbiAgICBwcm9kdWN0Py5pbWFnZSxcbiAgICAnaHR0cHM6Ly9wbGFjZWhvbGQuY28vODAweDYwMC8zYjgyZjYvZmZmZmZmP3RleHQ9UHJvZHVjdCtJbWFnZSsyJyxcbiAgICAnaHR0cHM6Ly9wbGFjZWhvbGQuY28vODAweDYwMC8xMGI5ODEvZmZmZmZmP3RleHQ9UHJvZHVjdCtJbWFnZSszJyxcbiAgICAnaHR0cHM6Ly9wbGFjZWhvbGQuY28vODAweDYwMC84YjVjZjYvZmZmZmZmP3RleHQ9UHJvZHVjdCtJbWFnZSs0JyxcbiAgXS5maWx0ZXIoQm9vbGVhbikgYXMgc3RyaW5nW107XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAocHJvZHVjdCkge1xuICAgICAgLy8gTG9hZCBleGlzdGluZyByZWNlbnRseSB2aWV3ZWQgcHJvZHVjdHNcbiAgICAgIGNvbnN0IHN0b3JlZCA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdyZWNlbnRseVZpZXdlZCcpO1xuICAgICAgbGV0IHJlY2VudFByb2R1Y3RzOiBQcm9kdWN0W10gPSBbXTtcblxuICAgICAgaWYgKHN0b3JlZCkge1xuICAgICAgICB0cnkge1xuICAgICAgICAgIHJlY2VudFByb2R1Y3RzID0gSlNPTi5wYXJzZShzdG9yZWQpO1xuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHBhcnNpbmcgcmVjZW50bHkgdmlld2VkIHByb2R1Y3RzOicsIGVycm9yKTtcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICAvLyBSZW1vdmUgdGhlIGN1cnJlbnQgcHJvZHVjdCBpZiBpdCBleGlzdHNcbiAgICAgIHJlY2VudFByb2R1Y3RzID0gcmVjZW50UHJvZHVjdHMuZmlsdGVyKHAgPT4gcC5pZCAhPT0gcHJvZHVjdC5pZCk7XG5cbiAgICAgIC8vIEFkZCB0aGUgY3VycmVudCBwcm9kdWN0IHRvIHRoZSBiZWdpbm5pbmdcbiAgICAgIHJlY2VudFByb2R1Y3RzLnVuc2hpZnQocHJvZHVjdCk7XG5cbiAgICAgIC8vIEtlZXAgb25seSB0aGUgbW9zdCByZWNlbnQgMTAgcHJvZHVjdHNcbiAgICAgIHJlY2VudFByb2R1Y3RzID0gcmVjZW50UHJvZHVjdHMuc2xpY2UoMCwgMTApO1xuXG4gICAgICAvLyBTYXZlIGJhY2sgdG8gbG9jYWxTdG9yYWdlXG4gICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgncmVjZW50bHlWaWV3ZWQnLCBKU09OLnN0cmluZ2lmeShyZWNlbnRQcm9kdWN0cykpO1xuICAgIH1cbiAgfSwgW3Byb2R1Y3RdKTtcblxuICBjb25zdCBoYW5kbGVQcmV2aW91c0ltYWdlID0gKCkgPT4ge1xuICAgIHNldFNlbGVjdGVkSW1hZ2VJbmRleCgocHJldikgPT4gKHByZXYgPT09IDAgPyBwcm9kdWN0SW1hZ2VzLmxlbmd0aCAtIDEgOiBwcmV2IC0gMSkpO1xuICB9O1xuXG4gIGNvbnN0IGhhbmRsZU5leHRJbWFnZSA9ICgpID0+IHtcbiAgICBzZXRTZWxlY3RlZEltYWdlSW5kZXgoKHByZXYpID0+IChwcmV2ID09PSBwcm9kdWN0SW1hZ2VzLmxlbmd0aCAtIDEgPyAwIDogcHJldiArIDEpKTtcbiAgfTtcblxuICBjb25zdCBmaW5kU2ltaWxhckl0ZW1zID0gKGN1cnJlbnRQcm9kdWN0OiBQcm9kdWN0KSA9PiB7XG4gICAgaWYgKCFjdXJyZW50UHJvZHVjdCkgcmV0dXJuIFtdO1xuICAgIHJldHVybiBwcm9kdWN0c1xuICAgICAgLmZpbHRlcihwID0+XG4gICAgICAgIHAuaWQgIT09IGN1cnJlbnRQcm9kdWN0LmlkICYmXG4gICAgICAgIChwLmNhdGVnb3J5ID09PSBjdXJyZW50UHJvZHVjdC5jYXRlZ29yeSB8fCBwLmNvbmRpdGlvbiA9PT0gY3VycmVudFByb2R1Y3QuY29uZGl0aW9uIHx8IHAubmFtZS5zcGxpdCgnICcpWzBdID09PSBjdXJyZW50UHJvZHVjdC5uYW1lLnNwbGl0KCcgJylbMF0pXG4gICAgICApXG4gICAgICAuc2xpY2UoMCwgNCk7IC8vIFNob3cgdXAgdG8gNCBzaW1pbGFyIGl0ZW1zXG4gIH07XG5cbiAgaWYgKCFwcm9kdWN0KSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWdyYWRpZW50LXRvLWJyIGZyb20tc2xhdGUtNTAgdG8tc2xhdGUtMTAwXCI+XG4gICAgICAgIDxIZWFkZXJcbiAgICAgICAgICBzZWFyY2hRdWVyeT17c2VhcmNoUXVlcnl9XG4gICAgICAgICAgc2V0U2VhcmNoUXVlcnk9e3NldFNlYXJjaFF1ZXJ5fVxuICAgICAgICAgIGlzTWVudU9wZW49e2lzTWVudU9wZW59XG4gICAgICAgICAgc2V0SXNNZW51T3Blbj17c2V0SXNNZW51T3Blbn1cbiAgICAgICAgLz5cbiAgICAgICAgPG1haW4gY2xhc3NOYW1lPVwiY29udGFpbmVyIG14LWF1dG8gcHgtNCBzbTpweC02IGxnOnB4LTggcHktOFwiPlxuICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1jZW50ZXJcIj5Qcm9kdWN0IG5vdCBmb3VuZDwvaDE+XG4gICAgICAgIDwvbWFpbj5cbiAgICAgICAgPEZvb3RlciBjYXRlZ29yaWVzPXtjYXRlZ29yaWVzfSAvPlxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfVxuXG4gIGNvbnN0IGhhbmRsZVdpc2hsaXN0Q2xpY2sgPSAoKSA9PiB7XG4gICAgaWYgKGlzSW5XaXNobGlzdChwcm9kdWN0LmlkKSkge1xuICAgICAgcmVtb3ZlRnJvbVdpc2hsaXN0KHByb2R1Y3QuaWQpO1xuICAgIH0gZWxzZSB7XG4gICAgICBhZGRUb1dpc2hsaXN0KHByb2R1Y3QpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBnZXRBdmFpbGFiaWxpdHlDb2xvciA9IChzdGF0dXM/OiBzdHJpbmcpID0+IHtcbiAgICBzd2l0Y2ggKHN0YXR1cykge1xuICAgICAgY2FzZSAnQXZhaWxhYmxlJzpcbiAgICAgICAgcmV0dXJuICdiZy1ncmVlbi0xMDAgdGV4dC1ncmVlbi03MDAnO1xuICAgICAgY2FzZSAnU29sZCc6XG4gICAgICAgIHJldHVybiAnYmctcmVkLTEwMCB0ZXh0LXJlZC03MDAnO1xuICAgICAgY2FzZSAnVW5kZXIgQ29udHJhY3QnOlxuICAgICAgICByZXR1cm4gJ2JnLXllbGxvdy0xMDAgdGV4dC15ZWxsb3ctNzAwJztcbiAgICAgIGRlZmF1bHQ6XG4gICAgICAgIHJldHVybiAnYmctc2xhdGUtMTAwIHRleHQtc2xhdGUtNzAwJztcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgZ2V0Q29uZGl0aW9uQ29sb3IgPSAoY29uZGl0aW9uPzogc3RyaW5nKSA9PiB7XG4gICAgc3dpdGNoIChjb25kaXRpb24/LnRvTG93ZXJDYXNlKCkpIHtcbiAgICAgIGNhc2UgJ25ldyc6XG4gICAgICAgIHJldHVybiAnYmctZ3JlZW4tMTAwIHRleHQtZ3JlZW4tNzAwJztcbiAgICAgIGNhc2UgJ2xpa2UgbmV3JzpcbiAgICAgICAgcmV0dXJuICdiZy1lbWVyYWxkLTEwMCB0ZXh0LWVtZXJhbGQtNzAwJztcbiAgICAgIGNhc2UgJ2dvb2QnOlxuICAgICAgICByZXR1cm4gJ2JnLWJsdWUtMTAwIHRleHQtYmx1ZS03MDAnO1xuICAgICAgY2FzZSAnZmFpcic6XG4gICAgICAgIHJldHVybiAnYmcteWVsbG93LTEwMCB0ZXh0LXllbGxvdy03MDAnO1xuICAgICAgY2FzZSAncG9vcic6XG4gICAgICAgIHJldHVybiAnYmctcmVkLTEwMCB0ZXh0LXJlZC03MDAnO1xuICAgICAgZGVmYXVsdDpcbiAgICAgICAgcmV0dXJuICdiZy1zbGF0ZS0xMDAgdGV4dC1zbGF0ZS03MDAnO1xuICAgIH1cbiAgfTtcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWdyYWRpZW50LXRvLWJyIGZyb20tc2xhdGUtNTAgdG8tc2xhdGUtMTAwXCI+XG4gICAgICA8SGVhZGVyXG4gICAgICAgIHNlYXJjaFF1ZXJ5PXtzZWFyY2hRdWVyeX1cbiAgICAgICAgc2V0U2VhcmNoUXVlcnk9e3NldFNlYXJjaFF1ZXJ5fVxuICAgICAgICBpc01lbnVPcGVuPXtpc01lbnVPcGVufVxuICAgICAgICBzZXRJc01lbnVPcGVuPXtzZXRJc01lbnVPcGVufVxuICAgICAgLz5cbiAgICAgIDxtYWluIGNsYXNzTmFtZT1cImNvbnRhaW5lciBteC1hdXRvIHB4LTQgc206cHgtNiBsZzpweC04IHB5LThcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy03eGwgbXgtYXV0b1wiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBnYXAtOCBtYi0xMlwiPlxuICAgICAgICAgICAgey8qIEltYWdlIEdhbGxlcnkgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQteGwgc2hhZG93LXNtIG92ZXJmbG93LWhpZGRlblwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlXCI+XG4gICAgICAgICAgICAgICAgPGltZ1xuICAgICAgICAgICAgICAgICAgc3JjPXtwcm9kdWN0SW1hZ2VzW3NlbGVjdGVkSW1hZ2VJbmRleF19XG4gICAgICAgICAgICAgICAgICBhbHQ9e3Byb2R1Y3QubmFtZX1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBoLTk2IG9iamVjdC1jb3ZlclwiXG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICB7cHJvZHVjdEltYWdlcy5sZW5ndGggPiAxICYmIChcbiAgICAgICAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVQcmV2aW91c0ltYWdlfVxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImFic29sdXRlIGxlZnQtMiB0b3AtMS8yIC10cmFuc2xhdGUteS0xLzIgcC0yIHJvdW5kZWQtZnVsbCBiZy13aGl0ZS84MCBob3ZlcjpiZy13aGl0ZSB0ZXh0LXNsYXRlLTYwMCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICA8Q2hldnJvbkxlZnRJY29uIGNsYXNzTmFtZT1cInctNiBoLTZcIiAvPlxuICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZU5leHRJbWFnZX1cbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJhYnNvbHV0ZSByaWdodC0yIHRvcC0xLzIgLXRyYW5zbGF0ZS15LTEvMiBwLTIgcm91bmRlZC1mdWxsIGJnLXdoaXRlLzgwIGhvdmVyOmJnLXdoaXRlIHRleHQtc2xhdGUtNjAwIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgIDxDaGV2cm9uUmlnaHRJY29uIGNsYXNzTmFtZT1cInctNiBoLTZcIiAvPlxuICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNCBmbGV4IGdhcC0yIG92ZXJmbG93LXgtYXV0b1wiPlxuICAgICAgICAgICAgICAgIHtwcm9kdWN0SW1hZ2VzLm1hcCgoaW1hZ2UsIGluZGV4KSA9PiAoXG4gICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgIGtleT17aW5kZXh9XG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNlbGVjdGVkSW1hZ2VJbmRleChpbmRleCl9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YGZsZXgtc2hyaW5rLTAgdy0yMCBoLTIwIHJvdW5kZWQtbGcgb3ZlcmZsb3ctaGlkZGVuIGJvcmRlci0yIHRyYW5zaXRpb24tY29sb3JzICR7XG4gICAgICAgICAgICAgICAgICAgICAgc2VsZWN0ZWRJbWFnZUluZGV4ID09PSBpbmRleFxuICAgICAgICAgICAgICAgICAgICAgICAgPyAnYm9yZGVyLWluZGlnby02MDAnXG4gICAgICAgICAgICAgICAgICAgICAgICA6ICdib3JkZXItdHJhbnNwYXJlbnQgaG92ZXI6Ym9yZGVyLXNsYXRlLTIwMCdcbiAgICAgICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIDxpbWdcbiAgICAgICAgICAgICAgICAgICAgICBzcmM9e2ltYWdlfVxuICAgICAgICAgICAgICAgICAgICAgIGFsdD17YCR7cHJvZHVjdC5uYW1lfSAtIEltYWdlICR7aW5kZXggKyAxfWB9XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGgtZnVsbCBvYmplY3QtY292ZXJcIlxuICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIHsvKiBQcm9kdWN0IEluZm8gKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQteGwgc2hhZG93LXNtIHAtNlwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLXN0YXJ0IG1iLTRcIj5cbiAgICAgICAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC0zeGwgZm9udC1ib2xkXCI+e3Byb2R1Y3QubmFtZX08L2gxPlxuICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZVdpc2hsaXN0Q2xpY2t9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BwLTIgcm91bmRlZC1mdWxsIHRyYW5zaXRpb24tY29sb3JzICR7XG4gICAgICAgICAgICAgICAgICAgIGlzSW5XaXNobGlzdChwcm9kdWN0LmlkKVxuICAgICAgICAgICAgICAgICAgICAgID8gJ2JnLXJlZC01MDAgdGV4dC13aGl0ZSdcbiAgICAgICAgICAgICAgICAgICAgICA6ICdiZy1zbGF0ZS0xMDAgdGV4dC1zbGF0ZS02MDAgaG92ZXI6Ymctc2xhdGUtMjAwJ1xuICAgICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPEhlYXJ0SWNvbiBjbGFzc05hbWU9XCJ3LTYgaC02XCIgLz5cbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWluZGlnby02MDAgbWItNFwiPntwcm9kdWN0LnByaWNlfTwvcD5cblxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC13cmFwIGdhcC0yIG1iLTZcIj5cbiAgICAgICAgICAgICAgICB7cHJvZHVjdC5hdmFpbGFiaWxpdHkgJiYgKFxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2BweC0zIHB5LTEgcm91bmRlZC1mdWxsIHRleHQtc20gZm9udC1tZWRpdW0gJHtnZXRBdmFpbGFiaWxpdHlDb2xvcihwcm9kdWN0LmF2YWlsYWJpbGl0eSl9YH0+XG4gICAgICAgICAgICAgICAgICAgIHtwcm9kdWN0LmF2YWlsYWJpbGl0eX1cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAge3Byb2R1Y3QuY29uZGl0aW9uICYmIChcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgcHgtMyBweS0xIHJvdW5kZWQtZnVsbCB0ZXh0LXNtIGZvbnQtbWVkaXVtICR7Z2V0Q29uZGl0aW9uQ29sb3IocHJvZHVjdC5jb25kaXRpb24pfWB9PlxuICAgICAgICAgICAgICAgICAgICB7cHJvZHVjdC5jb25kaXRpb259XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNCBtYi02XCI+XG4gICAgICAgICAgICAgICAge3Byb2R1Y3QuY2F0ZWdvcnkgJiYgKFxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciB0ZXh0LXNsYXRlLTYwMFwiPlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB3LTI0XCI+Q2F0ZWdvcnk6PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8c3Bhbj57cHJvZHVjdC5jYXRlZ29yeX08L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgIHtwcm9kdWN0LmxvY2F0aW9uICYmIChcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgdGV4dC1zbGF0ZS02MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgPExvY2F0aW9uSWNvbiBjbGFzc05hbWU9XCJ3LTUgaC01IG1yLTJcIiAvPlxuICAgICAgICAgICAgICAgICAgICA8c3Bhbj57cHJvZHVjdC5sb2NhdGlvbn08L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICB7cHJvZHVjdC5kZXNjcmlwdGlvbiAmJiAoXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi02XCI+XG4gICAgICAgICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LXNlbWlib2xkIG1iLTJcIj5EZXNjcmlwdGlvbjwvaDI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInByb3NlIHByb3NlLXNsYXRlIG1heC13LW5vbmVcIj5cbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbGF0ZS02MDAgd2hpdGVzcGFjZS1wcmUtbGluZVwiPntwcm9kdWN0LmRlc2NyaXB0aW9ufTwvcD5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApfVxuXG4gICAgICAgICAgICAgIHsvKiBQcm9kdWN0IERldGFpbHMgKi99XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNlwiPlxuICAgICAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtc2VtaWJvbGQgbWItMlwiPlByb2R1Y3QgRGV0YWlsczwvaDI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0yIGdhcC00XCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXNsYXRlLTUwIHAtMyByb3VuZGVkLWxnXCI+XG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1zbGF0ZS01MDBcIj5CcmFuZDwvcD5cbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW1cIj57cHJvZHVjdC5icmFuZCB8fCAnTm90IHNwZWNpZmllZCd9PC9wPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXNsYXRlLTUwIHAtMyByb3VuZGVkLWxnXCI+XG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1zbGF0ZS01MDBcIj5Nb2RlbDwvcD5cbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW1cIj57cHJvZHVjdC5tb2RlbCB8fCAnTm90IHNwZWNpZmllZCd9PC9wPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXNsYXRlLTUwIHAtMyByb3VuZGVkLWxnXCI+XG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1zbGF0ZS01MDBcIj5ZZWFyPC9wPlxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJmb250LW1lZGl1bVwiPntwcm9kdWN0LnllYXIgfHwgJ05vdCBzcGVjaWZpZWQnfTwvcD5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1zbGF0ZS01MCBwLTMgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtc2xhdGUtNTAwXCI+V2FycmFudHk8L3A+XG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtXCI+e3Byb2R1Y3Qud2FycmFudHkgfHwgJ05vdCBzcGVjaWZpZWQnfTwvcD5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZ2FwLTRcIj5cbiAgICAgICAgICAgICAgICA8YnV0dG9uIGNsYXNzTmFtZT1cImZsZXgtMSBweS0zIGJnLWluZGlnby02MDAgdGV4dC13aGl0ZSByb3VuZGVkLWxnIGhvdmVyOmJnLWluZGlnby03MDAgdHJhbnNpdGlvblwiPlxuICAgICAgICAgICAgICAgICAgQ29udGFjdCBTZWxsZXJcbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICA8YnV0dG9uIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGdhcC0yIHB4LTYgcHktMyBiZy13aGl0ZSBib3JkZXIgYm9yZGVyLXNsYXRlLTIwMCByb3VuZGVkLWxnIGhvdmVyOmJnLXNsYXRlLTUwIHRyYW5zaXRpb25cIj5cbiAgICAgICAgICAgICAgICAgIDxDaGF0SWNvbiBjbGFzc05hbWU9XCJ3LTUgaC01XCIgLz5cbiAgICAgICAgICAgICAgICAgIDxzcGFuPk1lc3NhZ2U8L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogU2FmZXR5IFRpcHMgU2VjdGlvbiAqL31cbiAgICAgICAgICA8c2VjdGlvbiBjbGFzc05hbWU9XCJtYi0xMlwiPlxuICAgICAgICAgICAgPFNhZmV0eVRpcHMgLz5cbiAgICAgICAgICA8L3NlY3Rpb24+XG5cbiAgICAgICAgICB7LyogUmV2aWV3cyBTZWN0aW9uICovfVxuICAgICAgICAgIDxzZWN0aW9uIGNsYXNzTmFtZT1cIm1iLTEyXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQteGwgc2hhZG93LXNtIHAtNlwiPlxuICAgICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIG1iLTRcIj5SZXZpZXdzICYgRmVlZGJhY2s8L2gyPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHB5LTggdGV4dC1ncmF5LTUwMFwiPlxuICAgICAgICAgICAgICAgIDxwPlJldmlld3MgYXJlIGF2YWlsYWJsZSBmb3IgcHJvZHVjdHMgaW4gdGhlIGRhdGFiYXNlLjwvcD5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIG10LTJcIj5UaGlzIGlzIGEgZGVtbyBwcm9kdWN0IHVzaW5nIG1vY2sgZGF0YS48L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9zZWN0aW9uPlxuXG4gICAgICAgICAgey8qIFNpbWlsYXIgSXRlbXMgU2VjdGlvbiAqL31cbiAgICAgICAgICB7cHJvZHVjdCAmJiBmaW5kU2ltaWxhckl0ZW1zKHByb2R1Y3QpLmxlbmd0aCA+IDAgJiYgKFxuICAgICAgICAgICAgPHNlY3Rpb24gY2xhc3NOYW1lPVwibWItMTJcIj5cbiAgICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCBtYi02XCI+U2ltaWxhciBJdGVtczwvaDI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBzbTpncmlkLWNvbHMtMiBtZDpncmlkLWNvbHMtMyBsZzpncmlkLWNvbHMtNCBnYXAtNlwiPlxuICAgICAgICAgICAgICAgIHtmaW5kU2ltaWxhckl0ZW1zKHByb2R1Y3QpLm1hcCgoc2ltaWxhclByb2R1Y3QpID0+IChcbiAgICAgICAgICAgICAgICAgIDxQcm9kdWN0Q2FyZCBrZXk9e3NpbWlsYXJQcm9kdWN0LmlkfSBwcm9kdWN0PXtzaW1pbGFyUHJvZHVjdH0gLz5cbiAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L3NlY3Rpb24+XG4gICAgICAgICAgKX1cblxuICAgICAgICAgIDxSZWNlbnRseVZpZXdlZCAvPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvbWFpbj5cbiAgICAgIDxGb290ZXIgY2F0ZWdvcmllcz17Y2F0ZWdvcmllc30gLz5cbiAgICA8L2Rpdj5cbiAgKTtcbn0iXSwibmFtZXMiOlsidXNlUGFyYW1zIiwidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJIZWFkZXIiLCJGb290ZXIiLCJwcm9kdWN0cyIsImNhdGVnb3JpZXMiLCJIZWFydEljb24iLCJDaGF0SWNvbiIsIkxvY2F0aW9uSWNvbiIsIkNoZXZyb25MZWZ0SWNvbiIsIkNoZXZyb25SaWdodEljb24iLCJ1c2VVc2VyIiwiUmVjZW50bHlWaWV3ZWQiLCJQcm9kdWN0Q2FyZCIsIlNhZmV0eVRpcHMiLCJQcm9kdWN0RGV0YWlsIiwiaWQiLCJzZWFyY2hRdWVyeSIsInNldFNlYXJjaFF1ZXJ5IiwiY2FydCIsInNldENhcnQiLCJpc01lbnVPcGVuIiwic2V0SXNNZW51T3BlbiIsInNlbGVjdGVkSW1hZ2VJbmRleCIsInNldFNlbGVjdGVkSW1hZ2VJbmRleCIsImFkZFRvUmVjZW50bHlWaWV3ZWQiLCJhZGRUb1dpc2hsaXN0IiwicmVtb3ZlRnJvbVdpc2hsaXN0IiwiaXNJbldpc2hsaXN0IiwicHJvZHVjdCIsImZpbmQiLCJwIiwiTnVtYmVyIiwicHJvZHVjdEltYWdlcyIsImltYWdlIiwiZmlsdGVyIiwiQm9vbGVhbiIsInN0b3JlZCIsImxvY2FsU3RvcmFnZSIsImdldEl0ZW0iLCJyZWNlbnRQcm9kdWN0cyIsIkpTT04iLCJwYXJzZSIsImVycm9yIiwiY29uc29sZSIsInVuc2hpZnQiLCJzbGljZSIsInNldEl0ZW0iLCJzdHJpbmdpZnkiLCJoYW5kbGVQcmV2aW91c0ltYWdlIiwicHJldiIsImxlbmd0aCIsImhhbmRsZU5leHRJbWFnZSIsImZpbmRTaW1pbGFySXRlbXMiLCJjdXJyZW50UHJvZHVjdCIsImNhdGVnb3J5IiwiY29uZGl0aW9uIiwibmFtZSIsInNwbGl0IiwiZGl2IiwiY2xhc3NOYW1lIiwibWFpbiIsImgxIiwiaGFuZGxlV2lzaGxpc3RDbGljayIsImdldEF2YWlsYWJpbGl0eUNvbG9yIiwic3RhdHVzIiwiZ2V0Q29uZGl0aW9uQ29sb3IiLCJ0b0xvd2VyQ2FzZSIsImltZyIsInNyYyIsImFsdCIsImJ1dHRvbiIsIm9uQ2xpY2siLCJtYXAiLCJpbmRleCIsInByaWNlIiwiYXZhaWxhYmlsaXR5Iiwic3BhbiIsImxvY2F0aW9uIiwiZGVzY3JpcHRpb24iLCJoMiIsImJyYW5kIiwibW9kZWwiLCJ5ZWFyIiwid2FycmFudHkiLCJzZWN0aW9uIiwic2ltaWxhclByb2R1Y3QiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/app/product/[id]/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/providers.tsx":
/*!*******************************!*\
  !*** ./src/app/providers.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _context_UserContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../context/UserContext */ \"(ssr)/./src/context/UserContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction Providers({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_2__.SessionProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_UserContext__WEBPACK_IMPORTED_MODULE_3__.UserProvider, {\n            children: children\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/providers.tsx\",\n            lineNumber: 10,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/providers.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL3Byb3ZpZGVycy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBRTBCO0FBQ3dCO0FBQ0k7QUFFdkMsU0FBU0csVUFBVSxFQUFFQyxRQUFRLEVBQWlDO0lBQzNFLHFCQUNFLDhEQUFDSCw0REFBZUE7a0JBQ2QsNEVBQUNDLDhEQUFZQTtzQkFDVkU7Ozs7Ozs7Ozs7O0FBSVQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJrZXRwbGFjZS8uL3NyYy9hcHAvcHJvdmlkZXJzLnRzeD85MzI2Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IFNlc3Npb25Qcm92aWRlciB9IGZyb20gJ25leHQtYXV0aC9yZWFjdCc7XG5pbXBvcnQgeyBVc2VyUHJvdmlkZXIgfSBmcm9tICcuLi9jb250ZXh0L1VzZXJDb250ZXh0JztcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUHJvdmlkZXJzKHsgY2hpbGRyZW4gfTogeyBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlIH0pIHtcbiAgcmV0dXJuIChcbiAgICA8U2Vzc2lvblByb3ZpZGVyPlxuICAgICAgPFVzZXJQcm92aWRlcj5cbiAgICAgICAge2NoaWxkcmVufVxuICAgICAgPC9Vc2VyUHJvdmlkZXI+XG4gICAgPC9TZXNzaW9uUHJvdmlkZXI+XG4gICk7XG59ICJdLCJuYW1lcyI6WyJSZWFjdCIsIlNlc3Npb25Qcm92aWRlciIsIlVzZXJQcm92aWRlciIsIlByb3ZpZGVycyIsImNoaWxkcmVuIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/app/providers.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Footer.tsx":
/*!***********************************!*\
  !*** ./src/components/Footer.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction Footer({ categories }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-white border-t border-slate-100\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 sm:px-6 lg:px-8 py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold mb-4\",\n                                    children: \"Categories\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n                                    lineNumber: 17,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: `/categories/${category.slug}`,\n                                                className: \"text-slate-600 hover:text-slate-900\",\n                                                children: category.name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n                                                lineNumber: 21,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, category.id, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n                                            lineNumber: 20,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n                                    lineNumber: 18,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold mb-4\",\n                                    children: \"Quick Links\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n                                    lineNumber: 32,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/products\",\n                                                className: \"text-slate-600 hover:text-slate-900\",\n                                                children: \"All Products\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n                                                lineNumber: 35,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n                                            lineNumber: 34,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/sellers\",\n                                                className: \"text-slate-600 hover:text-slate-900\",\n                                                children: \"Top Sellers\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n                                                lineNumber: 40,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n                                            lineNumber: 39,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/about\",\n                                                className: \"text-slate-600 hover:text-slate-900\",\n                                                children: \"About Us\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n                                                lineNumber: 45,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n                                            lineNumber: 44,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/contact\",\n                                                className: \"text-slate-600 hover:text-slate-900\",\n                                                children: \"Contact\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n                                                lineNumber: 50,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n                                            lineNumber: 49,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n                                    lineNumber: 33,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold mb-4\",\n                                    children: \"Help & Support\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/faqs\",\n                                                className: \"text-slate-600 hover:text-slate-900\",\n                                                children: \"FAQ\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n                                                lineNumber: 60,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n                                            lineNumber: 59,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/shipping\",\n                                                className: \"text-slate-600 hover:text-slate-900\",\n                                                children: \"Shipping Info\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n                                                lineNumber: 65,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/returns\",\n                                                className: \"text-slate-600 hover:text-slate-900\",\n                                                children: \"Returns\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n                                                lineNumber: 70,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/privacy-policy\",\n                                                className: \"text-slate-600 hover:text-slate-900\",\n                                                children: \"Privacy Policy\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n                                                lineNumber: 75,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n                                            lineNumber: 74,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold mb-4\",\n                                    children: \"Newsletter\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-slate-600 mb-4\",\n                                    children: \"Subscribe to get special offers, free giveaways, and once-in-a-lifetime deals.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    className: \"flex gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"email\",\n                                            placeholder: \"Enter your email\",\n                                            className: \"flex-1 px-4 py-2 rounded-lg border border-slate-200 focus:outline-none focus:ring-2 focus:ring-indigo-500\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n                                            lineNumber: 87,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            className: \"px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition\",\n                                            children: \"Subscribe\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n                                            lineNumber: 92,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n                    lineNumber: 15,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-12 pt-8 border-t border-slate-100 text-center text-slate-600\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: [\n                            \"\\xa9 \",\n                            new Date().getFullYear(),\n                            \" Marketplace. All rights reserved.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n                    lineNumber: 101,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n            lineNumber: 14,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Footer.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Header.tsx":
/*!***********************************!*\
  !*** ./src/components/Header.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _Icons__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Icons */ \"(ssr)/./src/components/Icons.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Header({ searchQuery, setSearchQuery, isMenuOpen, setIsMenuOpen, cart = [] }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"bg-white shadow-sm\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between h-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                            href: \"/\",\n                            className: \"text-2xl font-bold text-indigo-600\",\n                            children: \"Marketplace\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Header.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex flex-1 max-w-2xl mx-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative w-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: searchQuery,\n                                        onChange: (e)=>setSearchQuery(e.target.value),\n                                        placeholder: \"Search for properties, vehicles, or gadgets...\",\n                                        className: \"w-full px-4 py-2 pl-10 border border-slate-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Header.tsx\",\n                                        lineNumber: 28,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Icons__WEBPACK_IMPORTED_MODULE_2__.SearchIcon, {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Header.tsx\",\n                                            lineNumber: 36,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Header.tsx\",\n                                        lineNumber: 35,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Header.tsx\",\n                                lineNumber: 27,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Header.tsx\",\n                            lineNumber: 26,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"hidden md:flex items-center space-x-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    href: \"/products\",\n                                    className: \"text-slate-600 hover:text-slate-900\",\n                                    children: \"Products\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Header.tsx\",\n                                    lineNumber: 43,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    href: \"/sellers\",\n                                    className: \"text-slate-600 hover:text-slate-900\",\n                                    children: \"Sellers\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Header.tsx\",\n                                    lineNumber: 46,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Header.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                            className: \"md:hidden p-2 text-slate-600 hover:text-slate-900\",\n                            children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Icons__WEBPACK_IMPORTED_MODULE_2__.CloseIcon, {\n                                className: \"w-6 h-6\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Header.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Icons__WEBPACK_IMPORTED_MODULE_2__.MenuIcon, {\n                                className: \"w-6 h-6\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Header.tsx\",\n                                lineNumber: 59,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Header.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Header.tsx\",\n                    lineNumber: 19,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"md:hidden py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: searchQuery,\n                                onChange: (e)=>setSearchQuery(e.target.value),\n                                placeholder: \"Search for properties, vehicles, or gadgets...\",\n                                className: \"w-full px-4 py-2 pl-10 border border-slate-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Header.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Icons__WEBPACK_IMPORTED_MODULE_2__.SearchIcon, {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Header.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Header.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Header.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Header.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 9\n                }, this),\n                isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"md:hidden py-4 border-t border-slate-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"flex flex-col space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                href: \"/products\",\n                                className: \"text-slate-600 hover:text-slate-900\",\n                                onClick: ()=>setIsMenuOpen(false),\n                                children: \"Products\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Header.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                href: \"/sellers\",\n                                className: \"text-slate-600 hover:text-slate-900\",\n                                onClick: ()=>setIsMenuOpen(false),\n                                children: \"Sellers\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Header.tsx\",\n                                lineNumber: 91,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Header.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Header.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Header.tsx\",\n            lineNumber: 18,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Header.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Icons.tsx":
/*!**********************************!*\
  !*** ./src/components/Icons.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BookmarkIcon: () => (/* binding */ BookmarkIcon),\n/* harmony export */   ChatIcon: () => (/* binding */ ChatIcon),\n/* harmony export */   CheckIcon: () => (/* binding */ CheckIcon),\n/* harmony export */   ChevronDownIcon: () => (/* binding */ ChevronDownIcon),\n/* harmony export */   ChevronLeftIcon: () => (/* binding */ ChevronLeftIcon),\n/* harmony export */   ChevronRightIcon: () => (/* binding */ ChevronRightIcon),\n/* harmony export */   ClockIcon: () => (/* binding */ ClockIcon),\n/* harmony export */   CloseIcon: () => (/* binding */ CloseIcon),\n/* harmony export */   FacebookIcon: () => (/* binding */ FacebookIcon),\n/* harmony export */   FilterIcon: () => (/* binding */ FilterIcon),\n/* harmony export */   FlagIcon: () => (/* binding */ FlagIcon),\n/* harmony export */   HeartIcon: () => (/* binding */ HeartIcon),\n/* harmony export */   IdentificationIcon: () => (/* binding */ IdentificationIcon),\n/* harmony export */   InstagramIcon: () => (/* binding */ InstagramIcon),\n/* harmony export */   LocationIcon: () => (/* binding */ LocationIcon),\n/* harmony export */   MenuIcon: () => (/* binding */ MenuIcon),\n/* harmony export */   PhoneIcon: () => (/* binding */ PhoneIcon),\n/* harmony export */   SearchIcon: () => (/* binding */ SearchIcon),\n/* harmony export */   ShieldCheckIcon: () => (/* binding */ ShieldCheckIcon),\n/* harmony export */   ShoppingCartIcon: () => (/* binding */ ShoppingCartIcon),\n/* harmony export */   SortIcon: () => (/* binding */ SortIcon),\n/* harmony export */   StarIcon: () => (/* binding */ StarIcon),\n/* harmony export */   TagIcon: () => (/* binding */ TagIcon),\n/* harmony export */   TwitterIcon: () => (/* binding */ TwitterIcon),\n/* harmony export */   UserIcon: () => (/* binding */ UserIcon)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst SearchIcon = ({ className = \"w-6 h-6\" })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        className: className,\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        stroke: \"currentColor\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n            lineNumber: 7,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n        lineNumber: 6,\n        columnNumber: 3\n    }, undefined);\nconst ShoppingCartIcon = ({ className = \"h-5 w-5\" })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        className: className,\n        viewBox: \"0 0 20 20\",\n        fill: \"currentColor\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            d: \"M3 1a1 1 0 000 2h1.22l.305 1.222a.997.997 0 00.01.042l1.358 5.43-.893.892C3.74 11.846 4.632 14 6.414 14H15a1 1 0 000-2H6.414l1-1H14a1 1 0 00.894-.553l3-6A1 1 0 0017 3H6.28l-.31-1.243A1 1 0 005 1H3zM16 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM6.5 18a1.5 1.5 0 100-3 1.5 1.5 0 000 3z\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n            lineNumber: 13,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n        lineNumber: 12,\n        columnNumber: 3\n    }, undefined);\nconst HeartIcon = ({ className = \"w-6 h-6\" })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        className: className,\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        stroke: \"currentColor\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n            lineNumber: 19,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n        lineNumber: 18,\n        columnNumber: 3\n    }, undefined);\nconst UserIcon = ({ className = \"h-5 w-5\" })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        className: className,\n        viewBox: \"0 0 20 20\",\n        fill: \"currentColor\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            fillRule: \"evenodd\",\n            d: \"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\",\n            clipRule: \"evenodd\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n            lineNumber: 25,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined);\nconst MenuIcon = ({ className = \"w-6 h-6\" })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        className: className,\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        stroke: \"currentColor\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M4 6h16M4 12h16M4 18h16\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n            lineNumber: 31,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n        lineNumber: 30,\n        columnNumber: 3\n    }, undefined);\nconst CloseIcon = ({ className = \"w-6 h-6\" })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        className: className,\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        stroke: \"currentColor\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M6 18L18 6M6 6l12 12\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n            lineNumber: 37,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n        lineNumber: 36,\n        columnNumber: 3\n    }, undefined);\nfunction StarIcon({ className = \"w-6 h-6\" }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        className: className,\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        stroke: \"currentColor\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n            lineNumber: 50,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\nfunction CheckIcon({ className = \"w-6 h-6\" }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        className: className,\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        stroke: \"currentColor\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M5 13l4 4L19 7\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n            lineNumber: 69,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n        lineNumber: 62,\n        columnNumber: 5\n    }, this);\n}\nfunction ChatIcon({ className = \"w-6 h-6\" }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        className: className,\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        stroke: \"currentColor\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n            lineNumber: 88,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n        lineNumber: 81,\n        columnNumber: 5\n    }, this);\n}\nfunction LocationIcon({ className = \"w-6 h-6\" }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        className: className,\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        stroke: \"currentColor\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n                lineNumber: 107,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M15 11a3 3 0 11-6 0 3 3 0 016 0z\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n                lineNumber: 113,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n        lineNumber: 100,\n        columnNumber: 5\n    }, this);\n}\nconst FacebookIcon = ({ className = \"h-5 w-5\" })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        className: className,\n        viewBox: \"0 0 20 20\",\n        fill: \"currentColor\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            fillRule: \"evenodd\",\n            d: \"M20 10c0-5.523-4.477-10-10-10S0 4.477 0 10c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V10h2.54V7.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V10h2.773l-.443 2.89h-2.33v6.988C16.343 19.128 20 14.991 20 10z\",\n            clipRule: \"evenodd\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n            lineNumber: 125,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n        lineNumber: 124,\n        columnNumber: 3\n    }, undefined);\nconst TwitterIcon = ({ className = \"h-5 w-5\" })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        className: className,\n        viewBox: \"0 0 20 20\",\n        fill: \"currentColor\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            d: \"M6.29 18.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0020 3.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.073 4.073 0 01.8 7.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 010 16.407a11.616 11.616 0 006.29 1.84\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n            lineNumber: 131,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n        lineNumber: 130,\n        columnNumber: 3\n    }, undefined);\nconst InstagramIcon = ({ className = \"h-5 w-5\" })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        className: className,\n        viewBox: \"0 0 20 20\",\n        fill: \"currentColor\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            fillRule: \"evenodd\",\n            d: \"M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z\",\n            clipRule: \"evenodd\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n            lineNumber: 137,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n        lineNumber: 136,\n        columnNumber: 3\n    }, undefined);\nconst FilterIcon = ({ className = \"w-6 h-6\" })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        className: className,\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        stroke: \"currentColor\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n            lineNumber: 143,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n        lineNumber: 142,\n        columnNumber: 3\n    }, undefined);\nconst SortIcon = ({ className = \"w-6 h-6\" })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        className: className,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            d: \"M3 7.5L7.5 3m0 0L12 7.5M7.5 3v13.5m13.5 0L16.5 21m0 0L12 16.5m4.5 4.5V7.5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n            lineNumber: 156,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n        lineNumber: 148,\n        columnNumber: 3\n    }, undefined);\nconst ChevronLeftIcon = ({ className = \"w-6 h-6\" })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        className: className,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            d: \"M15.75 19.5L8.25 12l7.5-7.5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n            lineNumber: 173,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n        lineNumber: 165,\n        columnNumber: 3\n    }, undefined);\nconst ChevronRightIcon = ({ className = \"w-6 h-6\" })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        className: className,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            d: \"M8.25 4.5l7.5 7.5-7.5 7.5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n            lineNumber: 190,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n        lineNumber: 182,\n        columnNumber: 3\n    }, undefined);\nconst FlagIcon = ({ className = \"w-6 h-6\" })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        className: className,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            d: \"M3 3v1.5M3 21v-6m0 0l2.77-.693a9 9 0 016.208.682l.108.054a9 9 0 006.086.71l3.114-.732a48.524 48.524 0 01-.005-10.499l-3.11.732a9 9 0 01-6.085-.711l-.108-.054a9 9 0 00-6.208-.682L3 4.5M3 15V4.5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n            lineNumber: 207,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n        lineNumber: 199,\n        columnNumber: 3\n    }, undefined);\nconst ShieldCheckIcon = ({ className = \"w-6 h-6\" })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        className: className,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            d: \"M9 12.75L11.25 15 15 9.75m-3-7.036A11.959 11.959 0 013.598 6 11.99 11.99 0 003 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285z\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n            lineNumber: 224,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n        lineNumber: 216,\n        columnNumber: 3\n    }, undefined);\nconst PhoneIcon = ({ className = \"w-6 h-6\" })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        className: className,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            d: \"M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 002.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 01-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 00-1.091-.852H4.5A2.25 2.25 0 002.25 4.5v2.25z\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n            lineNumber: 241,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n        lineNumber: 233,\n        columnNumber: 3\n    }, undefined);\nconst IdentificationIcon = ({ className = \"w-6 h-6\" })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        className: className,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            d: \"M15 9h3.75M15 12h3.75M15 15h3.75M4.5 19.5h15a2.25 2.25 0 002.25-2.25V6.75A2.25 2.25 0 0019.5 4.5h-15a2.25 2.25 0 00-2.25 2.25v10.5A2.25 2.25 0 004.5 19.5zm6-10.125a1.875 1.875 0 11-3.75 0 1.875 1.875 0 013.75 0zm1.294 6.336a6.721 6.721 0 01-3.17.789 6.721 6.721 0 01-3.168-.789 3.376 3.376 0 016.338 0z\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n            lineNumber: 258,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n        lineNumber: 250,\n        columnNumber: 3\n    }, undefined);\nfunction ChevronDownIcon({ className = \"w-6 h-6\" }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        className: className,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            d: \"M19.5 8.25l-7.5 7.5-7.5-7.5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n            lineNumber: 276,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n        lineNumber: 268,\n        columnNumber: 5\n    }, this);\n}\nfunction ClockIcon({ className }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        className: className,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            d: \"M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n            lineNumber: 295,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n        lineNumber: 287,\n        columnNumber: 5\n    }, this);\n}\nfunction BookmarkIcon({ className }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        className: className,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            d: \"M17.593 3.322c1.1.128 1.907 1.077 1.907 2.185V21L12 17.25 4.5 21V5.507c0-1.108.806-2.057 1.907-2.185a48.507 48.507 0 0111.186 0z\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n            lineNumber: 314,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n        lineNumber: 306,\n        columnNumber: 5\n    }, this);\n}\nfunction TagIcon({ className }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        className: className,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                d: \"M9.568 3H5.25A2.25 2.25 0 003 5.25v4.318c0 .597.237 1.17.659 1.591l9.581 9.581c.699.699 1.78.872 2.607.33a18.095 18.095 0 005.223-5.223c.542-.827.369-1.908-.33-2.607L11.16 3.66A2.25 2.25 0 009.568 3z\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n                lineNumber: 333,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                d: \"M6 6h.008v.008H6V6z\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n                lineNumber: 338,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n        lineNumber: 325,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Icons.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/LocationDisplay.tsx":
/*!********************************************!*\
  !*** ./src/components/LocationDisplay.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LocationDisplay)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_MapPinIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=MapPinIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/MapPinIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction LocationDisplay({ location, showDistance = true, className = \"\" }) {\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const [distance, setDistance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (session && showDistance) {\n            fetchUserLocation();\n        }\n    }, [\n        session,\n        location,\n        showDistance\n    ]);\n    const fetchUserLocation = async ()=>{\n        try {\n            setIsLoading(true);\n            const response = await fetch(\"/api/user/locations/current\");\n            if (!response.ok) {\n                // User might not have a current location set\n                return;\n            }\n            const userLocation = await response.json();\n            // Calculate distance between user location and product location\n            const calculatedDistance = calculateDistance(userLocation.latitude, userLocation.longitude, location.latitude, location.longitude);\n            setDistance(calculatedDistance);\n        } catch (error) {\n            console.error(\"Error fetching user location:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Calculate distance using Haversine formula\n    const calculateDistance = (lat1, lon1, lat2, lon2)=>{\n        const R = 6371; // Radius of the Earth in km\n        const dLat = (lat2 - lat1) * (Math.PI / 180);\n        const dLon = (lon2 - lon1) * (Math.PI / 180);\n        const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) + Math.cos(lat1 * (Math.PI / 180)) * Math.cos(lat2 * (Math.PI / 180)) * Math.sin(dLon / 2) * Math.sin(dLon / 2);\n        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));\n        const distance = R * c; // Distance in km\n        return distance;\n    };\n    const formatDistance = (distance)=>{\n        if (distance < 1) {\n            return `${(distance * 1000).toFixed(0)} m`;\n        } else if (distance < 10) {\n            return `${distance.toFixed(1)} km`;\n        } else {\n            return `${distance.toFixed(0)} km`;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `flex items-center ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MapPinIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                className: \"h-4 w-4 text-gray-400 mr-1 flex-shrink-0\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/LocationDisplay.tsx\",\n                lineNumber: 93,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-sm text-gray-500 truncate\",\n                children: [\n                    location.city,\n                    \", \",\n                    location.state && `${location.state}, `,\n                    location.country,\n                    showDistance && distance !== null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"ml-1 text-gray-400\",\n                        children: [\n                            \"(\",\n                            formatDistance(distance),\n                            \" away)\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/LocationDisplay.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/LocationDisplay.tsx\",\n                lineNumber: 94,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/LocationDisplay.tsx\",\n        lineNumber: 92,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/LocationDisplay.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Message.tsx":
/*!************************************!*\
  !*** ./src/components/Message.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Message)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Icons__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Icons */ \"(ssr)/./src/components/Icons.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Message({ productId, sellerId, productName, sellerName }) {\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        // Here we would typically send the message to a backend\n        console.log(\"Message sent:\", {\n            productId,\n            sellerId,\n            message,\n            timestamp: new Date().toISOString()\n        });\n        setMessage(\"\");\n        setIsOpen(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setIsOpen(true),\n                className: \"flex items-center justify-center gap-2 px-6 py-3 bg-white border border-slate-200 rounded-lg hover:bg-slate-50 transition\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Icons__WEBPACK_IMPORTED_MODULE_2__.ChatIcon, {\n                        className: \"w-5 h-5\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Message.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"Message\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Message.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Message.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-xl shadow-lg max-w-lg w-full p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-start mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold\",\n                                            children: \"Message Seller\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Message.tsx\",\n                                            lineNumber: 45,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-slate-600\",\n                                            children: [\n                                                \"About: \",\n                                                productName\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Message.tsx\",\n                                            lineNumber: 46,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-slate-600\",\n                                            children: [\n                                                \"Seller: \",\n                                                sellerName\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Message.tsx\",\n                                            lineNumber: 49,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Message.tsx\",\n                                    lineNumber: 44,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setIsOpen(false),\n                                    className: \"text-slate-400 hover:text-slate-600\",\n                                    children: \"✕\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Message.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Message.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    value: message,\n                                    onChange: (e)=>setMessage(e.target.value),\n                                    placeholder: \"Type your message here...\",\n                                    className: \"w-full h-32 px-4 py-2 border border-slate-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 mb-4\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Message.tsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setIsOpen(false),\n                                            className: \"px-6 py-2 text-slate-600 hover:text-slate-900\",\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Message.tsx\",\n                                            lineNumber: 70,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            className: \"px-6 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition\",\n                                            children: \"Send Message\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Message.tsx\",\n                                            lineNumber: 77,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Message.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Message.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Message.tsx\",\n                    lineNumber: 42,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Message.tsx\",\n                lineNumber: 41,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Message.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ProductCard.tsx":
/*!****************************************!*\
  !*** ./src/components/ProductCard.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProductCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_Icons__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Icons */ \"(ssr)/./src/components/Icons.tsx\");\n/* harmony import */ var _context_UserContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/context/UserContext */ \"(ssr)/./src/context/UserContext.tsx\");\n/* harmony import */ var _Message__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Message */ \"(ssr)/./src/components/Message.tsx\");\n/* harmony import */ var _VerificationBadge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./VerificationBadge */ \"(ssr)/./src/components/VerificationBadge.tsx\");\n/* harmony import */ var _ReportButton__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ReportButton */ \"(ssr)/./src/components/ReportButton.tsx\");\n/* harmony import */ var _LocationDisplay__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./LocationDisplay */ \"(ssr)/./src/components/LocationDisplay.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nfunction ProductCard({ product, showDistance = false }) {\n    const { addToWishlist, removeFromWishlist, isInWishlist } = (0,_context_UserContext__WEBPACK_IMPORTED_MODULE_4__.useUser)();\n    const [isHovered, setIsHovered] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleWishlistClick = (e)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        if (isInWishlist(product.id)) {\n            removeFromWishlist(product.id);\n        } else {\n            addToWishlist(product);\n        }\n    };\n    const handleReport = (data)=>{\n        // Here we would typically send the report to a backend\n        console.log(\"Report submitted:\", data);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 overflow-hidden\",\n        onMouseEnter: ()=>setIsHovered(true),\n        onMouseLeave: ()=>setIsHovered(false),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                href: `/products/${product.id}`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: \"/placeholder.png\",\n                                alt: product.name,\n                                className: \"w-full h-48 object-cover\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                                lineNumber: 50,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleWishlistClick,\n                                className: `absolute top-2 right-2 p-2 rounded-full transition-colors ${isInWishlist(product.id) ? \"bg-red-500 text-white\" : \"bg-white/80 text-slate-600 hover:bg-white\"}`,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Icons__WEBPACK_IMPORTED_MODULE_3__.HeartIcon, {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"font-semibold text-lg mb-1\",\n                                children: product.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-slate-600 mb-2\",\n                                children: typeof product.price === \"number\" ? `$${product.price.toFixed(2)}` : product.price\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 11\n                            }, this),\n                            product.location && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LocationDisplay__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                location: product.location,\n                                showDistance: showDistance,\n                                className: \"mb-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 13\n                            }, this),\n                            product.seller && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-slate-600\",\n                                            children: product.seller.name\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_VerificationBadge__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            status: product.seller.verificationStatus,\n                                            size: \"sm\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 pb-4 flex items-center justify-between\",\n                children: [\n                    product.seller && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Message__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        productId: product.id,\n                        sellerId: product.seller.id,\n                        productName: product.name,\n                        sellerName: product.seller.name || \"\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ReportButton__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        itemId: product.id,\n                        itemType: \"product\",\n                        onReport: handleReport\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n                lineNumber: 91,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductCard.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ProductCard.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/RecentlyViewed.tsx":
/*!*******************************************!*\
  !*** ./src/components/RecentlyViewed.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RecentlyViewed)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ProductCard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ProductCard */ \"(ssr)/./src/components/ProductCard.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst MAX_RECENT_ITEMS = 4;\nfunction RecentlyViewed() {\n    const [recentProducts, setRecentProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Load recently viewed products from localStorage\n        const stored = localStorage.getItem(\"recentlyViewed\");\n        if (stored) {\n            try {\n                const products = JSON.parse(stored);\n                setRecentProducts(products.slice(0, MAX_RECENT_ITEMS));\n            } catch (error) {\n                console.error(\"Error parsing recently viewed products:\", error);\n            }\n        }\n    }, []);\n    if (recentProducts.length === 0) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-xl shadow-sm p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-xl font-bold mb-4\",\n                children: \"Recently Viewed\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/RecentlyViewed.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4\",\n                children: recentProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ProductCard__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        product: product\n                    }, product.id, false, {\n                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/RecentlyViewed.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/RecentlyViewed.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/RecentlyViewed.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/RecentlyViewed.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ReportButton.tsx":
/*!*****************************************!*\
  !*** ./src/components/ReportButton.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ReportButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Icons__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Icons */ \"(ssr)/./src/components/Icons.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction ReportButton({ itemId, itemType, onReport }) {\n    const [isModalOpen, setIsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [reason, setReason] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [details, setDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        onReport({\n            itemId,\n            itemType,\n            reason,\n            details\n        });\n        setIsModalOpen(false);\n        setReason(\"\");\n        setDetails(\"\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setIsModalOpen(true),\n                className: \"flex items-center gap-2 text-sm text-slate-600 hover:text-slate-900\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Icons__WEBPACK_IMPORTED_MODULE_2__.FlagIcon, {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ReportButton.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"Report\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ReportButton.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ReportButton.tsx\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, this),\n            isModalOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-xl p-6 max-w-md w-full mx-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-bold mb-4\",\n                            children: [\n                                \"Report \",\n                                itemType === \"product\" ? \"Listing\" : \"Seller\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ReportButton.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-slate-700 mb-1\",\n                                            children: \"Reason for Report\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ReportButton.tsx\",\n                                            lineNumber: 51,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: reason,\n                                            onChange: (e)=>setReason(e.target.value),\n                                            className: \"w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500\",\n                                            required: true,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Select a reason\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ReportButton.tsx\",\n                                                    lineNumber: 60,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"fake\",\n                                                    children: \"Fake or Counterfeit Item\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ReportButton.tsx\",\n                                                    lineNumber: 61,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"scam\",\n                                                    children: \"Suspected Scam\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ReportButton.tsx\",\n                                                    lineNumber: 62,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"inappropriate\",\n                                                    children: \"Inappropriate Content\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ReportButton.tsx\",\n                                                    lineNumber: 63,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"misleading\",\n                                                    children: \"Misleading Information\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ReportButton.tsx\",\n                                                    lineNumber: 64,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"other\",\n                                                    children: \"Other\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ReportButton.tsx\",\n                                                    lineNumber: 65,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ReportButton.tsx\",\n                                            lineNumber: 54,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ReportButton.tsx\",\n                                    lineNumber: 50,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-slate-700 mb-1\",\n                                            children: \"Additional Details\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ReportButton.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: details,\n                                            onChange: (e)=>setDetails(e.target.value),\n                                            className: \"w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500\",\n                                            rows: 4,\n                                            placeholder: \"Please provide any additional information that will help us understand the issue\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ReportButton.tsx\",\n                                            lineNumber: 72,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ReportButton.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setIsModalOpen(false),\n                                            className: \"px-4 py-2 text-slate-600 hover:text-slate-900\",\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ReportButton.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            className: \"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700\",\n                                            children: \"Submit Report\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ReportButton.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ReportButton.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ReportButton.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ReportButton.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ReportButton.tsx\",\n                lineNumber: 46,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ReportButton.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/SafetyTips.tsx":
/*!***************************************!*\
  !*** ./src/components/SafetyTips.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SafetyTips)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronUpIcon_CreditCardIcon_DocumentCheckIcon_ExclamationTriangleIcon_EyeIcon_MapPinIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronUpIcon,CreditCardIcon,DocumentCheckIcon,ExclamationTriangleIcon,EyeIcon,MapPinIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CreditCardIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronUpIcon_CreditCardIcon_DocumentCheckIcon_ExclamationTriangleIcon_EyeIcon_MapPinIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronUpIcon,CreditCardIcon,DocumentCheckIcon,ExclamationTriangleIcon,EyeIcon,MapPinIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/MapPinIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronUpIcon_CreditCardIcon_DocumentCheckIcon_ExclamationTriangleIcon_EyeIcon_MapPinIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronUpIcon,CreditCardIcon,DocumentCheckIcon,ExclamationTriangleIcon,EyeIcon,MapPinIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronUpIcon_CreditCardIcon_DocumentCheckIcon_ExclamationTriangleIcon_EyeIcon_MapPinIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronUpIcon,CreditCardIcon,DocumentCheckIcon,ExclamationTriangleIcon,EyeIcon,MapPinIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/DocumentCheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronUpIcon_CreditCardIcon_DocumentCheckIcon_ExclamationTriangleIcon_EyeIcon_MapPinIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronUpIcon,CreditCardIcon,DocumentCheckIcon,ExclamationTriangleIcon,EyeIcon,MapPinIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronUpIcon_CreditCardIcon_DocumentCheckIcon_ExclamationTriangleIcon_EyeIcon_MapPinIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronUpIcon,CreditCardIcon,DocumentCheckIcon,ExclamationTriangleIcon,EyeIcon,MapPinIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronUpIcon_CreditCardIcon_DocumentCheckIcon_ExclamationTriangleIcon_EyeIcon_MapPinIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronUpIcon,CreditCardIcon,DocumentCheckIcon,ExclamationTriangleIcon,EyeIcon,MapPinIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChevronUpIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronUpIcon_CreditCardIcon_DocumentCheckIcon_ExclamationTriangleIcon_EyeIcon_MapPinIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronUpIcon,CreditCardIcon,DocumentCheckIcon,ExclamationTriangleIcon,EyeIcon,MapPinIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst safetyTips = [\n    {\n        id: \"payment\",\n        icon: _barrel_optimize_names_ChevronDownIcon_ChevronUpIcon_CreditCardIcon_DocumentCheckIcon_ExclamationTriangleIcon_EyeIcon_MapPinIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        title: \"Never send prepayments\",\n        description: \"Avoid sending money, gift cards, or cryptocurrency before seeing the item in person. Legitimate sellers will accept payment upon delivery or pickup.\",\n        color: \"text-red-600 bg-red-50 border-red-200\"\n    },\n    {\n        id: \"meeting\",\n        icon: _barrel_optimize_names_ChevronDownIcon_ChevronUpIcon_CreditCardIcon_DocumentCheckIcon_ExclamationTriangleIcon_EyeIcon_MapPinIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        title: \"Meet in safe public places\",\n        description: \"Choose well-lit, busy public locations like shopping centers, coffee shops, or police station parking lots. Bring a friend if possible.\",\n        color: \"text-blue-600 bg-blue-50 border-blue-200\"\n    },\n    {\n        id: \"inspection\",\n        icon: _barrel_optimize_names_ChevronDownIcon_ChevronUpIcon_CreditCardIcon_DocumentCheckIcon_ExclamationTriangleIcon_EyeIcon_MapPinIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        title: \"Inspect before you buy\",\n        description: \"Thoroughly examine the item to ensure it matches the description and photos. Test electronics and check for any damage or defects.\",\n        color: \"text-green-600 bg-green-50 border-green-200\"\n    },\n    {\n        id: \"documentation\",\n        icon: _barrel_optimize_names_ChevronDownIcon_ChevronUpIcon_CreditCardIcon_DocumentCheckIcon_ExclamationTriangleIcon_EyeIcon_MapPinIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        title: \"Verify documentation\",\n        description: \"For valuable items, ask for receipts, warranties, or proof of ownership. Check serial numbers and authenticity when applicable.\",\n        color: \"text-purple-600 bg-purple-50 border-purple-200\"\n    }\n];\nfunction SafetyTips() {\n    const [isExpanded, setIsExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 cursor-pointer hover:bg-gray-50 transition-colors\",\n                onClick: ()=>setIsExpanded(!isExpanded),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-2 bg-orange-100 rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronUpIcon_CreditCardIcon_DocumentCheckIcon_ExclamationTriangleIcon_EyeIcon_MapPinIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-6 h-6 text-orange-600\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/SafetyTips.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/SafetyTips.tsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900\",\n                                            children: \"Safety Tips\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/SafetyTips.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Stay safe while buying and selling\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/SafetyTips.tsx\",\n                                            lineNumber: 70,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/SafetyTips.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/SafetyTips.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronUpIcon_CreditCardIcon_DocumentCheckIcon_ExclamationTriangleIcon_EyeIcon_MapPinIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"w-5 h-5 text-orange-500\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/SafetyTips.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 13\n                                }, this),\n                                isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronUpIcon_CreditCardIcon_DocumentCheckIcon_ExclamationTriangleIcon_EyeIcon_MapPinIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"w-5 h-5 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/SafetyTips.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronUpIcon_CreditCardIcon_DocumentCheckIcon_ExclamationTriangleIcon_EyeIcon_MapPinIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-5 h-5 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/SafetyTips.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/SafetyTips.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/SafetyTips.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/SafetyTips.tsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, this),\n            isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 space-y-4\",\n                        children: safetyTips.map((tip)=>{\n                            const IconComponent = tip.icon;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `p-4 rounded-lg border ${tip.color}`,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-shrink-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/SafetyTips.tsx\",\n                                                lineNumber: 96,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/SafetyTips.tsx\",\n                                            lineNumber: 95,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900 mb-1\",\n                                                    children: tip.title\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/SafetyTips.tsx\",\n                                                    lineNumber: 99,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-700\",\n                                                    children: tip.description\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/SafetyTips.tsx\",\n                                                    lineNumber: 102,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/SafetyTips.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/SafetyTips.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 19\n                                }, this)\n                            }, tip.id, false, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/SafetyTips.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/SafetyTips.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 pb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 rounded-lg p-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronUpIcon_CreditCardIcon_DocumentCheckIcon_ExclamationTriangleIcon_EyeIcon_MapPinIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-4 h-4 text-gray-500 mt-0.5 flex-shrink-0\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/SafetyTips.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Remember:\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/SafetyTips.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 19\n                                            }, this),\n                                            \" If a deal seems too good to be true, it probably is. Trust your instincts and report suspicious activity to keep our marketplace safe for everyone.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/SafetyTips.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/SafetyTips.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/SafetyTips.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/SafetyTips.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/SafetyTips.tsx\",\n                lineNumber: 85,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/SafetyTips.tsx\",\n        lineNumber: 58,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/SafetyTips.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/VerificationBadge.tsx":
/*!**********************************************!*\
  !*** ./src/components/VerificationBadge.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ VerificationBadge)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Icons__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Icons */ \"(ssr)/./src/components/Icons.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction VerificationBadge({ status, size = \"md\", type = \"Basic\", showTooltip = false }) {\n    const getSizeClasses = ()=>{\n        switch(size){\n            case \"sm\":\n                return \"text-xs px-2 py-0.5\";\n            case \"lg\":\n                return \"text-sm px-3 py-1\";\n            default:\n                return \"text-xs px-2.5 py-0.5\";\n        }\n    };\n    const getStatusClasses = ()=>{\n        switch(status){\n            case \"Premium\":\n                return \"bg-purple-100 text-purple-700\";\n            case \"Enhanced\":\n                return \"bg-blue-100 text-blue-700\";\n            case \"Verified\":\n                return \"bg-green-100 text-green-700\";\n            case \"Pending\":\n                return \"bg-yellow-100 text-yellow-700\";\n            default:\n                return \"bg-slate-100 text-slate-700\";\n        }\n    };\n    const getIcon = ()=>{\n        if (type === \"Phone\") {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Icons__WEBPACK_IMPORTED_MODULE_1__.PhoneIcon, {\n                className: \"w-3 h-3\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/VerificationBadge.tsx\",\n                lineNumber: 49,\n                columnNumber: 14\n            }, this);\n        } else if (type === \"ID\") {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Icons__WEBPACK_IMPORTED_MODULE_1__.IdentificationIcon, {\n                className: \"w-3 h-3\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/VerificationBadge.tsx\",\n                lineNumber: 51,\n                columnNumber: 14\n            }, this);\n        } else if (type === \"Social\") {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Icons__WEBPACK_IMPORTED_MODULE_1__.CheckIcon, {\n                className: \"w-3 h-3\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/VerificationBadge.tsx\",\n                lineNumber: 53,\n                columnNumber: 14\n            }, this);\n        } else {\n            switch(status){\n                case \"Premium\":\n                case \"Enhanced\":\n                case \"Verified\":\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Icons__WEBPACK_IMPORTED_MODULE_1__.ShieldCheckIcon, {\n                        className: \"w-3 h-3\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/VerificationBadge.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 18\n                    }, this);\n                case \"Pending\":\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Icons__WEBPACK_IMPORTED_MODULE_1__.CheckIcon, {\n                        className: \"w-3 h-3\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/VerificationBadge.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 18\n                    }, this);\n                default:\n                    return null;\n            }\n        }\n    };\n    const getTooltipText = ()=>{\n        if (type === \"Phone\") {\n            return \"Phone number verified\";\n        } else if (type === \"ID\") {\n            return \"ID verified\";\n        } else if (type === \"Social\") {\n            return \"Social account verified\";\n        } else {\n            switch(status){\n                case \"Premium\":\n                    return \"Premium verified seller with ID and phone verification\";\n                case \"Enhanced\":\n                    return \"Enhanced verification with additional verification steps\";\n                case \"Verified\":\n                    return \"Basic verification completed\";\n                case \"Pending\":\n                    return \"Verification in progress\";\n                default:\n                    return \"Not verified\";\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative group\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `inline-flex items-center gap-1 rounded-full font-medium ${getSizeClasses()} ${getStatusClasses()}`,\n                children: [\n                    getIcon(),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: status\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/VerificationBadge.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/VerificationBadge.tsx\",\n                lineNumber: 93,\n                columnNumber: 7\n            }, this),\n            showTooltip && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap pointer-events-none z-10\",\n                children: getTooltipText()\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/VerificationBadge.tsx\",\n                lineNumber: 101,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/VerificationBadge.tsx\",\n        lineNumber: 92,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/VerificationBadge.tsx\n");

/***/ }),

/***/ "(ssr)/./src/context/UserContext.tsx":
/*!*************************************!*\
  !*** ./src/context/UserContext.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UserProvider: () => (/* binding */ UserProvider),\n/* harmony export */   useUser: () => (/* binding */ useUser)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ UserProvider,useUser auto */ \n\nconst UserContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction UserProvider({ children }) {\n    const [wishlist, setWishlist] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [recentlyViewed, setRecentlyViewed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Load wishlist from localStorage on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const savedWishlist = localStorage.getItem(\"wishlist\");\n        if (savedWishlist) {\n            setWishlist(JSON.parse(savedWishlist));\n        }\n    }, []);\n    // Save wishlist to localStorage whenever it changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        localStorage.setItem(\"wishlist\", JSON.stringify(wishlist));\n    }, [\n        wishlist\n    ]);\n    const addToWishlist = (product)=>{\n        setWishlist((prev)=>{\n            if (!prev.find((p)=>p.id === product.id)) {\n                return [\n                    ...prev,\n                    product\n                ];\n            }\n            return prev;\n        });\n    };\n    const removeFromWishlist = (productId)=>{\n        setWishlist((prev)=>prev.filter((p)=>p.id !== productId));\n    };\n    const addToRecentlyViewed = (product)=>{\n        setRecentlyViewed((prev)=>{\n            const filtered = prev.filter((p)=>p.id !== product.id);\n            return [\n                product,\n                ...filtered\n            ].slice(0, 10); // Keep only last 10 items\n        });\n    };\n    const clearRecentlyViewed = ()=>{\n        setRecentlyViewed([]);\n    };\n    const isInWishlist = (productId)=>{\n        return wishlist.some((p)=>p.id === productId);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UserContext.Provider, {\n        value: {\n            wishlist,\n            recentlyViewed,\n            addToWishlist,\n            removeFromWishlist,\n            addToRecentlyViewed,\n            clearRecentlyViewed,\n            isInWishlist\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/context/UserContext.tsx\",\n        lineNumber: 64,\n        columnNumber: 5\n    }, this);\n}\nfunction useUser() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(UserContext);\n    if (context === undefined) {\n        throw new Error(\"useUser must be used within a UserProvider\");\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/context/UserContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/data.ts":
/*!*************************!*\
  !*** ./src/lib/data.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   categories: () => (/* binding */ categories),\n/* harmony export */   featuredSellers: () => (/* binding */ featuredSellers),\n/* harmony export */   products: () => (/* binding */ products)\n/* harmony export */ });\nconst categories = [\n    {\n        id: 1,\n        name: \"Real Estate\",\n        description: \"Properties for sale and rent including houses, apartments, and commercial spaces\",\n        slug: \"real-estate\",\n        icon: \"\\uD83C\\uDFE0\",\n        count: 150\n    },\n    {\n        id: 2,\n        name: \"Vehicles\",\n        description: \"Cars, motorcycles, and other vehicles for sale\",\n        slug: \"vehicles\",\n        icon: \"\\uD83D\\uDE97\",\n        count: 200\n    },\n    {\n        id: 3,\n        name: \"Gadgets\",\n        description: \"Electronics, smartphones, and other tech gadgets\",\n        slug: \"gadgets\",\n        icon: \"\\uD83D\\uDCF1\",\n        count: 300\n    }\n];\nconst products = [\n    {\n        id: 1,\n        name: \"Modern 3-Bedroom Apartment\",\n        price: \"$250,000\",\n        image: \"/images/apartment.jpg\",\n        category: \"real-estate\",\n        description: \"Spacious 3-bedroom apartment in prime location with modern amenities.\",\n        condition: \"New\",\n        location: \"Lagos, Nigeria\",\n        availability: \"Available\",\n        seller: {\n            id: 1,\n            name: \"PrimeProperties\",\n            rating: 4.8\n        }\n    },\n    {\n        id: 2,\n        name: \"2022 Toyota Camry\",\n        price: \"$35,000\",\n        image: \"/images/camry.jpg\",\n        category: \"vehicles\",\n        description: \"Well-maintained Toyota Camry with low mileage and full service history.\",\n        condition: \"Used\",\n        location: \"Abuja, Nigeria\",\n        availability: \"Available\",\n        seller: {\n            id: 2,\n            name: \"AutoMasters\",\n            rating: 4.9\n        }\n    },\n    {\n        id: 3,\n        name: \"iPhone 15 Pro Max\",\n        price: \"$1,199\",\n        image: \"/images/iphone.jpg\",\n        category: \"gadgets\",\n        description: \"Latest iPhone model with Pro camera system and A17 Pro chip.\",\n        condition: \"New\",\n        location: \"Lagos, Nigeria\",\n        availability: \"Available\",\n        seller: {\n            id: 3,\n            name: \"TechStore\",\n            rating: 4.7\n        }\n    },\n    {\n        id: 4,\n        name: \"Luxury Villa\",\n        price: \"$500,000\",\n        image: \"/images/villa.jpg\",\n        category: \"real-estate\",\n        description: \"Stunning 5-bedroom villa with pool and garden in exclusive neighborhood.\",\n        condition: \"New\",\n        location: \"Port Harcourt, Nigeria\",\n        availability: \"Available\",\n        seller: {\n            id: 1,\n            name: \"PrimeProperties\",\n            rating: 4.8\n        }\n    }\n];\nconst featuredSellers = [\n    {\n        id: 1,\n        name: \"TechStore\",\n        rating: 4.8,\n        activeListings: 45,\n        joined: \"2022\",\n        location: \"Lagos, Nigeria\",\n        responseTime: \"< 1 hour\",\n        verificationStatus: \"Verified\",\n        email: \"<EMAIL>\",\n        phone: \"+234 ************\",\n        level: {\n            current: 2,\n            title: \"Silver\",\n            points: 1500,\n            nextLevelPoints: 2000\n        },\n        performance: {\n            totalSales: 15000,\n            averageRating: 4.8,\n            responseRate: 98,\n            completionRate: 99,\n            disputeRate: 1\n        },\n        badges: [],\n        achievements: []\n    },\n    {\n        id: 2,\n        name: \"MobileWorld\",\n        rating: 4.5,\n        activeListings: 32,\n        joined: \"2021\",\n        location: \"Abuja, Nigeria\",\n        responseTime: \"< 2 hours\",\n        verificationStatus: \"Verified\",\n        email: \"<EMAIL>\",\n        phone: \"+234 ************\",\n        level: {\n            current: 1,\n            title: \"Bronze\",\n            points: 800,\n            nextLevelPoints: 1000\n        },\n        performance: {\n            totalSales: 8000,\n            averageRating: 4.5,\n            responseRate: 95,\n            completionRate: 97,\n            disputeRate: 2\n        },\n        badges: [],\n        achievements: []\n    },\n    {\n        id: 3,\n        name: \"ShoeStore\",\n        rating: 4.9,\n        activeListings: 28,\n        joined: \"2023\",\n        location: \"Port Harcourt, Nigeria\",\n        responseTime: \"< 1 hour\",\n        verificationStatus: \"Verified\",\n        email: \"<EMAIL>\",\n        phone: \"+234 ************\",\n        level: {\n            current: 3,\n            title: \"Gold\",\n            points: 2500,\n            nextLevelPoints: 3000\n        },\n        performance: {\n            totalSales: 25000,\n            averageRating: 4.9,\n            responseRate: 99,\n            completionRate: 100,\n            disputeRate: 0\n        },\n        badges: [],\n        achievements: []\n    }\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/data.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"149448ddc89a\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFya2V0cGxhY2UvLi9zcmMvYXBwL2dsb2JhbHMuY3NzPzJlNDAiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIxNDk0NDhkZGM4OWFcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./providers */ \"(rsc)/./src/app/providers.tsx\");\n\n\n\n\nconst metadata = {\n    title: \"Marketplace\",\n    description: \"A modern marketplace for buying and selling products\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/layout.tsx\",\n                lineNumber: 21,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/layout.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/layout.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBS01BO0FBSGdCO0FBQ2E7QUFJNUIsTUFBTUUsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUM7QUFFYyxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztrQkFDVCw0RUFBQ0M7WUFBS0MsV0FBV1YsK0pBQWU7c0JBQzlCLDRFQUFDQyxrREFBU0E7MEJBQ1BLOzs7Ozs7Ozs7Ozs7Ozs7O0FBS1giLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJrZXRwbGFjZS8uL3NyYy9hcHAvbGF5b3V0LnRzeD81N2E5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tICduZXh0J1xuaW1wb3J0IHsgSW50ZXIgfSBmcm9tICduZXh0L2ZvbnQvZ29vZ2xlJ1xuaW1wb3J0ICcuL2dsb2JhbHMuY3NzJ1xuaW1wb3J0IFByb3ZpZGVycyBmcm9tICcuL3Byb3ZpZGVycydcblxuY29uc3QgaW50ZXIgPSBJbnRlcih7IHN1YnNldHM6IFsnbGF0aW4nXSB9KVxuXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xuICB0aXRsZTogJ01hcmtldHBsYWNlJyxcbiAgZGVzY3JpcHRpb246ICdBIG1vZGVybiBtYXJrZXRwbGFjZSBmb3IgYnV5aW5nIGFuZCBzZWxsaW5nIHByb2R1Y3RzJyxcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufToge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXG59KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImVuXCI+XG4gICAgICA8Ym9keSBjbGFzc05hbWU9e2ludGVyLmNsYXNzTmFtZX0+XG4gICAgICAgIDxQcm92aWRlcnM+XG4gICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICA8L1Byb3ZpZGVycz5cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn0gIl0sIm5hbWVzIjpbImludGVyIiwiUHJvdmlkZXJzIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJib2R5IiwiY2xhc3NOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/product/[id]/page.tsx":
/*!***************************************!*\
  !*** ./src/app/product/[id]/page.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/AI Development/marketplace/src/app/product/[id]/page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/app/providers.tsx":
/*!*******************************!*\
  !*** ./src/app/providers.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/AI Development/marketplace/src/app/providers.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/@heroicons"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fproduct%2F%5Bid%5D%2Fpage&page=%2Fproduct%2F%5Bid%5D%2Fpage&appPaths=%2Fproduct%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fproduct%2F%5Bid%5D%2Fpage.tsx&appDir=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();