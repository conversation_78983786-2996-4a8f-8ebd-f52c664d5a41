"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/stats/route";
exports.ids = ["app/api/admin/stats/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fstats%2Froute&page=%2Fapi%2Fadmin%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fstats%2Froute.ts&appDir=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fstats%2Froute&page=%2Fapi%2Fadmin%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fstats%2Froute.ts&appDir=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_mac_Documents_AI_Development_marketplace_src_app_api_admin_stats_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/admin/stats/route.ts */ \"(rsc)/./src/app/api/admin/stats/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/stats/route\",\n        pathname: \"/api/admin/stats\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/stats/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/api/admin/stats/route.ts\",\n    nextConfigOutput,\n    userland: _Users_mac_Documents_AI_Development_marketplace_src_app_api_admin_stats_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/admin/stats/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fstats%2Froute&page=%2Fapi%2Fadmin%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fstats%2Froute.ts&appDir=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/admin/stats/route.ts":
/*!******************************************!*\
  !*** ./src/app/api/admin/stats/route.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var next_auth_next__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/next */ \"(rsc)/./node_modules/next-auth/next/index.js\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n/* harmony import */ var _auth_nextauth_route__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../auth/[...nextauth]/route */ \"(rsc)/./src/app/api/auth/[...nextauth]/route.ts\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_4__);\n\n\n\n\n\nasync function GET() {\n    try {\n        const session = await (0,next_auth_next__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_auth_nextauth_route__WEBPACK_IMPORTED_MODULE_3__.authOptions);\n        if (!session || session.user.role !== _prisma_client__WEBPACK_IMPORTED_MODULE_4__.Role.ADMIN) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                message: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        // Get the current date and calculate the date 12 months ago\n        const now = new Date();\n        const twelveMonthsAgo = new Date();\n        twelveMonthsAgo.setMonth(now.getMonth() - 11);\n        twelveMonthsAgo.setDate(1);\n        twelveMonthsAgo.setHours(0, 0, 0, 0);\n        // Fetch all stats in parallel\n        const [totalUsers, totalProducts, totalCategories, totalSales, monthlySalesData, userGrowthData, topCategoriesData] = await Promise.all([\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.user.count(),\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.product.count(),\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.category.count(),\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.product.aggregate({\n                _sum: {\n                    price: true\n                }\n            }),\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.product.groupBy({\n                by: [\n                    \"createdAt\"\n                ],\n                _sum: {\n                    price: true\n                },\n                where: {\n                    createdAt: {\n                        gte: twelveMonthsAgo\n                    }\n                },\n                orderBy: {\n                    createdAt: \"asc\"\n                }\n            }),\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.user.groupBy({\n                by: [\n                    \"createdAt\"\n                ],\n                _count: {\n                    _all: true\n                },\n                where: {\n                    createdAt: {\n                        gte: twelveMonthsAgo\n                    }\n                },\n                orderBy: {\n                    createdAt: \"asc\"\n                }\n            }),\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.category.findMany({\n                include: {\n                    _count: {\n                        select: {\n                            products: true\n                        }\n                    }\n                },\n                orderBy: {\n                    products: {\n                        _count: \"desc\"\n                    }\n                },\n                take: 5\n            })\n        ]);\n        // Process monthly sales data\n        const monthlySales = Array.from({\n            length: 12\n        }, (_, i)=>{\n            const date = new Date(now);\n            date.setMonth(now.getMonth() - i);\n            const month = date.toLocaleString(\"default\", {\n                month: \"short\"\n            });\n            const year = date.getFullYear();\n            return {\n                month: `${month} ${year}`,\n                amount: 0\n            };\n        }).reverse();\n        // Process user growth data\n        const userGrowth = Array.from({\n            length: 12\n        }, (_, i)=>{\n            const date = new Date(now);\n            date.setMonth(now.getMonth() - i);\n            const month = date.toLocaleString(\"default\", {\n                month: \"short\"\n            });\n            const year = date.getFullYear();\n            return {\n                month: `${month} ${year}`,\n                count: 0\n            };\n        }).reverse();\n        // Fill in the actual sales data\n        monthlySalesData.forEach((sale)=>{\n            const saleDate = new Date(sale.createdAt);\n            const month = saleDate.toLocaleString(\"default\", {\n                month: \"short\"\n            });\n            const year = saleDate.getFullYear();\n            const monthKey = `${month} ${year}`;\n            const monthIndex = monthlySales.findIndex((m)=>m.month === monthKey);\n            if (monthIndex !== -1) {\n                monthlySales[monthIndex].amount += sale._sum.price || 0;\n            }\n        });\n        // Fill in the actual user growth data\n        userGrowthData.forEach((growth)=>{\n            const growthDate = new Date(growth.createdAt);\n            const month = growthDate.toLocaleString(\"default\", {\n                month: \"short\"\n            });\n            const year = growthDate.getFullYear();\n            const monthKey = `${month} ${year}`;\n            const monthIndex = userGrowth.findIndex((m)=>m.month === monthKey);\n            if (monthIndex !== -1) {\n                userGrowth[monthIndex].count += growth._count._all;\n            }\n        });\n        // Process top categories data\n        const topCategories = topCategoriesData.map((category)=>({\n                name: category.name,\n                count: category._count.products\n            }));\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            totalUsers,\n            totalProducts,\n            totalCategories,\n            totalSales: totalSales._sum.price || 0,\n            monthlySales,\n            userGrowth,\n            topCategories\n        });\n    } catch (error) {\n        console.error(\"Error fetching admin stats:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            message: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/admin/stats/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/api/auth/[...nextauth]/route.ts":
/*!*************************************************!*\
  !*** ./src/app/api/auth/[...nextauth]/route.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ handler),\n/* harmony export */   POST: () => (/* binding */ handler),\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var _auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @auth/prisma-adapter */ \"(rsc)/./node_modules/@auth/prisma-adapter/index.js\");\n/* harmony import */ var next_auth_next__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/next */ \"(rsc)/./node_modules/next-auth/next/index.js\");\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n\n\n\n\n\n\nconst authOptions = {\n    adapter: (0,_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_0__.PrismaAdapter)(_lib_prisma__WEBPACK_IMPORTED_MODULE_5__.prisma),\n    providers: [\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_3__[\"default\"])({\n            clientId: process.env.GOOGLE_CLIENT_ID,\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET\n        }),\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n            name: \"credentials\",\n            credentials: {\n                email: {\n                    label: \"Email\",\n                    type: \"email\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.email || !credentials?.password) {\n                    throw new Error(\"Invalid credentials\");\n                }\n                const user = await _lib_prisma__WEBPACK_IMPORTED_MODULE_5__.prisma.user.findUnique({\n                    where: {\n                        email: credentials.email\n                    }\n                });\n                if (!user || !user?.hashedPassword) {\n                    throw new Error(\"Invalid credentials\");\n                }\n                const isCorrectPassword = await bcryptjs__WEBPACK_IMPORTED_MODULE_4__[\"default\"].compare(credentials.password, user.hashedPassword);\n                if (!isCorrectPassword) {\n                    throw new Error(\"Invalid credentials\");\n                }\n                return user;\n            }\n        })\n    ],\n    pages: {\n        signIn: \"/login\"\n    },\n    debug: \"development\" === \"development\",\n    session: {\n        strategy: \"jwt\"\n    },\n    secret: process.env.NEXTAUTH_SECRET,\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                token.id = user.id;\n                token.role = user.role;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (session.user) {\n                session.user.id = token.id;\n                session.user.role = token.role;\n            }\n            return session;\n        }\n    }\n};\nconst handler = (0,next_auth_next__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(authOptions);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/[...nextauth]/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) {\n    globalForPrisma.prisma = prisma;\n}\n// For backward compatibility\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (prisma);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQThDO0FBRTlDLE1BQU1DLGtCQUFrQkM7QUFJakIsTUFBTUMsU0FBU0YsZ0JBQWdCRSxNQUFNLElBQUksSUFBSUgsd0RBQVlBLEdBQUc7QUFFbkUsSUFBSUksSUFBeUIsRUFBYztJQUN6Q0gsZ0JBQWdCRSxNQUFNLEdBQUdBO0FBQzNCO0FBRUEsNkJBQTZCO0FBQzdCLGlFQUFlQSxNQUFNQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFya2V0cGxhY2UvLi9zcmMvbGliL3ByaXNtYS50cz8wMWQ3Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFByaXNtYUNsaWVudCB9IGZyb20gJ0BwcmlzbWEvY2xpZW50JztcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWQ7XG59O1xuXG5leHBvcnQgY29uc3QgcHJpc21hID0gZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA/PyBuZXcgUHJpc21hQ2xpZW50KCk7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPSBwcmlzbWE7XG59XG5cbi8vIEZvciBiYWNrd2FyZCBjb21wYXRpYmlsaXR5XG5leHBvcnQgZGVmYXVsdCBwcmlzbWE7Il0sIm5hbWVzIjpbIlByaXNtYUNsaWVudCIsImdsb2JhbEZvclByaXNtYSIsImdsb2JhbFRoaXMiLCJwcmlzbWEiLCJwcm9jZXNzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/bcryptjs","vendor-chunks/oauth","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/uuid","vendor-chunks/yallist","vendor-chunks/cookie","vendor-chunks/@auth","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fstats%2Froute&page=%2Fapi%2Fadmin%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fstats%2Froute.ts&appDir=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();