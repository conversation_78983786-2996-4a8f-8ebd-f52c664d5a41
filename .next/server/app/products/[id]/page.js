/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/products/[id]/page";
exports.ids = ["app/products/[id]/page"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fproducts%2F%5Bid%5D%2Fpage&page=%2Fproducts%2F%5Bid%5D%2Fpage&appPaths=%2Fproducts%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fproducts%2F%5Bid%5D%2Fpage.tsx&appDir=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fproducts%2F%5Bid%5D%2Fpage&page=%2Fproducts%2F%5Bid%5D%2Fpage&appPaths=%2Fproducts%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fproducts%2F%5Bid%5D%2Fpage.tsx&appDir=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'products',\n        {\n        children: [\n        '[id]',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/products/[id]/page.tsx */ \"(rsc)/./src/app/products/[id]/page.tsx\")), \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/[id]/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/Users/<USER>/Documents/AI Development/marketplace/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/[id]/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/products/[id]/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/products/[id]/page\",\n        pathname: \"/products/[id]\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fproducts%2F%5Bid%5D%2Fpage&page=%2Fproducts%2F%5Bid%5D%2Fpage&appPaths=%2Fproducts%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fproducts%2F%5Bid%5D%2Fpage.tsx&appDir=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fimage-component.js&modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fsrc%2Fcomponents%2FFollowButtonClient.tsx&modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fsrc%2Fcomponents%2FProductReviews.tsx&modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fsrc%2Fcomponents%2FSafetyTips.tsx&modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fsrc%2Fcomponents%2FShareButtonClient.tsx&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fimage-component.js&modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fsrc%2Fcomponents%2FFollowButtonClient.tsx&modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fsrc%2Fcomponents%2FProductReviews.tsx&modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fsrc%2Fcomponents%2FSafetyTips.tsx&modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fsrc%2Fcomponents%2FShareButtonClient.tsx&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/image-component.js */ \"(ssr)/./node_modules/next/dist/client/image-component.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/FollowButtonClient.tsx */ \"(ssr)/./src/components/FollowButtonClient.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ProductReviews.tsx */ \"(ssr)/./src/components/ProductReviews.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/SafetyTips.tsx */ \"(ssr)/./src/components/SafetyTips.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ShareButtonClient.tsx */ \"(ssr)/./src/components/ShareButtonClient.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZtYWMlMkZEb2N1bWVudHMlMkZBSSUyMERldmVsb3BtZW50JTJGbWFya2V0cGxhY2UlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmltYWdlLWNvbXBvbmVudC5qcyZtb2R1bGVzPSUyRlVzZXJzJTJGbWFjJTJGRG9jdW1lbnRzJTJGQUklMjBEZXZlbG9wbWVudCUyRm1hcmtldHBsYWNlJTJGc3JjJTJGY29tcG9uZW50cyUyRkZvbGxvd0J1dHRvbkNsaWVudC50c3gmbW9kdWxlcz0lMkZVc2VycyUyRm1hYyUyRkRvY3VtZW50cyUyRkFJJTIwRGV2ZWxvcG1lbnQlMkZtYXJrZXRwbGFjZSUyRnNyYyUyRmNvbXBvbmVudHMlMkZQcm9kdWN0UmV2aWV3cy50c3gmbW9kdWxlcz0lMkZVc2VycyUyRm1hYyUyRkRvY3VtZW50cyUyRkFJJTIwRGV2ZWxvcG1lbnQlMkZtYXJrZXRwbGFjZSUyRnNyYyUyRmNvbXBvbmVudHMlMkZTYWZldHlUaXBzLnRzeCZtb2R1bGVzPSUyRlVzZXJzJTJGbWFjJTJGRG9jdW1lbnRzJTJGQUklMjBEZXZlbG9wbWVudCUyRm1hcmtldHBsYWNlJTJGc3JjJTJGY29tcG9uZW50cyUyRlNoYXJlQnV0dG9uQ2xpZW50LnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsc05BQXFJO0FBQ3JJLDBMQUEwSDtBQUMxSCxrTEFBc0g7QUFDdEgsMEtBQWtIO0FBQ2xIIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFya2V0cGxhY2UvP2YyZWIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvbWFjL0RvY3VtZW50cy9BSSBEZXZlbG9wbWVudC9tYXJrZXRwbGFjZS9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9pbWFnZS1jb21wb25lbnQuanNcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9tYWMvRG9jdW1lbnRzL0FJIERldmVsb3BtZW50L21hcmtldHBsYWNlL3NyYy9jb21wb25lbnRzL0ZvbGxvd0J1dHRvbkNsaWVudC50c3hcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9tYWMvRG9jdW1lbnRzL0FJIERldmVsb3BtZW50L21hcmtldHBsYWNlL3NyYy9jb21wb25lbnRzL1Byb2R1Y3RSZXZpZXdzLnRzeFwiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL21hYy9Eb2N1bWVudHMvQUkgRGV2ZWxvcG1lbnQvbWFya2V0cGxhY2Uvc3JjL2NvbXBvbmVudHMvU2FmZXR5VGlwcy50c3hcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9tYWMvRG9jdW1lbnRzL0FJIERldmVsb3BtZW50L21hcmtldHBsYWNlL3NyYy9jb21wb25lbnRzL1NoYXJlQnV0dG9uQ2xpZW50LnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fimage-component.js&modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fsrc%2Fcomponents%2FFollowButtonClient.tsx&modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fsrc%2Fcomponents%2FProductReviews.tsx&modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fsrc%2Fcomponents%2FSafetyTips.tsx&modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fsrc%2Fcomponents%2FShareButtonClient.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fsrc%2Fapp%2Fglobals.css&modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fsrc%2Fapp%2Fproviders.tsx&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fsrc%2Fapp%2Fglobals.css&modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fsrc%2Fapp%2Fproviders.tsx&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/providers.tsx */ \"(ssr)/./src/app/providers.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZtYWMlMkZEb2N1bWVudHMlMkZBSSUyMERldmVsb3BtZW50JTJGbWFya2V0cGxhY2UlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZm9udCUyRmdvb2dsZSUyRnRhcmdldC5jc3MlM0YlN0IlMjJwYXRoJTIyJTNBJTIyc3JjJTJGYXBwJTJGbGF5b3V0LnRzeCUyMiUyQyUyMmltcG9ydCUyMiUzQSUyMkludGVyJTIyJTJDJTIyYXJndW1lbnRzJTIyJTNBJTVCJTdCJTIyc3Vic2V0cyUyMiUzQSU1QiUyMmxhdGluJTIyJTVEJTdEJTVEJTJDJTIydmFyaWFibGVOYW1lJTIyJTNBJTIyaW50ZXIlMjIlN0QmbW9kdWxlcz0lMkZVc2VycyUyRm1hYyUyRkRvY3VtZW50cyUyRkFJJTIwRGV2ZWxvcG1lbnQlMkZtYXJrZXRwbGFjZSUyRnNyYyUyRmFwcCUyRmdsb2JhbHMuY3NzJm1vZHVsZXM9JTJGVXNlcnMlMkZtYWMlMkZEb2N1bWVudHMlMkZBSSUyMERldmVsb3BtZW50JTJGbWFya2V0cGxhY2UlMkZzcmMlMkZhcHAlMkZwcm92aWRlcnMudHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL21hcmtldHBsYWNlLz9lMzdkIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL21hYy9Eb2N1bWVudHMvQUkgRGV2ZWxvcG1lbnQvbWFya2V0cGxhY2Uvc3JjL2FwcC9wcm92aWRlcnMudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fsrc%2Fapp%2Fglobals.css&modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fsrc%2Fapp%2Fproviders.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/providers.tsx":
/*!*******************************!*\
  !*** ./src/app/providers.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _context_UserContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../context/UserContext */ \"(ssr)/./src/context/UserContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction Providers({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_2__.SessionProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_UserContext__WEBPACK_IMPORTED_MODULE_3__.UserProvider, {\n            children: children\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/providers.tsx\",\n            lineNumber: 10,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/providers.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL3Byb3ZpZGVycy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBRTBCO0FBQ3dCO0FBQ0k7QUFFdkMsU0FBU0csVUFBVSxFQUFFQyxRQUFRLEVBQWlDO0lBQzNFLHFCQUNFLDhEQUFDSCw0REFBZUE7a0JBQ2QsNEVBQUNDLDhEQUFZQTtzQkFDVkU7Ozs7Ozs7Ozs7O0FBSVQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJrZXRwbGFjZS8uL3NyYy9hcHAvcHJvdmlkZXJzLnRzeD85MzI2Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IFNlc3Npb25Qcm92aWRlciB9IGZyb20gJ25leHQtYXV0aC9yZWFjdCc7XG5pbXBvcnQgeyBVc2VyUHJvdmlkZXIgfSBmcm9tICcuLi9jb250ZXh0L1VzZXJDb250ZXh0JztcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUHJvdmlkZXJzKHsgY2hpbGRyZW4gfTogeyBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlIH0pIHtcbiAgcmV0dXJuIChcbiAgICA8U2Vzc2lvblByb3ZpZGVyPlxuICAgICAgPFVzZXJQcm92aWRlcj5cbiAgICAgICAge2NoaWxkcmVufVxuICAgICAgPC9Vc2VyUHJvdmlkZXI+XG4gICAgPC9TZXNzaW9uUHJvdmlkZXI+XG4gICk7XG59ICJdLCJuYW1lcyI6WyJSZWFjdCIsIlNlc3Npb25Qcm92aWRlciIsIlVzZXJQcm92aWRlciIsIlByb3ZpZGVycyIsImNoaWxkcmVuIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/app/providers.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/FollowButton.tsx":
/*!*****************************************!*\
  !*** ./src/components/FollowButton.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FollowButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_UserMinusIcon_UserPlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=UserMinusIcon,UserPlusIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/UserPlusIcon.js\");\n/* harmony import */ var _barrel_optimize_names_UserMinusIcon_UserPlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=UserMinusIcon,UserPlusIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/UserMinusIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction FollowButton({ sellerId, className = \"\", onFollowChange }) {\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const [isFollowing, setIsFollowing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (session) {\n            checkFollowStatus();\n        }\n    }, [\n        session,\n        sellerId\n    ]);\n    const checkFollowStatus = async ()=>{\n        try {\n            const response = await fetch(`/api/follow/check?sellerId=${sellerId}`);\n            if (!response.ok) {\n                throw new Error(\"Failed to check follow status\");\n            }\n            const data = await response.json();\n            setIsFollowing(data.isFollowing);\n        } catch (err) {\n            console.error(\"Error checking follow status:\", err);\n        }\n    };\n    const handleFollowToggle = async ()=>{\n        if (!session) {\n            // Redirect to login or show login modal\n            return;\n        }\n        try {\n            setIsLoading(true);\n            setError(\"\");\n            if (isFollowing) {\n                // Unfollow\n                const response = await fetch(`/api/follow?sellerId=${sellerId}`, {\n                    method: \"DELETE\"\n                });\n                if (!response.ok) {\n                    throw new Error(\"Failed to unfollow seller\");\n                }\n                setIsFollowing(false);\n                if (onFollowChange) onFollowChange(false);\n            } else {\n                // Follow\n                const response = await fetch(\"/api/follow\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        sellerId\n                    })\n                });\n                if (!response.ok) {\n                    throw new Error(\"Failed to follow seller\");\n                }\n                setIsFollowing(true);\n                if (onFollowChange) onFollowChange(true);\n            }\n        } catch (err) {\n            setError(err.message || \"An error occurred\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    if (!session) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n            onClick: ()=>window.location.href = \"/api/auth/signin\",\n            className: `inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 ${className}`,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_UserMinusIcon_UserPlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"-ml-1 mr-2 h-5 w-5 text-gray-400\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/FollowButton.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 9\n                }, this),\n                \"Follow\"\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/FollowButton.tsx\",\n            lineNumber: 92,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: handleFollowToggle,\n                disabled: isLoading,\n                className: `inline-flex items-center px-4 py-2 border ${isFollowing ? \"border-gray-300 text-gray-700 bg-white hover:bg-gray-50\" : \"border-indigo-600 text-white bg-indigo-600 hover:bg-indigo-700\"} rounded-md shadow-sm text-sm font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors ${isLoading ? \"opacity-75 cursor-not-allowed\" : \"\"} ${className}`,\n                children: isFollowing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_UserMinusIcon_UserPlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            className: \"-ml-1 mr-2 h-5 w-5\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/FollowButton.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 13\n                        }, this),\n                        \"Unfollow\"\n                    ]\n                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_UserMinusIcon_UserPlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            className: \"-ml-1 mr-2 h-5 w-5\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/FollowButton.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 13\n                        }, this),\n                        \"Follow\"\n                    ]\n                }, void 0, true)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/FollowButton.tsx\",\n                lineNumber: 104,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mt-2 text-sm text-red-600\",\n                children: error\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/FollowButton.tsx\",\n                lineNumber: 129,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/FollowButton.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/FollowButtonClient.tsx":
/*!***********************************************!*\
  !*** ./src/components/FollowButtonClient.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FollowButtonClient)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _FollowButton__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./FollowButton */ \"(ssr)/./src/components/FollowButton.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction FollowButtonClient({ sellerId }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FollowButton__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        sellerId: sellerId\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/FollowButtonClient.tsx\",\n        lineNumber: 6,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9Gb2xsb3dCdXR0b25DbGllbnQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBRTBDO0FBRTNCLFNBQVNDLG1CQUFtQixFQUFFQyxRQUFRLEVBQXdCO0lBQzNFLHFCQUFPLDhEQUFDRixxREFBWUE7UUFBQ0UsVUFBVUE7Ozs7OztBQUNqQyIsInNvdXJjZXMiOlsid2VicGFjazovL21hcmtldHBsYWNlLy4vc3JjL2NvbXBvbmVudHMvRm9sbG93QnV0dG9uQ2xpZW50LnRzeD82YWM5Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IEZvbGxvd0J1dHRvbiBmcm9tICcuL0ZvbGxvd0J1dHRvbic7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEZvbGxvd0J1dHRvbkNsaWVudCh7IHNlbGxlcklkIH06IHsgc2VsbGVySWQ6IHN0cmluZyB9KSB7XG4gIHJldHVybiA8Rm9sbG93QnV0dG9uIHNlbGxlcklkPXtzZWxsZXJJZH0gLz47XG59XG4iXSwibmFtZXMiOlsiRm9sbG93QnV0dG9uIiwiRm9sbG93QnV0dG9uQ2xpZW50Iiwic2VsbGVySWQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/FollowButtonClient.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ProductReviews.tsx":
/*!*******************************************!*\
  !*** ./src/components/ProductReviews.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProductReviews)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_StarIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=StarIcon!=!@heroicons/react/24/solid */ \"(ssr)/./node_modules/@heroicons/react/24/solid/esm/StarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=StarIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/StarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,PlusIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,PlusIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* harmony import */ var _ReviewCard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ReviewCard */ \"(ssr)/./src/components/ReviewCard.tsx\");\n/* harmony import */ var _ReviewForm__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ReviewForm */ \"(ssr)/./src/components/ReviewForm.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nfunction ProductReviews({ productId, sellerId }) {\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const [reviews, setReviews] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        averageRating: 0,\n        totalReviews: 0,\n        ratingDistribution: {\n            5: 0,\n            4: 0,\n            3: 0,\n            2: 0,\n            1: 0\n        }\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showForm, setShowForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingReview, setEditingReview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [hasMore, setHasMore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const isOwner = session?.user?.id === sellerId;\n    const userReview = reviews.find((review)=>review.reviewer.id === session?.user?.id);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchReviews();\n    }, [\n        productId\n    ]);\n    const fetchReviews = async (pageNum = 1, reset = true)=>{\n        try {\n            const response = await fetch(`/api/products/${productId}/reviews?page=${pageNum}&limit=10`);\n            if (!response.ok) throw new Error(\"Failed to fetch reviews\");\n            const data = await response.json();\n            if (reset) {\n                setReviews(data.reviews);\n            } else {\n                setReviews((prev)=>[\n                        ...prev,\n                        ...data.reviews\n                    ]);\n            }\n            setStats(data.stats);\n            setHasMore(data.pagination.page < data.pagination.totalPages);\n            setPage(pageNum);\n        } catch (error) {\n            console.error(\"Error fetching reviews:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleSubmitReview = async (data)=>{\n        setIsSubmitting(true);\n        try {\n            const url = editingReview ? `/api/reviews/${editingReview.id}` : `/api/products/${productId}/reviews`;\n            const method = editingReview ? \"PUT\" : \"POST\";\n            const response = await fetch(url, {\n                method,\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(data)\n            });\n            if (!response.ok) {\n                const error = await response.json();\n                throw new Error(error.message || \"Failed to submit review\");\n            }\n            // Refresh reviews\n            await fetchReviews();\n            setShowForm(false);\n            setEditingReview(null);\n        } catch (error) {\n            console.error(\"Error submitting review:\", error);\n            alert(error instanceof Error ? error.message : \"Failed to submit review\");\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const handleDeleteReview = async (reviewId)=>{\n        try {\n            const response = await fetch(`/api/reviews/${reviewId}`, {\n                method: \"DELETE\"\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to delete review\");\n            }\n            // Refresh reviews\n            await fetchReviews();\n        } catch (error) {\n            console.error(\"Error deleting review:\", error);\n            alert(\"Failed to delete review\");\n        }\n    };\n    const handleEditReview = (review)=>{\n        setEditingReview(review);\n        setShowForm(true);\n    };\n    const loadMoreReviews = ()=>{\n        fetchReviews(page + 1, false);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-xl shadow-sm p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-6 bg-gray-200 rounded w-1/4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductReviews.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-4 bg-gray-200 rounded\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductReviews.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-4 bg-gray-200 rounded w-3/4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductReviews.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductReviews.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductReviews.tsx\",\n                lineNumber: 149,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductReviews.tsx\",\n            lineNumber: 148,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-xl shadow-sm\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold text-gray-900\",\n                                    children: \"Reviews & Feedback\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductReviews.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center mt-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                1,\n                                                2,\n                                                3,\n                                                4,\n                                                5\n                                            ].map((star)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: star <= Math.round(stats.averageRating) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_StarIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"w-5 h-5 text-yellow-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductReviews.tsx\",\n                                                        lineNumber: 172,\n                                                        columnNumber: 23\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"w-5 h-5 text-gray-300\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductReviews.tsx\",\n                                                        lineNumber: 174,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, star, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductReviews.tsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductReviews.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-2 text-gray-600\",\n                                            children: [\n                                                stats.averageRating.toFixed(1),\n                                                \" (\",\n                                                stats.totalReviews,\n                                                \" reviews)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductReviews.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductReviews.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductReviews.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, this),\n                        session && !isOwner && !userReview && !showForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setShowForm(true),\n                            className: \"flex items-center space-x-2 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductReviews.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Write Review\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductReviews.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductReviews.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductReviews.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductReviews.tsx\",\n                lineNumber: 163,\n                columnNumber: 7\n            }, this),\n            showForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ReviewForm__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    productId: productId,\n                    existingReview: editingReview || undefined,\n                    onSubmit: handleSubmitReview,\n                    onCancel: ()=>{\n                        setShowForm(false);\n                        setEditingReview(null);\n                    },\n                    isSubmitting: isSubmitting\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductReviews.tsx\",\n                    lineNumber: 201,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductReviews.tsx\",\n                lineNumber: 200,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6\",\n                children: reviews.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-500\",\n                        children: \"No reviews yet. Be the first to review this product!\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductReviews.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductReviews.tsx\",\n                    lineNumber: 217,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        reviews.map((review)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ReviewCard__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                review: review,\n                                onEdit: handleEditReview,\n                                onDelete: handleDeleteReview\n                            }, review.id, false, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductReviews.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 15\n                            }, this)),\n                        hasMore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center pt-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: loadMoreReviews,\n                                className: \"flex items-center space-x-2 mx-auto px-4 py-2 text-indigo-600 border border-indigo-600 rounded-lg hover:bg-indigo-50 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductReviews.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Load More Reviews\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductReviews.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductReviews.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductReviews.tsx\",\n                            lineNumber: 233,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductReviews.tsx\",\n                    lineNumber: 221,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductReviews.tsx\",\n                lineNumber: 215,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductReviews.tsx\",\n        lineNumber: 161,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ProductReviews.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ReviewCard.tsx":
/*!***************************************!*\
  !*** ./src/components/ReviewCard.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ReviewCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_StarIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=StarIcon!=!@heroicons/react/24/solid */ \"(ssr)/./node_modules/@heroicons/react/24/solid/esm/StarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=StarIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/StarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_EllipsisVerticalIcon_PencilIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=EllipsisVerticalIcon,PencilIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/EllipsisVerticalIcon.js\");\n/* harmony import */ var _barrel_optimize_names_EllipsisVerticalIcon_PencilIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=EllipsisVerticalIcon,PencilIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js\");\n/* harmony import */ var _barrel_optimize_names_EllipsisVerticalIcon_PencilIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=EllipsisVerticalIcon,PencilIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction ReviewCard({ review, onEdit, onDelete }) {\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const [showMenu, setShowMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isDeleting, setIsDeleting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const isOwner = session?.user?.id === review.reviewer.id;\n    const formattedDate = new Date(review.createdAt).toLocaleDateString(\"en-US\", {\n        year: \"numeric\",\n        month: \"long\",\n        day: \"numeric\"\n    });\n    const handleDelete = async ()=>{\n        if (!onDelete || !window.confirm(\"Are you sure you want to delete this review?\")) {\n            return;\n        }\n        setIsDeleting(true);\n        try {\n            await onDelete(review.id);\n        } catch (error) {\n            console.error(\"Error deleting review:\", error);\n        } finally{\n            setIsDeleting(false);\n            setShowMenu(false);\n        }\n    };\n    const handleEdit = ()=>{\n        if (onEdit) {\n            onEdit(review);\n        }\n        setShowMenu(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white border border-gray-200 rounded-lg p-4 hover:shadow-sm transition-shadow\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-start justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-start space-x-3 flex-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-shrink-0\",\n                                children: review.reviewer.image ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative w-10 h-10\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        src: review.reviewer.image,\n                                        alt: review.reviewer.name || \"Reviewer\",\n                                        fill: true,\n                                        className: \"rounded-full object-cover\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ReviewCard.tsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ReviewCard.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium text-gray-600\",\n                                        children: review.reviewer.name?.charAt(0)?.toUpperCase() || \"?\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ReviewCard.tsx\",\n                                        lineNumber: 84,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ReviewCard.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ReviewCard.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 min-w-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 mb-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-sm font-medium text-gray-900 truncate\",\n                                                children: review.reviewer.name || \"Anonymous\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ReviewCard.tsx\",\n                                                lineNumber: 94,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: \"•\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ReviewCard.tsx\",\n                                                lineNumber: 97,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: formattedDate\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ReviewCard.tsx\",\n                                                lineNumber: 98,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ReviewCard.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-1 mb-2\",\n                                        children: [\n                                            [\n                                                1,\n                                                2,\n                                                3,\n                                                4,\n                                                5\n                                            ].map((star)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: star <= review.rating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_StarIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        className: \"w-4 h-4 text-yellow-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ReviewCard.tsx\",\n                                                        lineNumber: 106,\n                                                        columnNumber: 21\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"w-4 h-4 text-gray-300\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ReviewCard.tsx\",\n                                                        lineNumber: 108,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, star, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ReviewCard.tsx\",\n                                                    lineNumber: 104,\n                                                    columnNumber: 17\n                                                }, this)),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600 ml-2\",\n                                                children: [\n                                                    \"(\",\n                                                    review.rating,\n                                                    \"/5)\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ReviewCard.tsx\",\n                                                lineNumber: 112,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ReviewCard.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 13\n                                    }, this),\n                                    review.comment && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-700 leading-relaxed\",\n                                        children: review.comment\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ReviewCard.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ReviewCard.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ReviewCard.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 9\n                    }, this),\n                    isOwner && (onEdit || onDelete) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowMenu(!showMenu),\n                                className: \"p-1 text-gray-400 hover:text-gray-600 transition-colors\",\n                                disabled: isDeleting,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EllipsisVerticalIcon_PencilIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ReviewCard.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ReviewCard.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 13\n                            }, this),\n                            showMenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute right-0 top-8 w-32 bg-white border border-gray-200 rounded-md shadow-lg z-10\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"py-1\",\n                                    children: [\n                                        onEdit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleEdit,\n                                            className: \"flex items-center space-x-2 w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EllipsisVerticalIcon_PencilIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ReviewCard.tsx\",\n                                                    lineNumber: 145,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Edit\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ReviewCard.tsx\",\n                                                    lineNumber: 146,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ReviewCard.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 21\n                                        }, this),\n                                        onDelete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleDelete,\n                                            disabled: isDeleting,\n                                            className: \"flex items-center space-x-2 w-full px-3 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors disabled:opacity-50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EllipsisVerticalIcon_PencilIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ReviewCard.tsx\",\n                                                    lineNumber: 155,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: isDeleting ? \"Deleting...\" : \"Delete\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ReviewCard.tsx\",\n                                                    lineNumber: 156,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ReviewCard.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ReviewCard.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ReviewCard.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ReviewCard.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ReviewCard.tsx\",\n                lineNumber: 69,\n                columnNumber: 7\n            }, this),\n            showMenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-0\",\n                onClick: ()=>setShowMenu(false)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ReviewCard.tsx\",\n                lineNumber: 168,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ReviewCard.tsx\",\n        lineNumber: 68,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ReviewCard.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ReviewForm.tsx":
/*!***************************************!*\
  !*** ./src/components/ReviewForm.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ReviewForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_StarIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=StarIcon!=!@heroicons/react/24/solid */ \"(ssr)/./node_modules/@heroicons/react/24/solid/esm/StarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=StarIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/StarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction ReviewForm({ productId, existingReview, onSubmit, onCancel, isSubmitting = false }) {\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const [rating, setRating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(existingReview?.rating || 0);\n    const [comment, setComment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(existingReview?.comment || \"\");\n    const [hoveredRating, setHoveredRating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (rating === 0) {\n            alert(\"Please select a rating\");\n            return;\n        }\n        try {\n            await onSubmit({\n                rating,\n                comment\n            });\n        } catch (error) {\n            console.error(\"Error submitting review:\", error);\n        }\n    };\n    if (!session) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-gray-50 border border-gray-200 rounded-lg p-6 text-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-gray-600\",\n                children: \"Please sign in to leave a review\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ReviewForm.tsx\",\n                lineNumber: 59,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ReviewForm.tsx\",\n            lineNumber: 58,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white border border-gray-200 rounded-lg p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900\",\n                        children: existingReview ? \"Edit Your Review\" : \"Write a Review\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ReviewForm.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onCancel,\n                        className: \"p-1 text-gray-400 hover:text-gray-600 transition-colors\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            className: \"w-5 h-5\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ReviewForm.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ReviewForm.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ReviewForm.tsx\",\n                lineNumber: 66,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit,\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                children: \"Rating *\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ReviewForm.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-1\",\n                                children: [\n                                    [\n                                        1,\n                                        2,\n                                        3,\n                                        4,\n                                        5\n                                    ].map((star)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setRating(star),\n                                            onMouseEnter: ()=>setHoveredRating(star),\n                                            onMouseLeave: ()=>setHoveredRating(0),\n                                            className: \"p-1 transition-transform hover:scale-110\",\n                                            children: star <= (hoveredRating || rating) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_StarIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"w-8 h-8 text-yellow-400\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ReviewForm.tsx\",\n                                                lineNumber: 95,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"w-8 h-8 text-gray-300\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ReviewForm.tsx\",\n                                                lineNumber: 97,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, star, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ReviewForm.tsx\",\n                                            lineNumber: 86,\n                                            columnNumber: 15\n                                        }, this)),\n                                    rating > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2 text-sm text-gray-600\",\n                                        children: [\n                                            \"(\",\n                                            rating,\n                                            \"/5)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ReviewForm.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ReviewForm.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ReviewForm.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"comment\",\n                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                children: \"Comment (optional)\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ReviewForm.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                id: \"comment\",\n                                value: comment,\n                                onChange: (e)=>setComment(e.target.value),\n                                rows: 4,\n                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 resize-none\",\n                                placeholder: \"Share your experience with this product...\",\n                                maxLength: 500\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ReviewForm.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-1 text-xs text-gray-500 text-right\",\n                                children: [\n                                    comment.length,\n                                    \"/500 characters\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ReviewForm.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ReviewForm.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-end space-x-3 pt-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: onCancel,\n                                className: \"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors\",\n                                disabled: isSubmitting,\n                                children: \"Cancel\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ReviewForm.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                disabled: rating === 0 || isSubmitting,\n                                className: \"px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                                children: isSubmitting ? \"Submitting...\" : existingReview ? \"Update Review\" : \"Submit Review\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ReviewForm.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ReviewForm.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ReviewForm.tsx\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ReviewForm.tsx\",\n        lineNumber: 65,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ReviewForm.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/SafetyTips.tsx":
/*!***************************************!*\
  !*** ./src/components/SafetyTips.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SafetyTips)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronUpIcon_CreditCardIcon_DocumentCheckIcon_ExclamationTriangleIcon_EyeIcon_MapPinIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronUpIcon,CreditCardIcon,DocumentCheckIcon,ExclamationTriangleIcon,EyeIcon,MapPinIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CreditCardIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronUpIcon_CreditCardIcon_DocumentCheckIcon_ExclamationTriangleIcon_EyeIcon_MapPinIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronUpIcon,CreditCardIcon,DocumentCheckIcon,ExclamationTriangleIcon,EyeIcon,MapPinIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/MapPinIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronUpIcon_CreditCardIcon_DocumentCheckIcon_ExclamationTriangleIcon_EyeIcon_MapPinIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronUpIcon,CreditCardIcon,DocumentCheckIcon,ExclamationTriangleIcon,EyeIcon,MapPinIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronUpIcon_CreditCardIcon_DocumentCheckIcon_ExclamationTriangleIcon_EyeIcon_MapPinIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronUpIcon,CreditCardIcon,DocumentCheckIcon,ExclamationTriangleIcon,EyeIcon,MapPinIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/DocumentCheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronUpIcon_CreditCardIcon_DocumentCheckIcon_ExclamationTriangleIcon_EyeIcon_MapPinIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronUpIcon,CreditCardIcon,DocumentCheckIcon,ExclamationTriangleIcon,EyeIcon,MapPinIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronUpIcon_CreditCardIcon_DocumentCheckIcon_ExclamationTriangleIcon_EyeIcon_MapPinIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronUpIcon,CreditCardIcon,DocumentCheckIcon,ExclamationTriangleIcon,EyeIcon,MapPinIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronUpIcon_CreditCardIcon_DocumentCheckIcon_ExclamationTriangleIcon_EyeIcon_MapPinIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronUpIcon,CreditCardIcon,DocumentCheckIcon,ExclamationTriangleIcon,EyeIcon,MapPinIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChevronUpIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronUpIcon_CreditCardIcon_DocumentCheckIcon_ExclamationTriangleIcon_EyeIcon_MapPinIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronUpIcon,CreditCardIcon,DocumentCheckIcon,ExclamationTriangleIcon,EyeIcon,MapPinIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst safetyTips = [\n    {\n        id: \"payment\",\n        icon: _barrel_optimize_names_ChevronDownIcon_ChevronUpIcon_CreditCardIcon_DocumentCheckIcon_ExclamationTriangleIcon_EyeIcon_MapPinIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        title: \"Never send prepayments\",\n        description: \"Avoid sending money, gift cards, or cryptocurrency before seeing the item in person. Legitimate sellers will accept payment upon delivery or pickup.\",\n        color: \"text-red-600 bg-red-50 border-red-200\"\n    },\n    {\n        id: \"meeting\",\n        icon: _barrel_optimize_names_ChevronDownIcon_ChevronUpIcon_CreditCardIcon_DocumentCheckIcon_ExclamationTriangleIcon_EyeIcon_MapPinIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        title: \"Meet in safe public places\",\n        description: \"Choose well-lit, busy public locations like shopping centers, coffee shops, or police station parking lots. Bring a friend if possible.\",\n        color: \"text-blue-600 bg-blue-50 border-blue-200\"\n    },\n    {\n        id: \"inspection\",\n        icon: _barrel_optimize_names_ChevronDownIcon_ChevronUpIcon_CreditCardIcon_DocumentCheckIcon_ExclamationTriangleIcon_EyeIcon_MapPinIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        title: \"Inspect before you buy\",\n        description: \"Thoroughly examine the item to ensure it matches the description and photos. Test electronics and check for any damage or defects.\",\n        color: \"text-green-600 bg-green-50 border-green-200\"\n    },\n    {\n        id: \"documentation\",\n        icon: _barrel_optimize_names_ChevronDownIcon_ChevronUpIcon_CreditCardIcon_DocumentCheckIcon_ExclamationTriangleIcon_EyeIcon_MapPinIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        title: \"Verify documentation\",\n        description: \"For valuable items, ask for receipts, warranties, or proof of ownership. Check serial numbers and authenticity when applicable.\",\n        color: \"text-purple-600 bg-purple-50 border-purple-200\"\n    }\n];\nfunction SafetyTips() {\n    const [isExpanded, setIsExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 cursor-pointer hover:bg-gray-50 transition-colors\",\n                onClick: ()=>setIsExpanded(!isExpanded),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-2 bg-orange-100 rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronUpIcon_CreditCardIcon_DocumentCheckIcon_ExclamationTriangleIcon_EyeIcon_MapPinIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-6 h-6 text-orange-600\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/SafetyTips.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/SafetyTips.tsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900\",\n                                            children: \"Safety Tips\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/SafetyTips.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Stay safe while buying and selling\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/SafetyTips.tsx\",\n                                            lineNumber: 70,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/SafetyTips.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/SafetyTips.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronUpIcon_CreditCardIcon_DocumentCheckIcon_ExclamationTriangleIcon_EyeIcon_MapPinIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"w-5 h-5 text-orange-500\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/SafetyTips.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 13\n                                }, this),\n                                isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronUpIcon_CreditCardIcon_DocumentCheckIcon_ExclamationTriangleIcon_EyeIcon_MapPinIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"w-5 h-5 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/SafetyTips.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronUpIcon_CreditCardIcon_DocumentCheckIcon_ExclamationTriangleIcon_EyeIcon_MapPinIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-5 h-5 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/SafetyTips.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/SafetyTips.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/SafetyTips.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/SafetyTips.tsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, this),\n            isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 space-y-4\",\n                        children: safetyTips.map((tip)=>{\n                            const IconComponent = tip.icon;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `p-4 rounded-lg border ${tip.color}`,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-shrink-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/SafetyTips.tsx\",\n                                                lineNumber: 96,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/SafetyTips.tsx\",\n                                            lineNumber: 95,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900 mb-1\",\n                                                    children: tip.title\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/SafetyTips.tsx\",\n                                                    lineNumber: 99,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-700\",\n                                                    children: tip.description\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/SafetyTips.tsx\",\n                                                    lineNumber: 102,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/SafetyTips.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/SafetyTips.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 19\n                                }, this)\n                            }, tip.id, false, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/SafetyTips.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/SafetyTips.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 pb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 rounded-lg p-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronUpIcon_CreditCardIcon_DocumentCheckIcon_ExclamationTriangleIcon_EyeIcon_MapPinIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-4 h-4 text-gray-500 mt-0.5 flex-shrink-0\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/SafetyTips.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Remember:\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/SafetyTips.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 19\n                                            }, this),\n                                            \" If a deal seems too good to be true, it probably is. Trust your instincts and report suspicious activity to keep our marketplace safe for everyone.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/SafetyTips.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/SafetyTips.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/SafetyTips.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/SafetyTips.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/SafetyTips.tsx\",\n                lineNumber: 85,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/SafetyTips.tsx\",\n        lineNumber: 58,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/SafetyTips.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ShareButton.tsx":
/*!****************************************!*\
  !*** ./src/components/ShareButton.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ShareButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_CheckIcon_ClipboardIcon_EnvelopeIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,ClipboardIcon,EnvelopeIcon,ShareIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ShareIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_ClipboardIcon_EnvelopeIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,ClipboardIcon,EnvelopeIcon,ShareIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_ClipboardIcon_EnvelopeIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,ClipboardIcon,EnvelopeIcon,ShareIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ClipboardIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_ClipboardIcon_EnvelopeIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,ClipboardIcon,EnvelopeIcon,ShareIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/EnvelopeIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction ShareButton({ title, url, description = \"\", className = \"\" }) {\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [copied, setCopied] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const dropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Close dropdown when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        function handleClickOutside(event) {\n            if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n                setIsOpen(false);\n            }\n        }\n        document.addEventListener(\"mousedown\", handleClickOutside);\n        return ()=>{\n            document.removeEventListener(\"mousedown\", handleClickOutside);\n        };\n    }, []);\n    // Reset copied state after 2 seconds\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (copied) {\n            const timer = setTimeout(()=>{\n                setCopied(false);\n            }, 2000);\n            return ()=>clearTimeout(timer);\n        }\n    }, [\n        copied\n    ]);\n    const handleShare = async ()=>{\n        // Use Web Share API if available\n        if (navigator.share) {\n            try {\n                await navigator.share({\n                    title,\n                    text: description,\n                    url\n                });\n                // Track share event\n                trackShareEvent(\"native\");\n                return;\n            } catch (error) {\n                console.error(\"Error sharing:\", error);\n            }\n        }\n        // Fallback to dropdown\n        setIsOpen(!isOpen);\n    };\n    const copyToClipboard = ()=>{\n        navigator.clipboard.writeText(url);\n        setCopied(true);\n        setIsOpen(false);\n        // Track share event\n        trackShareEvent(\"copy\");\n    };\n    const shareViaEmail = ()=>{\n        const subject = encodeURIComponent(title);\n        const body = encodeURIComponent(`${description}\\n\\n${url}`);\n        window.open(`mailto:?subject=${subject}&body=${body}`);\n        setIsOpen(false);\n        // Track share event\n        trackShareEvent(\"email\");\n    };\n    const shareViaFacebook = ()=>{\n        window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`);\n        setIsOpen(false);\n        // Track share event\n        trackShareEvent(\"facebook\");\n    };\n    const shareViaTwitter = ()=>{\n        const text = encodeURIComponent(`${title}\\n${url}`);\n        window.open(`https://twitter.com/intent/tweet?text=${text}`);\n        setIsOpen(false);\n        // Track share event\n        trackShareEvent(\"twitter\");\n    };\n    const shareViaWhatsApp = ()=>{\n        const text = encodeURIComponent(`${title}\\n${url}`);\n        window.open(`https://wa.me/?text=${text}`);\n        setIsOpen(false);\n        // Track share event\n        trackShareEvent(\"whatsapp\");\n    };\n    // Track share events\n    const trackShareEvent = async (platform)=>{\n        try {\n            await fetch(\"/api/share-analytics\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    url,\n                    platform\n                })\n            });\n        } catch (error) {\n            console.error(\"Error tracking share event:\", error);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `relative ${className}`,\n        ref: dropdownRef,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: handleShare,\n                className: \"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500\",\n                \"aria-label\": \"Share\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_ClipboardIcon_EnvelopeIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        className: \"-ml-0.5 mr-2 h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ShareButton.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 9\n                    }, this),\n                    \"Share\"\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ShareButton.tsx\",\n                lineNumber: 133,\n                columnNumber: 7\n            }, this),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"py-1\",\n                    role: \"menu\",\n                    \"aria-orientation\": \"vertical\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: copyToClipboard,\n                            className: \"w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center\",\n                            role: \"menuitem\",\n                            children: [\n                                copied ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_ClipboardIcon_EnvelopeIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"mr-3 h-5 w-5 text-green-500\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ShareButton.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 17\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_ClipboardIcon_EnvelopeIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"mr-3 h-5 w-5 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ShareButton.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 17\n                                }, this),\n                                copied ? \"Copied!\" : \"Copy Link\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ShareButton.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: shareViaEmail,\n                            className: \"w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center\",\n                            role: \"menuitem\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_ClipboardIcon_EnvelopeIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"mr-3 h-5 w-5 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ShareButton.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 15\n                                }, this),\n                                \"Email\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ShareButton.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: shareViaFacebook,\n                            className: \"w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center\",\n                            role: \"menuitem\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"mr-3 h-5 w-5 text-blue-600\",\n                                    fill: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ShareButton.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ShareButton.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 15\n                                }, this),\n                                \"Facebook\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ShareButton.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: shareViaTwitter,\n                            className: \"w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center\",\n                            role: \"menuitem\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"mr-3 h-5 w-5 text-blue-400\",\n                                    fill: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723 10.054 10.054 0 01-3.127 1.184 4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ShareButton.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ShareButton.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 15\n                                }, this),\n                                \"Twitter\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ShareButton.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: shareViaWhatsApp,\n                            className: \"w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center\",\n                            role: \"menuitem\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"mr-3 h-5 w-5 text-green-500\",\n                                    fill: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893a11.821 11.821 0 00-3.48-8.413z\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ShareButton.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ShareButton.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 15\n                                }, this),\n                                \"WhatsApp\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ShareButton.tsx\",\n                            lineNumber: 189,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ShareButton.tsx\",\n                    lineNumber: 144,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ShareButton.tsx\",\n                lineNumber: 143,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ShareButton.tsx\",\n        lineNumber: 132,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ShareButton.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ShareButtonClient.tsx":
/*!**********************************************!*\
  !*** ./src/components/ShareButtonClient.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ShareButtonClient)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ShareButton__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ShareButton */ \"(ssr)/./src/components/ShareButton.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction ShareButtonClient({ title, description }) {\n    const [url, setUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setUrl(window.location.href);\n    }, []);\n    if (!url) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ShareButton__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        title: title,\n        url: url,\n        description: description\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ShareButtonClient.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9TaGFyZUJ1dHRvbkNsaWVudC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUU0QztBQUNKO0FBRXpCLFNBQVNHLGtCQUFrQixFQUN4Q0MsS0FBSyxFQUNMQyxXQUFXLEVBSVo7SUFDQyxNQUFNLENBQUNDLEtBQUtDLE9BQU8sR0FBR04sK0NBQVFBLENBQUM7SUFFL0JELGdEQUFTQSxDQUFDO1FBQ1JPLE9BQU9DLE9BQU9DLFFBQVEsQ0FBQ0MsSUFBSTtJQUM3QixHQUFHLEVBQUU7SUFFTCxJQUFJLENBQUNKLEtBQUssT0FBTztJQUVqQixxQkFDRSw4REFBQ0osb0RBQVdBO1FBQ1ZFLE9BQU9BO1FBQ1BFLEtBQUtBO1FBQ0xELGFBQWFBOzs7Ozs7QUFHbkIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJrZXRwbGFjZS8uL3NyYy9jb21wb25lbnRzL1NoYXJlQnV0dG9uQ2xpZW50LnRzeD9kZTQxIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgdXNlRWZmZWN0LCB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCBTaGFyZUJ1dHRvbiBmcm9tICcuL1NoYXJlQnV0dG9uJztcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gU2hhcmVCdXR0b25DbGllbnQoeyBcbiAgdGl0bGUsIFxuICBkZXNjcmlwdGlvbiBcbn06IHsgXG4gIHRpdGxlOiBzdHJpbmc7XG4gIGRlc2NyaXB0aW9uPzogc3RyaW5nO1xufSkge1xuICBjb25zdCBbdXJsLCBzZXRVcmxdID0gdXNlU3RhdGUoJycpO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgc2V0VXJsKHdpbmRvdy5sb2NhdGlvbi5ocmVmKTtcbiAgfSwgW10pO1xuXG4gIGlmICghdXJsKSByZXR1cm4gbnVsbDtcblxuICByZXR1cm4gKFxuICAgIDxTaGFyZUJ1dHRvbiBcbiAgICAgIHRpdGxlPXt0aXRsZX0gXG4gICAgICB1cmw9e3VybH0gXG4gICAgICBkZXNjcmlwdGlvbj17ZGVzY3JpcHRpb259IFxuICAgIC8+XG4gICk7XG59XG4iXSwibmFtZXMiOlsidXNlRWZmZWN0IiwidXNlU3RhdGUiLCJTaGFyZUJ1dHRvbiIsIlNoYXJlQnV0dG9uQ2xpZW50IiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsInVybCIsInNldFVybCIsIndpbmRvdyIsImxvY2F0aW9uIiwiaHJlZiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ShareButtonClient.tsx\n");

/***/ }),

/***/ "(ssr)/./src/context/UserContext.tsx":
/*!*************************************!*\
  !*** ./src/context/UserContext.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UserProvider: () => (/* binding */ UserProvider),\n/* harmony export */   useUser: () => (/* binding */ useUser)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ UserProvider,useUser auto */ \n\nconst UserContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction UserProvider({ children }) {\n    const [wishlist, setWishlist] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [recentlyViewed, setRecentlyViewed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Load wishlist from localStorage on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const savedWishlist = localStorage.getItem(\"wishlist\");\n        if (savedWishlist) {\n            setWishlist(JSON.parse(savedWishlist));\n        }\n    }, []);\n    // Save wishlist to localStorage whenever it changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        localStorage.setItem(\"wishlist\", JSON.stringify(wishlist));\n    }, [\n        wishlist\n    ]);\n    const addToWishlist = (product)=>{\n        setWishlist((prev)=>{\n            if (!prev.find((p)=>p.id === product.id)) {\n                return [\n                    ...prev,\n                    product\n                ];\n            }\n            return prev;\n        });\n    };\n    const removeFromWishlist = (productId)=>{\n        setWishlist((prev)=>prev.filter((p)=>p.id !== productId));\n    };\n    const addToRecentlyViewed = (product)=>{\n        setRecentlyViewed((prev)=>{\n            const filtered = prev.filter((p)=>p.id !== product.id);\n            return [\n                product,\n                ...filtered\n            ].slice(0, 10); // Keep only last 10 items\n        });\n    };\n    const clearRecentlyViewed = ()=>{\n        setRecentlyViewed([]);\n    };\n    const isInWishlist = (productId)=>{\n        return wishlist.some((p)=>p.id === productId);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UserContext.Provider, {\n        value: {\n            wishlist,\n            recentlyViewed,\n            addToWishlist,\n            removeFromWishlist,\n            addToRecentlyViewed,\n            clearRecentlyViewed,\n            isInWishlist\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/context/UserContext.tsx\",\n        lineNumber: 64,\n        columnNumber: 5\n    }, this);\n}\nfunction useUser() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(UserContext);\n    if (context === undefined) {\n        throw new Error(\"useUser must be used within a UserProvider\");\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/context/UserContext.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"149448ddc89a\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFya2V0cGxhY2UvLi9zcmMvYXBwL2dsb2JhbHMuY3NzPzJlNDAiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIxNDk0NDhkZGM4OWFcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/api/auth/[...nextauth]/route.ts":
/*!*************************************************!*\
  !*** ./src/app/api/auth/[...nextauth]/route.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ handler),\n/* harmony export */   POST: () => (/* binding */ handler),\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var _auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @auth/prisma-adapter */ \"(rsc)/./node_modules/@auth/prisma-adapter/index.js\");\n/* harmony import */ var next_auth_next__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/next */ \"(rsc)/./node_modules/next-auth/next/index.js\");\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n\n\n\n\n\n\nconst authOptions = {\n    adapter: (0,_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_0__.PrismaAdapter)(_lib_prisma__WEBPACK_IMPORTED_MODULE_5__.prisma),\n    providers: [\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_3__[\"default\"])({\n            clientId: process.env.GOOGLE_CLIENT_ID,\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET\n        }),\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n            name: \"credentials\",\n            credentials: {\n                email: {\n                    label: \"Email\",\n                    type: \"email\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.email || !credentials?.password) {\n                    throw new Error(\"Invalid credentials\");\n                }\n                const user = await _lib_prisma__WEBPACK_IMPORTED_MODULE_5__.prisma.user.findUnique({\n                    where: {\n                        email: credentials.email\n                    }\n                });\n                if (!user || !user?.hashedPassword) {\n                    throw new Error(\"Invalid credentials\");\n                }\n                const isCorrectPassword = await bcryptjs__WEBPACK_IMPORTED_MODULE_4__[\"default\"].compare(credentials.password, user.hashedPassword);\n                if (!isCorrectPassword) {\n                    throw new Error(\"Invalid credentials\");\n                }\n                return user;\n            }\n        })\n    ],\n    pages: {\n        signIn: \"/login\"\n    },\n    debug: \"development\" === \"development\",\n    session: {\n        strategy: \"jwt\"\n    },\n    secret: process.env.NEXTAUTH_SECRET,\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                token.id = user.id;\n                token.role = user.role;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (session.user) {\n                session.user.id = token.id;\n                session.user.role = token.role;\n            }\n            return session;\n        }\n    }\n};\nconst handler = (0,next_auth_next__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(authOptions);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/[...nextauth]/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./providers */ \"(rsc)/./src/app/providers.tsx\");\n\n\n\n\nconst metadata = {\n    title: \"Marketplace\",\n    description: \"A modern marketplace for buying and selling products\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/layout.tsx\",\n                lineNumber: 21,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/layout.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/layout.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBS01BO0FBSGdCO0FBQ2E7QUFJNUIsTUFBTUUsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUM7QUFFYyxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztrQkFDVCw0RUFBQ0M7WUFBS0MsV0FBV1YsK0pBQWU7c0JBQzlCLDRFQUFDQyxrREFBU0E7MEJBQ1BLOzs7Ozs7Ozs7Ozs7Ozs7O0FBS1giLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJrZXRwbGFjZS8uL3NyYy9hcHAvbGF5b3V0LnRzeD81N2E5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tICduZXh0J1xuaW1wb3J0IHsgSW50ZXIgfSBmcm9tICduZXh0L2ZvbnQvZ29vZ2xlJ1xuaW1wb3J0ICcuL2dsb2JhbHMuY3NzJ1xuaW1wb3J0IFByb3ZpZGVycyBmcm9tICcuL3Byb3ZpZGVycydcblxuY29uc3QgaW50ZXIgPSBJbnRlcih7IHN1YnNldHM6IFsnbGF0aW4nXSB9KVxuXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xuICB0aXRsZTogJ01hcmtldHBsYWNlJyxcbiAgZGVzY3JpcHRpb246ICdBIG1vZGVybiBtYXJrZXRwbGFjZSBmb3IgYnV5aW5nIGFuZCBzZWxsaW5nIHByb2R1Y3RzJyxcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufToge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXG59KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImVuXCI+XG4gICAgICA8Ym9keSBjbGFzc05hbWU9e2ludGVyLmNsYXNzTmFtZX0+XG4gICAgICAgIDxQcm92aWRlcnM+XG4gICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICA8L1Byb3ZpZGVycz5cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn0gIl0sIm5hbWVzIjpbImludGVyIiwiUHJvdmlkZXJzIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJib2R5IiwiY2xhc3NOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/products/[id]/page.tsx":
/*!****************************************!*\
  !*** ./src/app/products/[id]/page.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProductPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _app_api_auth_nextauth_route__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/api/auth/[...nextauth]/route */ \"(rsc)/./src/app/api/auth/[...nextauth]/route.ts\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/image */ \"(rsc)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _components_StartChatButton__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/StartChatButton */ \"(rsc)/./src/components/StartChatButton.tsx\");\n/* harmony import */ var _components_FollowButtonClient__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/FollowButtonClient */ \"(rsc)/./src/components/FollowButtonClient.tsx\");\n/* harmony import */ var _components_ShareButtonClient__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ShareButtonClient */ \"(rsc)/./src/components/ShareButtonClient.tsx\");\n/* harmony import */ var _components_ProductReviews__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ProductReviews */ \"(rsc)/./src/components/ProductReviews.tsx\");\n/* harmony import */ var _components_SafetyTips__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/SafetyTips */ \"(rsc)/./src/components/SafetyTips.tsx\");\n\n\n\n\n\n\n\n\n\n\n\nasync function getProduct(id) {\n    return _lib_prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.product.findUnique({\n        where: {\n            id\n        },\n        include: {\n            seller: {\n                select: {\n                    id: true,\n                    name: true,\n                    image: true,\n                    rating: true,\n                    verificationStatus: true\n                }\n            },\n            category: true\n        }\n    });\n}\nasync function ProductPage({ params }) {\n    const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_2__.getServerSession)(_app_api_auth_nextauth_route__WEBPACK_IMPORTED_MODULE_3__.authOptions);\n    const product = await getProduct(params.id);\n    if (!product) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: \"Product not found\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/[id]/page.tsx\",\n            lineNumber: 39,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto px-4 py-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative aspect-square\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        src: product.images?.[0] || \"/placeholder-product.jpg\",\n                                        alt: product.name,\n                                        fill: true,\n                                        className: \"object-cover rounded-lg\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/[id]/page.tsx\",\n                                        lineNumber: 49,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/[id]/page.tsx\",\n                                    lineNumber: 48,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-4 gap-4\",\n                                    children: product.images?.slice(1).map((image, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative aspect-square\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                src: image,\n                                                alt: `${product.name} - Image ${index + 2}`,\n                                                fill: true,\n                                                className: \"object-cover rounded-lg\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/[id]/page.tsx\",\n                                                lineNumber: 59,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, index, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/[id]/page.tsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/[id]/page.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/[id]/page.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-3xl font-bold\",\n                                            children: product.name\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/[id]/page.tsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-semibold text-indigo-600 mt-2\",\n                                            children: [\n                                                \"$\",\n                                                product.price.toFixed(2)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/[id]/page.tsx\",\n                                            lineNumber: 74,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/[id]/page.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative w-12 h-12\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        src: product.seller.image || \"/default-avatar.png\",\n                                                        alt: product.seller.name,\n                                                        fill: true,\n                                                        className: \"rounded-full object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/[id]/page.tsx\",\n                                                        lineNumber: 82,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/[id]/page.tsx\",\n                                                    lineNumber: 81,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium\",\n                                                            children: product.seller.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/[id]/page.tsx\",\n                                                            lineNumber: 90,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-500\",\n                                                                    children: [\n                                                                        \"Rating: \",\n                                                                        product.seller.rating.toFixed(1)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/[id]/page.tsx\",\n                                                                    lineNumber: 92,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                product.seller.verificationStatus === \"VERIFIED\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs bg-green-100 text-green-800 px-2 py-1 rounded\",\n                                                                    children: \"Verified\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/[id]/page.tsx\",\n                                                                    lineNumber: 96,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/[id]/page.tsx\",\n                                                            lineNumber: 91,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/[id]/page.tsx\",\n                                                    lineNumber: 89,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/[id]/page.tsx\",\n                                            lineNumber: 80,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FollowButtonClient__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            sellerId: product.seller.id\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/[id]/page.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/[id]/page.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"prose max-w-none\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: product.description\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/[id]/page.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/[id]/page.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-medium mb-2\",\n                                                    children: \"Category\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/[id]/page.tsx\",\n                                                    lineNumber: 112,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: product.category.name\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/[id]/page.tsx\",\n                                                    lineNumber: 113,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/[id]/page.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-medium mb-2\",\n                                                    children: \"Condition\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/[id]/page.tsx\",\n                                                    lineNumber: 117,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: product.condition\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/[id]/page.tsx\",\n                                                    lineNumber: 118,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/[id]/page.tsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 15\n                                        }, this),\n                                        product.location && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-medium mb-2\",\n                                                    children: \"Location\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/[id]/page.tsx\",\n                                                    lineNumber: 123,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: product.location\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/[id]/page.tsx\",\n                                                    lineNumber: 124,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/[id]/page.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/[id]/page.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"pt-6 space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_StartChatButton__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    productId: product.id,\n                                                    sellerId: product.seller.id,\n                                                    className: \"flex-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/[id]/page.tsx\",\n                                                    lineNumber: 131,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ShareButtonClient__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    title: product.name,\n                                                    description: product.description || undefined\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/[id]/page.tsx\",\n                                                    lineNumber: 136,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/[id]/page.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-500 flex items-center justify-end\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    product.shareCount || 0,\n                                                    \" shares\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/[id]/page.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/[id]/page.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/[id]/page.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/[id]/page.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/[id]/page.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SafetyTips__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/[id]/page.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/[id]/page.tsx\",\n                    lineNumber: 149,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProductReviews__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        productId: product.id,\n                        sellerId: product.seller.id\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/[id]/page.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/[id]/page.tsx\",\n                    lineNumber: 154,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/[id]/page.tsx\",\n            lineNumber: 44,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/products/[id]/page.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/products/[id]/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/providers.tsx":
/*!*******************************!*\
  !*** ./src/app/providers.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/AI Development/marketplace/src/app/providers.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/components/FollowButtonClient.tsx":
/*!***********************************************!*\
  !*** ./src/components/FollowButtonClient.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/AI Development/marketplace/src/components/FollowButtonClient.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/components/ProductReviews.tsx":
/*!*******************************************!*\
  !*** ./src/components/ProductReviews.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/AI Development/marketplace/src/components/ProductReviews.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/components/SafetyTips.tsx":
/*!***************************************!*\
  !*** ./src/components/SafetyTips.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/AI Development/marketplace/src/components/SafetyTips.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/components/ShareButtonClient.tsx":
/*!**********************************************!*\
  !*** ./src/components/ShareButtonClient.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/AI Development/marketplace/src/components/ShareButtonClient.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/components/StartChatButton.tsx":
/*!********************************************!*\
  !*** ./src/components/StartChatButton.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(rsc)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth/react */ \"(rsc)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nconst StartChatButton = ({ productId, sellerId, className = \"\" })=>{\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_3__.useSession)();\n    const handleClick = async ()=>{\n        if (!session) {\n            router.push(\"/login\");\n            return;\n        }\n        try {\n            const response = await fetch(\"/api/chats\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    productId,\n                    sellerId\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to create chat\");\n            }\n            const chat = await response.json();\n            router.push(`/chats/${chat.id}`);\n        } catch (error) {\n            console.error(\"Error creating chat:\", error);\n        // You might want to show an error message to the user here\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: handleClick,\n        className: `inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 ${className}`,\n        children: \"Message Seller\"\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/StartChatButton.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (StartChatButton);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/StartChatButton.tsx\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) {\n    globalForPrisma.prisma = prisma;\n}\n// For backward compatibility\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (prisma);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQThDO0FBRTlDLE1BQU1DLGtCQUFrQkM7QUFJakIsTUFBTUMsU0FBU0YsZ0JBQWdCRSxNQUFNLElBQUksSUFBSUgsd0RBQVlBLEdBQUc7QUFFbkUsSUFBSUksSUFBeUIsRUFBYztJQUN6Q0gsZ0JBQWdCRSxNQUFNLEdBQUdBO0FBQzNCO0FBRUEsNkJBQTZCO0FBQzdCLGlFQUFlQSxNQUFNQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFya2V0cGxhY2UvLi9zcmMvbGliL3ByaXNtYS50cz8wMWQ3Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFByaXNtYUNsaWVudCB9IGZyb20gJ0BwcmlzbWEvY2xpZW50JztcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWQ7XG59O1xuXG5leHBvcnQgY29uc3QgcHJpc21hID0gZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA/PyBuZXcgUHJpc21hQ2xpZW50KCk7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPSBwcmlzbWE7XG59XG5cbi8vIEZvciBiYWNrd2FyZCBjb21wYXRpYmlsaXR5XG5leHBvcnQgZGVmYXVsdCBwcmlzbWE7Il0sIm5hbWVzIjpbIlByaXNtYUNsaWVudCIsImdsb2JhbEZvclByaXNtYSIsImdsb2JhbFRoaXMiLCJwcmlzbWEiLCJwcm9jZXNzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/@heroicons","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/bcryptjs","vendor-chunks/oauth","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/uuid","vendor-chunks/yallist","vendor-chunks/cookie","vendor-chunks/@auth","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fproducts%2F%5Bid%5D%2Fpage&page=%2Fproducts%2F%5Bid%5D%2Fpage&appPaths=%2Fproducts%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fproducts%2F%5Bid%5D%2Fpage.tsx&appDir=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();