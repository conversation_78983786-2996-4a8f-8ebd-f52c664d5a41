"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/openid-client";
exports.ids = ["vendor-chunks/openid-client"];
exports.modules = {

/***/ "(ssr)/./node_modules/openid-client/lib/client.js":
/*!**************************************************!*\
  !*** ./node_modules/openid-client/lib/client.js ***!
  \**************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst { inspect } = __webpack_require__(/*! util */ \"util\");\nconst stdhttp = __webpack_require__(/*! http */ \"http\");\nconst crypto = __webpack_require__(/*! crypto */ \"crypto\");\nconst { strict: assert } = __webpack_require__(/*! assert */ \"assert\");\nconst querystring = __webpack_require__(/*! querystring */ \"querystring\");\nconst url = __webpack_require__(/*! url */ \"url\");\nconst { URL, URLSearchParams } = __webpack_require__(/*! url */ \"url\");\nconst jose = __webpack_require__(/*! jose */ \"(ssr)/./node_modules/jose/dist/node/cjs/index.js\");\nconst tokenHash = __webpack_require__(/*! oidc-token-hash */ \"(ssr)/./node_modules/oidc-token-hash/lib/index.js\");\nconst isKeyObject = __webpack_require__(/*! ./helpers/is_key_object */ \"(ssr)/./node_modules/openid-client/lib/helpers/is_key_object.js\");\nconst decodeJWT = __webpack_require__(/*! ./helpers/decode_jwt */ \"(ssr)/./node_modules/openid-client/lib/helpers/decode_jwt.js\");\nconst base64url = __webpack_require__(/*! ./helpers/base64url */ \"(ssr)/./node_modules/openid-client/lib/helpers/base64url.js\");\nconst defaults = __webpack_require__(/*! ./helpers/defaults */ \"(ssr)/./node_modules/openid-client/lib/helpers/defaults.js\");\nconst parseWwwAuthenticate = __webpack_require__(/*! ./helpers/www_authenticate_parser */ \"(ssr)/./node_modules/openid-client/lib/helpers/www_authenticate_parser.js\");\nconst { assertSigningAlgValuesSupport, assertIssuerConfiguration } = __webpack_require__(/*! ./helpers/assert */ \"(ssr)/./node_modules/openid-client/lib/helpers/assert.js\");\nconst pick = __webpack_require__(/*! ./helpers/pick */ \"(ssr)/./node_modules/openid-client/lib/helpers/pick.js\");\nconst isPlainObject = __webpack_require__(/*! ./helpers/is_plain_object */ \"(ssr)/./node_modules/openid-client/lib/helpers/is_plain_object.js\");\nconst processResponse = __webpack_require__(/*! ./helpers/process_response */ \"(ssr)/./node_modules/openid-client/lib/helpers/process_response.js\");\nconst TokenSet = __webpack_require__(/*! ./token_set */ \"(ssr)/./node_modules/openid-client/lib/token_set.js\");\nconst { OPError, RPError } = __webpack_require__(/*! ./errors */ \"(ssr)/./node_modules/openid-client/lib/errors.js\");\nconst now = __webpack_require__(/*! ./helpers/unix_timestamp */ \"(ssr)/./node_modules/openid-client/lib/helpers/unix_timestamp.js\");\nconst { random } = __webpack_require__(/*! ./helpers/generators */ \"(ssr)/./node_modules/openid-client/lib/helpers/generators.js\");\nconst request = __webpack_require__(/*! ./helpers/request */ \"(ssr)/./node_modules/openid-client/lib/helpers/request.js\");\nconst { CLOCK_TOLERANCE } = __webpack_require__(/*! ./helpers/consts */ \"(ssr)/./node_modules/openid-client/lib/helpers/consts.js\");\nconst { keystores } = __webpack_require__(/*! ./helpers/weak_cache */ \"(ssr)/./node_modules/openid-client/lib/helpers/weak_cache.js\");\nconst KeyStore = __webpack_require__(/*! ./helpers/keystore */ \"(ssr)/./node_modules/openid-client/lib/helpers/keystore.js\");\nconst clone = __webpack_require__(/*! ./helpers/deep_clone */ \"(ssr)/./node_modules/openid-client/lib/helpers/deep_clone.js\");\nconst { authenticatedPost, resolveResponseType, resolveRedirectUri } = __webpack_require__(/*! ./helpers/client */ \"(ssr)/./node_modules/openid-client/lib/helpers/client.js\");\nconst { queryKeyStore } = __webpack_require__(/*! ./helpers/issuer */ \"(ssr)/./node_modules/openid-client/lib/helpers/issuer.js\");\nconst DeviceFlowHandle = __webpack_require__(/*! ./device_flow_handle */ \"(ssr)/./node_modules/openid-client/lib/device_flow_handle.js\");\nconst [major, minor] = process.version.slice(1).split(\".\").map((str)=>parseInt(str, 10));\nconst rsaPssParams = major >= 17 || major === 16 && minor >= 9;\nconst retryAttempt = Symbol();\nconst skipNonceCheck = Symbol();\nconst skipMaxAgeCheck = Symbol();\nfunction pickCb(input) {\n    return pick(input, \"access_token\", \"code\", \"error_description\", \"error_uri\", \"error\", \"expires_in\", \"id_token\", \"iss\", \"response\", \"session_state\", \"state\", \"token_type\");\n}\nfunction authorizationHeaderValue(token, tokenType = \"Bearer\") {\n    return `${tokenType} ${token}`;\n}\nfunction getSearchParams(input) {\n    const parsed = url.parse(input);\n    if (!parsed.search) return {};\n    return querystring.parse(parsed.search.substring(1));\n}\nfunction verifyPresence(payload, jwt, prop) {\n    if (payload[prop] === undefined) {\n        throw new RPError({\n            message: `missing required JWT property ${prop}`,\n            jwt\n        });\n    }\n}\nfunction authorizationParams(params) {\n    const authParams = {\n        client_id: this.client_id,\n        scope: \"openid\",\n        response_type: resolveResponseType.call(this),\n        redirect_uri: resolveRedirectUri.call(this),\n        ...params\n    };\n    Object.entries(authParams).forEach(([key, value])=>{\n        if (value === null || value === undefined) {\n            delete authParams[key];\n        } else if (key === \"claims\" && typeof value === \"object\") {\n            authParams[key] = JSON.stringify(value);\n        } else if (key === \"resource\" && Array.isArray(value)) {\n            authParams[key] = value;\n        } else if (typeof value !== \"string\") {\n            authParams[key] = String(value);\n        }\n    });\n    return authParams;\n}\nfunction getKeystore(jwks) {\n    if (!isPlainObject(jwks) || !Array.isArray(jwks.keys) || jwks.keys.some((k)=>!isPlainObject(k) || !(\"kty\" in k))) {\n        throw new TypeError(\"jwks must be a JSON Web Key Set formatted object\");\n    }\n    return KeyStore.fromJWKS(jwks, {\n        onlyPrivate: true\n    });\n}\n// if an OP doesnt support client_secret_basic but supports client_secret_post, use it instead\n// this is in place to take care of most common pitfalls when first using discovered Issuers without\n// the support for default values defined by Discovery 1.0\nfunction checkBasicSupport(client, properties) {\n    try {\n        const supported = client.issuer.token_endpoint_auth_methods_supported;\n        if (!supported.includes(properties.token_endpoint_auth_method)) {\n            if (supported.includes(\"client_secret_post\")) {\n                properties.token_endpoint_auth_method = \"client_secret_post\";\n            }\n        }\n    } catch (err) {}\n}\nfunction handleCommonMistakes(client, metadata, properties) {\n    if (!metadata.token_endpoint_auth_method) {\n        // if no explicit value was provided\n        checkBasicSupport(client, properties);\n    }\n    // :fp: c'mon people... RTFM\n    if (metadata.redirect_uri) {\n        if (metadata.redirect_uris) {\n            throw new TypeError(\"provide a redirect_uri or redirect_uris, not both\");\n        }\n        properties.redirect_uris = [\n            metadata.redirect_uri\n        ];\n        delete properties.redirect_uri;\n    }\n    if (metadata.response_type) {\n        if (metadata.response_types) {\n            throw new TypeError(\"provide a response_type or response_types, not both\");\n        }\n        properties.response_types = [\n            metadata.response_type\n        ];\n        delete properties.response_type;\n    }\n}\nfunction getDefaultsForEndpoint(endpoint, issuer, properties) {\n    if (!issuer[`${endpoint}_endpoint`]) return;\n    const tokenEndpointAuthMethod = properties.token_endpoint_auth_method;\n    const tokenEndpointAuthSigningAlg = properties.token_endpoint_auth_signing_alg;\n    const eam = `${endpoint}_endpoint_auth_method`;\n    const easa = `${endpoint}_endpoint_auth_signing_alg`;\n    if (properties[eam] === undefined && properties[easa] === undefined) {\n        if (tokenEndpointAuthMethod !== undefined) {\n            properties[eam] = tokenEndpointAuthMethod;\n        }\n        if (tokenEndpointAuthSigningAlg !== undefined) {\n            properties[easa] = tokenEndpointAuthSigningAlg;\n        }\n    }\n}\nclass BaseClient {\n    #metadata;\n    #issuer;\n    #aadIssValidation;\n    #additionalAuthorizedParties;\n    constructor(issuer, aadIssValidation, metadata = {}, jwks, options){\n        this.#metadata = new Map();\n        this.#issuer = issuer;\n        this.#aadIssValidation = aadIssValidation;\n        if (typeof metadata.client_id !== \"string\" || !metadata.client_id) {\n            throw new TypeError(\"client_id is required\");\n        }\n        const properties = {\n            grant_types: [\n                \"authorization_code\"\n            ],\n            id_token_signed_response_alg: \"RS256\",\n            authorization_signed_response_alg: \"RS256\",\n            response_types: [\n                \"code\"\n            ],\n            token_endpoint_auth_method: \"client_secret_basic\",\n            ...this.fapi1() ? {\n                grant_types: [\n                    \"authorization_code\",\n                    \"implicit\"\n                ],\n                id_token_signed_response_alg: \"PS256\",\n                authorization_signed_response_alg: \"PS256\",\n                response_types: [\n                    \"code id_token\"\n                ],\n                tls_client_certificate_bound_access_tokens: true,\n                token_endpoint_auth_method: undefined\n            } : undefined,\n            ...this.fapi2() ? {\n                id_token_signed_response_alg: \"PS256\",\n                authorization_signed_response_alg: \"PS256\",\n                token_endpoint_auth_method: undefined\n            } : undefined,\n            ...metadata\n        };\n        if (this.fapi()) {\n            switch(properties.token_endpoint_auth_method){\n                case \"self_signed_tls_client_auth\":\n                case \"tls_client_auth\":\n                    break;\n                case \"private_key_jwt\":\n                    if (!jwks) {\n                        throw new TypeError(\"jwks is required\");\n                    }\n                    break;\n                case undefined:\n                    throw new TypeError(\"token_endpoint_auth_method is required\");\n                default:\n                    throw new TypeError(\"invalid or unsupported token_endpoint_auth_method\");\n            }\n        }\n        if (this.fapi2()) {\n            if (properties.tls_client_certificate_bound_access_tokens && properties.dpop_bound_access_tokens) {\n                throw new TypeError(\"either tls_client_certificate_bound_access_tokens or dpop_bound_access_tokens must be set to true\");\n            }\n            if (!properties.tls_client_certificate_bound_access_tokens && !properties.dpop_bound_access_tokens) {\n                throw new TypeError(\"either tls_client_certificate_bound_access_tokens or dpop_bound_access_tokens must be set to true\");\n            }\n        }\n        handleCommonMistakes(this, metadata, properties);\n        assertSigningAlgValuesSupport(\"token\", this.issuer, properties);\n        [\n            \"introspection\",\n            \"revocation\"\n        ].forEach((endpoint)=>{\n            getDefaultsForEndpoint(endpoint, this.issuer, properties);\n            assertSigningAlgValuesSupport(endpoint, this.issuer, properties);\n        });\n        Object.entries(properties).forEach(([key, value])=>{\n            this.#metadata.set(key, value);\n            if (!this[key]) {\n                Object.defineProperty(this, key, {\n                    get () {\n                        return this.#metadata.get(key);\n                    },\n                    enumerable: true\n                });\n            }\n        });\n        if (jwks !== undefined) {\n            const keystore = getKeystore.call(this, jwks);\n            keystores.set(this, keystore);\n        }\n        if (options != null && options.additionalAuthorizedParties) {\n            this.#additionalAuthorizedParties = clone(options.additionalAuthorizedParties);\n        }\n        this[CLOCK_TOLERANCE] = 0;\n    }\n    authorizationUrl(params = {}) {\n        if (!isPlainObject(params)) {\n            throw new TypeError(\"params must be a plain object\");\n        }\n        assertIssuerConfiguration(this.issuer, \"authorization_endpoint\");\n        const target = new URL(this.issuer.authorization_endpoint);\n        for (const [name, value] of Object.entries(authorizationParams.call(this, params))){\n            if (Array.isArray(value)) {\n                target.searchParams.delete(name);\n                for (const member of value){\n                    target.searchParams.append(name, member);\n                }\n            } else {\n                target.searchParams.set(name, value);\n            }\n        }\n        // TODO: is the replace needed?\n        return target.href.replace(/\\+/g, \"%20\");\n    }\n    authorizationPost(params = {}) {\n        if (!isPlainObject(params)) {\n            throw new TypeError(\"params must be a plain object\");\n        }\n        const inputs = authorizationParams.call(this, params);\n        const formInputs = Object.keys(inputs).map((name)=>`<input type=\"hidden\" name=\"${name}\" value=\"${inputs[name]}\"/>`).join(\"\\n\");\n        return `<!DOCTYPE html>\n<head>\n<title>Requesting Authorization</title>\n</head>\n<body onload=\"javascript:document.forms[0].submit()\">\n<form method=\"post\" action=\"${this.issuer.authorization_endpoint}\">\n  ${formInputs}\n</form>\n</body>\n</html>`;\n    }\n    endSessionUrl(params = {}) {\n        assertIssuerConfiguration(this.issuer, \"end_session_endpoint\");\n        const { 0: postLogout, length } = this.post_logout_redirect_uris || [];\n        const { post_logout_redirect_uri = length === 1 ? postLogout : undefined } = params;\n        let id_token_hint;\n        ({ id_token_hint, ...params } = params);\n        if (id_token_hint instanceof TokenSet) {\n            if (!id_token_hint.id_token) {\n                throw new TypeError(\"id_token not present in TokenSet\");\n            }\n            id_token_hint = id_token_hint.id_token;\n        }\n        const target = url.parse(this.issuer.end_session_endpoint);\n        const query = defaults(getSearchParams(this.issuer.end_session_endpoint), params, {\n            post_logout_redirect_uri,\n            client_id: this.client_id\n        }, {\n            id_token_hint\n        });\n        Object.entries(query).forEach(([key, value])=>{\n            if (value === null || value === undefined) {\n                delete query[key];\n            }\n        });\n        target.search = null;\n        target.query = query;\n        return url.format(target);\n    }\n    callbackParams(input) {\n        const isIncomingMessage = input instanceof stdhttp.IncomingMessage || input && input.method && input.url;\n        const isString = typeof input === \"string\";\n        if (!isString && !isIncomingMessage) {\n            throw new TypeError(\"#callbackParams only accepts string urls, http.IncomingMessage or a lookalike\");\n        }\n        if (isIncomingMessage) {\n            switch(input.method){\n                case \"GET\":\n                    return pickCb(getSearchParams(input.url));\n                case \"POST\":\n                    if (input.body === undefined) {\n                        throw new TypeError(\"incoming message body missing, include a body parser prior to this method call\");\n                    }\n                    switch(typeof input.body){\n                        case \"object\":\n                        case \"string\":\n                            if (Buffer.isBuffer(input.body)) {\n                                return pickCb(querystring.parse(input.body.toString(\"utf-8\")));\n                            }\n                            if (typeof input.body === \"string\") {\n                                return pickCb(querystring.parse(input.body));\n                            }\n                            return pickCb(input.body);\n                        default:\n                            throw new TypeError(\"invalid IncomingMessage body object\");\n                    }\n                default:\n                    throw new TypeError(\"invalid IncomingMessage method\");\n            }\n        } else {\n            return pickCb(getSearchParams(input));\n        }\n    }\n    async callback(redirectUri, parameters, checks = {}, { exchangeBody, clientAssertionPayload, DPoP } = {}) {\n        let params = pickCb(parameters);\n        if (checks.jarm && !(\"response\" in parameters)) {\n            throw new RPError({\n                message: \"expected a JARM response\",\n                checks,\n                params\n            });\n        } else if (\"response\" in parameters) {\n            const decrypted = await this.decryptJARM(params.response);\n            params = await this.validateJARM(decrypted);\n        }\n        if (this.default_max_age && !checks.max_age) {\n            checks.max_age = this.default_max_age;\n        }\n        if (params.state && !checks.state) {\n            throw new TypeError(\"checks.state argument is missing\");\n        }\n        if (!params.state && checks.state) {\n            throw new RPError({\n                message: \"state missing from the response\",\n                checks,\n                params\n            });\n        }\n        if (checks.state !== params.state) {\n            throw new RPError({\n                printf: [\n                    \"state mismatch, expected %s, got: %s\",\n                    checks.state,\n                    params.state\n                ],\n                checks,\n                params\n            });\n        }\n        if (\"iss\" in params) {\n            assertIssuerConfiguration(this.issuer, \"issuer\");\n            if (params.iss !== this.issuer.issuer) {\n                throw new RPError({\n                    printf: [\n                        \"iss mismatch, expected %s, got: %s\",\n                        this.issuer.issuer,\n                        params.iss\n                    ],\n                    params\n                });\n            }\n        } else if (this.issuer.authorization_response_iss_parameter_supported && !(\"id_token\" in params) && !(\"response\" in parameters)) {\n            throw new RPError({\n                message: \"iss missing from the response\",\n                params\n            });\n        }\n        if (params.error) {\n            throw new OPError(params);\n        }\n        const RESPONSE_TYPE_REQUIRED_PARAMS = {\n            code: [\n                \"code\"\n            ],\n            id_token: [\n                \"id_token\"\n            ],\n            token: [\n                \"access_token\",\n                \"token_type\"\n            ]\n        };\n        if (checks.response_type) {\n            for (const type of checks.response_type.split(\" \")){\n                if (type === \"none\") {\n                    if (params.code || params.id_token || params.access_token) {\n                        throw new RPError({\n                            message: 'unexpected params encountered for \"none\" response',\n                            checks,\n                            params\n                        });\n                    }\n                } else {\n                    for (const param of RESPONSE_TYPE_REQUIRED_PARAMS[type]){\n                        if (!params[param]) {\n                            throw new RPError({\n                                message: `${param} missing from response`,\n                                checks,\n                                params\n                            });\n                        }\n                    }\n                }\n            }\n        }\n        if (params.id_token) {\n            const tokenset = new TokenSet(params);\n            await this.decryptIdToken(tokenset);\n            await this.validateIdToken(tokenset, checks.nonce, \"authorization\", checks.max_age, checks.state);\n            if (!params.code) {\n                return tokenset;\n            }\n        }\n        if (params.code) {\n            const tokenset = await this.grant({\n                ...exchangeBody,\n                grant_type: \"authorization_code\",\n                code: params.code,\n                redirect_uri: redirectUri,\n                code_verifier: checks.code_verifier\n            }, {\n                clientAssertionPayload,\n                DPoP\n            });\n            await this.decryptIdToken(tokenset);\n            await this.validateIdToken(tokenset, checks.nonce, \"token\", checks.max_age);\n            if (params.session_state) {\n                tokenset.session_state = params.session_state;\n            }\n            return tokenset;\n        }\n        return new TokenSet(params);\n    }\n    async oauthCallback(redirectUri, parameters, checks = {}, { exchangeBody, clientAssertionPayload, DPoP } = {}) {\n        let params = pickCb(parameters);\n        if (checks.jarm && !(\"response\" in parameters)) {\n            throw new RPError({\n                message: \"expected a JARM response\",\n                checks,\n                params\n            });\n        } else if (\"response\" in parameters) {\n            const decrypted = await this.decryptJARM(params.response);\n            params = await this.validateJARM(decrypted);\n        }\n        if (params.state && !checks.state) {\n            throw new TypeError(\"checks.state argument is missing\");\n        }\n        if (!params.state && checks.state) {\n            throw new RPError({\n                message: \"state missing from the response\",\n                checks,\n                params\n            });\n        }\n        if (checks.state !== params.state) {\n            throw new RPError({\n                printf: [\n                    \"state mismatch, expected %s, got: %s\",\n                    checks.state,\n                    params.state\n                ],\n                checks,\n                params\n            });\n        }\n        if (\"iss\" in params) {\n            assertIssuerConfiguration(this.issuer, \"issuer\");\n            if (params.iss !== this.issuer.issuer) {\n                throw new RPError({\n                    printf: [\n                        \"iss mismatch, expected %s, got: %s\",\n                        this.issuer.issuer,\n                        params.iss\n                    ],\n                    params\n                });\n            }\n        } else if (this.issuer.authorization_response_iss_parameter_supported && !(\"id_token\" in params) && !(\"response\" in parameters)) {\n            throw new RPError({\n                message: \"iss missing from the response\",\n                params\n            });\n        }\n        if (params.error) {\n            throw new OPError(params);\n        }\n        if (typeof params.id_token === \"string\" && params.id_token.length) {\n            throw new RPError({\n                message: \"id_token detected in the response, you must use client.callback() instead of client.oauthCallback()\",\n                params\n            });\n        }\n        delete params.id_token;\n        const RESPONSE_TYPE_REQUIRED_PARAMS = {\n            code: [\n                \"code\"\n            ],\n            token: [\n                \"access_token\",\n                \"token_type\"\n            ]\n        };\n        if (checks.response_type) {\n            for (const type of checks.response_type.split(\" \")){\n                if (type === \"none\") {\n                    if (params.code || params.id_token || params.access_token) {\n                        throw new RPError({\n                            message: 'unexpected params encountered for \"none\" response',\n                            checks,\n                            params\n                        });\n                    }\n                }\n                if (RESPONSE_TYPE_REQUIRED_PARAMS[type]) {\n                    for (const param of RESPONSE_TYPE_REQUIRED_PARAMS[type]){\n                        if (!params[param]) {\n                            throw new RPError({\n                                message: `${param} missing from response`,\n                                checks,\n                                params\n                            });\n                        }\n                    }\n                }\n            }\n        }\n        if (params.code) {\n            const tokenset = await this.grant({\n                ...exchangeBody,\n                grant_type: \"authorization_code\",\n                code: params.code,\n                redirect_uri: redirectUri,\n                code_verifier: checks.code_verifier\n            }, {\n                clientAssertionPayload,\n                DPoP\n            });\n            if (typeof tokenset.id_token === \"string\" && tokenset.id_token.length) {\n                throw new RPError({\n                    message: \"id_token detected in the response, you must use client.callback() instead of client.oauthCallback()\",\n                    params\n                });\n            }\n            delete tokenset.id_token;\n            return tokenset;\n        }\n        return new TokenSet(params);\n    }\n    async decryptIdToken(token) {\n        if (!this.id_token_encrypted_response_alg) {\n            return token;\n        }\n        let idToken = token;\n        if (idToken instanceof TokenSet) {\n            if (!idToken.id_token) {\n                throw new TypeError(\"id_token not present in TokenSet\");\n            }\n            idToken = idToken.id_token;\n        }\n        const expectedAlg = this.id_token_encrypted_response_alg;\n        const expectedEnc = this.id_token_encrypted_response_enc;\n        const result = await this.decryptJWE(idToken, expectedAlg, expectedEnc);\n        if (token instanceof TokenSet) {\n            token.id_token = result;\n            return token;\n        }\n        return result;\n    }\n    async validateJWTUserinfo(body) {\n        const expectedAlg = this.userinfo_signed_response_alg;\n        return this.validateJWT(body, expectedAlg, []);\n    }\n    async decryptJARM(response) {\n        if (!this.authorization_encrypted_response_alg) {\n            return response;\n        }\n        const expectedAlg = this.authorization_encrypted_response_alg;\n        const expectedEnc = this.authorization_encrypted_response_enc;\n        return this.decryptJWE(response, expectedAlg, expectedEnc);\n    }\n    async decryptJWTUserinfo(body) {\n        if (!this.userinfo_encrypted_response_alg) {\n            return body;\n        }\n        const expectedAlg = this.userinfo_encrypted_response_alg;\n        const expectedEnc = this.userinfo_encrypted_response_enc;\n        return this.decryptJWE(body, expectedAlg, expectedEnc);\n    }\n    async decryptJWE(jwe, expectedAlg, expectedEnc = \"A128CBC-HS256\") {\n        const header = JSON.parse(base64url.decode(jwe.split(\".\")[0]));\n        if (header.alg !== expectedAlg) {\n            throw new RPError({\n                printf: [\n                    \"unexpected JWE alg received, expected %s, got: %s\",\n                    expectedAlg,\n                    header.alg\n                ],\n                jwt: jwe\n            });\n        }\n        if (header.enc !== expectedEnc) {\n            throw new RPError({\n                printf: [\n                    \"unexpected JWE enc received, expected %s, got: %s\",\n                    expectedEnc,\n                    header.enc\n                ],\n                jwt: jwe\n            });\n        }\n        const getPlaintext = (result)=>new TextDecoder().decode(result.plaintext);\n        let plaintext;\n        if (expectedAlg.match(/^(?:RSA|ECDH)/)) {\n            const keystore = await keystores.get(this);\n            const protectedHeader = jose.decodeProtectedHeader(jwe);\n            for (const key of keystore.all({\n                ...protectedHeader,\n                use: \"enc\"\n            })){\n                plaintext = await jose.compactDecrypt(jwe, await key.keyObject(protectedHeader.alg)).then(getPlaintext, ()=>{});\n                if (plaintext) break;\n            }\n        } else {\n            plaintext = await jose.compactDecrypt(jwe, this.secretForAlg(expectedAlg === \"dir\" ? expectedEnc : expectedAlg)).then(getPlaintext, ()=>{});\n        }\n        if (!plaintext) {\n            throw new RPError({\n                message: \"failed to decrypt JWE\",\n                jwt: jwe\n            });\n        }\n        return plaintext;\n    }\n    async validateIdToken(tokenSet, nonce, returnedBy, maxAge, state) {\n        let idToken = tokenSet;\n        const expectedAlg = this.id_token_signed_response_alg;\n        const isTokenSet = idToken instanceof TokenSet;\n        if (isTokenSet) {\n            if (!idToken.id_token) {\n                throw new TypeError(\"id_token not present in TokenSet\");\n            }\n            idToken = idToken.id_token;\n        }\n        idToken = String(idToken);\n        const timestamp = now();\n        const { protected: header, payload, key } = await this.validateJWT(idToken, expectedAlg);\n        if (typeof maxAge === \"number\" || maxAge !== skipMaxAgeCheck && this.require_auth_time) {\n            if (!payload.auth_time) {\n                throw new RPError({\n                    message: \"missing required JWT property auth_time\",\n                    jwt: idToken\n                });\n            }\n            if (typeof payload.auth_time !== \"number\") {\n                throw new RPError({\n                    message: \"JWT auth_time claim must be a JSON numeric value\",\n                    jwt: idToken\n                });\n            }\n        }\n        if (typeof maxAge === \"number\" && payload.auth_time + maxAge < timestamp - this[CLOCK_TOLERANCE]) {\n            throw new RPError({\n                printf: [\n                    \"too much time has elapsed since the last End-User authentication, max_age %i, auth_time: %i, now %i\",\n                    maxAge,\n                    payload.auth_time,\n                    timestamp - this[CLOCK_TOLERANCE]\n                ],\n                now: timestamp,\n                tolerance: this[CLOCK_TOLERANCE],\n                auth_time: payload.auth_time,\n                jwt: idToken\n            });\n        }\n        if (nonce !== skipNonceCheck && (payload.nonce || nonce !== undefined) && payload.nonce !== nonce) {\n            throw new RPError({\n                printf: [\n                    \"nonce mismatch, expected %s, got: %s\",\n                    nonce,\n                    payload.nonce\n                ],\n                jwt: idToken\n            });\n        }\n        if (returnedBy === \"authorization\") {\n            if (!payload.at_hash && tokenSet.access_token) {\n                throw new RPError({\n                    message: \"missing required property at_hash\",\n                    jwt: idToken\n                });\n            }\n            if (!payload.c_hash && tokenSet.code) {\n                throw new RPError({\n                    message: \"missing required property c_hash\",\n                    jwt: idToken\n                });\n            }\n            if (this.fapi1()) {\n                if (!payload.s_hash && (tokenSet.state || state)) {\n                    throw new RPError({\n                        message: \"missing required property s_hash\",\n                        jwt: idToken\n                    });\n                }\n            }\n            if (payload.s_hash) {\n                if (!state) {\n                    throw new TypeError('cannot verify s_hash, \"checks.state\" property not provided');\n                }\n                try {\n                    tokenHash.validate({\n                        claim: \"s_hash\",\n                        source: \"state\"\n                    }, payload.s_hash, state, header.alg, key.jwk && key.jwk.crv);\n                } catch (err) {\n                    throw new RPError({\n                        message: err.message,\n                        jwt: idToken\n                    });\n                }\n            }\n        }\n        if (this.fapi() && payload.iat < timestamp - 3600) {\n            throw new RPError({\n                printf: [\n                    \"JWT issued too far in the past, now %i, iat %i\",\n                    timestamp,\n                    payload.iat\n                ],\n                now: timestamp,\n                tolerance: this[CLOCK_TOLERANCE],\n                iat: payload.iat,\n                jwt: idToken\n            });\n        }\n        if (tokenSet.access_token && payload.at_hash !== undefined) {\n            try {\n                tokenHash.validate({\n                    claim: \"at_hash\",\n                    source: \"access_token\"\n                }, payload.at_hash, tokenSet.access_token, header.alg, key.jwk && key.jwk.crv);\n            } catch (err) {\n                throw new RPError({\n                    message: err.message,\n                    jwt: idToken\n                });\n            }\n        }\n        if (tokenSet.code && payload.c_hash !== undefined) {\n            try {\n                tokenHash.validate({\n                    claim: \"c_hash\",\n                    source: \"code\"\n                }, payload.c_hash, tokenSet.code, header.alg, key.jwk && key.jwk.crv);\n            } catch (err) {\n                throw new RPError({\n                    message: err.message,\n                    jwt: idToken\n                });\n            }\n        }\n        return tokenSet;\n    }\n    async validateJWT(jwt, expectedAlg, required = [\n        \"iss\",\n        \"sub\",\n        \"aud\",\n        \"exp\",\n        \"iat\"\n    ]) {\n        const isSelfIssued = this.issuer.issuer === \"https://self-issued.me\";\n        const timestamp = now();\n        let header;\n        let payload;\n        try {\n            ({ header, payload } = decodeJWT(jwt, {\n                complete: true\n            }));\n        } catch (err) {\n            throw new RPError({\n                printf: [\n                    \"failed to decode JWT (%s: %s)\",\n                    err.name,\n                    err.message\n                ],\n                jwt\n            });\n        }\n        if (header.alg !== expectedAlg) {\n            throw new RPError({\n                printf: [\n                    \"unexpected JWT alg received, expected %s, got: %s\",\n                    expectedAlg,\n                    header.alg\n                ],\n                jwt\n            });\n        }\n        if (isSelfIssued) {\n            required = [\n                ...required,\n                \"sub_jwk\"\n            ];\n        }\n        required.forEach(verifyPresence.bind(undefined, payload, jwt));\n        if (payload.iss !== undefined) {\n            let expectedIss = this.issuer.issuer;\n            if (this.#aadIssValidation) {\n                expectedIss = this.issuer.issuer.replace(\"{tenantid}\", payload.tid);\n            }\n            if (payload.iss !== expectedIss) {\n                throw new RPError({\n                    printf: [\n                        \"unexpected iss value, expected %s, got: %s\",\n                        expectedIss,\n                        payload.iss\n                    ],\n                    jwt\n                });\n            }\n        }\n        if (payload.iat !== undefined) {\n            if (typeof payload.iat !== \"number\") {\n                throw new RPError({\n                    message: \"JWT iat claim must be a JSON numeric value\",\n                    jwt\n                });\n            }\n        }\n        if (payload.nbf !== undefined) {\n            if (typeof payload.nbf !== \"number\") {\n                throw new RPError({\n                    message: \"JWT nbf claim must be a JSON numeric value\",\n                    jwt\n                });\n            }\n            if (payload.nbf > timestamp + this[CLOCK_TOLERANCE]) {\n                throw new RPError({\n                    printf: [\n                        \"JWT not active yet, now %i, nbf %i\",\n                        timestamp + this[CLOCK_TOLERANCE],\n                        payload.nbf\n                    ],\n                    now: timestamp,\n                    tolerance: this[CLOCK_TOLERANCE],\n                    nbf: payload.nbf,\n                    jwt\n                });\n            }\n        }\n        if (payload.exp !== undefined) {\n            if (typeof payload.exp !== \"number\") {\n                throw new RPError({\n                    message: \"JWT exp claim must be a JSON numeric value\",\n                    jwt\n                });\n            }\n            if (timestamp - this[CLOCK_TOLERANCE] >= payload.exp) {\n                throw new RPError({\n                    printf: [\n                        \"JWT expired, now %i, exp %i\",\n                        timestamp - this[CLOCK_TOLERANCE],\n                        payload.exp\n                    ],\n                    now: timestamp,\n                    tolerance: this[CLOCK_TOLERANCE],\n                    exp: payload.exp,\n                    jwt\n                });\n            }\n        }\n        if (payload.aud !== undefined) {\n            if (Array.isArray(payload.aud)) {\n                if (payload.aud.length > 1 && !payload.azp) {\n                    throw new RPError({\n                        message: \"missing required JWT property azp\",\n                        jwt\n                    });\n                }\n                if (!payload.aud.includes(this.client_id)) {\n                    throw new RPError({\n                        printf: [\n                            \"aud is missing the client_id, expected %s to be included in %j\",\n                            this.client_id,\n                            payload.aud\n                        ],\n                        jwt\n                    });\n                }\n            } else if (payload.aud !== this.client_id) {\n                throw new RPError({\n                    printf: [\n                        \"aud mismatch, expected %s, got: %s\",\n                        this.client_id,\n                        payload.aud\n                    ],\n                    jwt\n                });\n            }\n        }\n        if (payload.azp !== undefined) {\n            let additionalAuthorizedParties = this.#additionalAuthorizedParties;\n            if (typeof additionalAuthorizedParties === \"string\") {\n                additionalAuthorizedParties = [\n                    this.client_id,\n                    additionalAuthorizedParties\n                ];\n            } else if (Array.isArray(additionalAuthorizedParties)) {\n                additionalAuthorizedParties = [\n                    this.client_id,\n                    ...additionalAuthorizedParties\n                ];\n            } else {\n                additionalAuthorizedParties = [\n                    this.client_id\n                ];\n            }\n            if (!additionalAuthorizedParties.includes(payload.azp)) {\n                throw new RPError({\n                    printf: [\n                        \"azp mismatch, got: %s\",\n                        payload.azp\n                    ],\n                    jwt\n                });\n            }\n        }\n        let keys;\n        if (isSelfIssued) {\n            try {\n                assert(isPlainObject(payload.sub_jwk));\n                const key = await jose.importJWK(payload.sub_jwk, header.alg);\n                assert.equal(key.type, \"public\");\n                keys = [\n                    {\n                        keyObject () {\n                            return key;\n                        }\n                    }\n                ];\n            } catch (err) {\n                throw new RPError({\n                    message: \"failed to use sub_jwk claim as an asymmetric JSON Web Key\",\n                    jwt\n                });\n            }\n            if (await jose.calculateJwkThumbprint(payload.sub_jwk) !== payload.sub) {\n                throw new RPError({\n                    message: \"failed to match the subject with sub_jwk\",\n                    jwt\n                });\n            }\n        } else if (header.alg.startsWith(\"HS\")) {\n            keys = [\n                this.secretForAlg(header.alg)\n            ];\n        } else if (header.alg !== \"none\") {\n            keys = await queryKeyStore.call(this.issuer, {\n                ...header,\n                use: \"sig\"\n            });\n        }\n        if (!keys && header.alg === \"none\") {\n            return {\n                protected: header,\n                payload\n            };\n        }\n        for (const key of keys){\n            const verified = await jose.compactVerify(jwt, key instanceof Uint8Array ? key : await key.keyObject(header.alg)).catch(()=>{});\n            if (verified) {\n                return {\n                    payload,\n                    protected: verified.protectedHeader,\n                    key\n                };\n            }\n        }\n        throw new RPError({\n            message: \"failed to validate JWT signature\",\n            jwt\n        });\n    }\n    async refresh(refreshToken, { exchangeBody, clientAssertionPayload, DPoP } = {}) {\n        let token = refreshToken;\n        if (token instanceof TokenSet) {\n            if (!token.refresh_token) {\n                throw new TypeError(\"refresh_token not present in TokenSet\");\n            }\n            token = token.refresh_token;\n        }\n        const tokenset = await this.grant({\n            ...exchangeBody,\n            grant_type: \"refresh_token\",\n            refresh_token: String(token)\n        }, {\n            clientAssertionPayload,\n            DPoP\n        });\n        if (tokenset.id_token) {\n            await this.decryptIdToken(tokenset);\n            await this.validateIdToken(tokenset, skipNonceCheck, \"token\", skipMaxAgeCheck);\n            if (refreshToken instanceof TokenSet && refreshToken.id_token) {\n                const expectedSub = refreshToken.claims().sub;\n                const actualSub = tokenset.claims().sub;\n                if (actualSub !== expectedSub) {\n                    throw new RPError({\n                        printf: [\n                            \"sub mismatch, expected %s, got: %s\",\n                            expectedSub,\n                            actualSub\n                        ],\n                        jwt: tokenset.id_token\n                    });\n                }\n            }\n        }\n        return tokenset;\n    }\n    async requestResource(resourceUrl, accessToken, { method, headers, body, DPoP, tokenType = DPoP ? \"DPoP\" : accessToken instanceof TokenSet ? accessToken.token_type : \"Bearer\" } = {}, retry) {\n        if (accessToken instanceof TokenSet) {\n            if (!accessToken.access_token) {\n                throw new TypeError(\"access_token not present in TokenSet\");\n            }\n            accessToken = accessToken.access_token;\n        }\n        if (!accessToken) {\n            throw new TypeError(\"no access token provided\");\n        } else if (typeof accessToken !== \"string\") {\n            throw new TypeError(\"invalid access token provided\");\n        }\n        const requestOpts = {\n            headers: {\n                Authorization: authorizationHeaderValue(accessToken, tokenType),\n                ...headers\n            },\n            body\n        };\n        const mTLS = !!this.tls_client_certificate_bound_access_tokens;\n        const response = await request.call(this, {\n            ...requestOpts,\n            responseType: \"buffer\",\n            method,\n            url: resourceUrl\n        }, {\n            accessToken,\n            mTLS,\n            DPoP\n        });\n        const wwwAuthenticate = response.headers[\"www-authenticate\"];\n        if (retry !== retryAttempt && wwwAuthenticate && wwwAuthenticate.toLowerCase().startsWith(\"dpop \") && parseWwwAuthenticate(wwwAuthenticate).error === \"use_dpop_nonce\") {\n            return this.requestResource(resourceUrl, accessToken, {\n                method,\n                headers,\n                body,\n                DPoP,\n                tokenType\n            });\n        }\n        return response;\n    }\n    async userinfo(accessToken, { method = \"GET\", via = \"header\", tokenType, params, DPoP } = {}) {\n        assertIssuerConfiguration(this.issuer, \"userinfo_endpoint\");\n        const options = {\n            tokenType,\n            method: String(method).toUpperCase(),\n            DPoP\n        };\n        if (options.method !== \"GET\" && options.method !== \"POST\") {\n            throw new TypeError(\"#userinfo() method can only be POST or a GET\");\n        }\n        if (via === \"body\" && options.method !== \"POST\") {\n            throw new TypeError(\"can only send body on POST\");\n        }\n        const jwt = !!(this.userinfo_signed_response_alg || this.userinfo_encrypted_response_alg);\n        if (jwt) {\n            options.headers = {\n                Accept: \"application/jwt\"\n            };\n        } else {\n            options.headers = {\n                Accept: \"application/json\"\n            };\n        }\n        const mTLS = !!this.tls_client_certificate_bound_access_tokens;\n        let targetUrl;\n        if (mTLS && this.issuer.mtls_endpoint_aliases) {\n            targetUrl = this.issuer.mtls_endpoint_aliases.userinfo_endpoint;\n        }\n        targetUrl = new URL(targetUrl || this.issuer.userinfo_endpoint);\n        if (via === \"body\") {\n            options.headers.Authorization = undefined;\n            options.headers[\"Content-Type\"] = \"application/x-www-form-urlencoded\";\n            options.body = new URLSearchParams();\n            options.body.append(\"access_token\", accessToken instanceof TokenSet ? accessToken.access_token : accessToken);\n        }\n        // handle additional parameters, GET via querystring, POST via urlencoded body\n        if (params) {\n            if (options.method === \"GET\") {\n                Object.entries(params).forEach(([key, value])=>{\n                    targetUrl.searchParams.append(key, value);\n                });\n            } else if (options.body) {\n                // POST && via body\n                Object.entries(params).forEach(([key, value])=>{\n                    options.body.append(key, value);\n                });\n            } else {\n                // POST && via header\n                options.body = new URLSearchParams();\n                options.headers[\"Content-Type\"] = \"application/x-www-form-urlencoded\";\n                Object.entries(params).forEach(([key, value])=>{\n                    options.body.append(key, value);\n                });\n            }\n        }\n        if (options.body) {\n            options.body = options.body.toString();\n        }\n        const response = await this.requestResource(targetUrl, accessToken, options);\n        let parsed = processResponse(response, {\n            bearer: true\n        });\n        if (jwt) {\n            if (!/^application\\/jwt/.test(response.headers[\"content-type\"])) {\n                throw new RPError({\n                    message: \"expected application/jwt response from the userinfo_endpoint\",\n                    response\n                });\n            }\n            const body = response.body.toString();\n            const userinfo = await this.decryptJWTUserinfo(body);\n            if (!this.userinfo_signed_response_alg) {\n                try {\n                    parsed = JSON.parse(userinfo);\n                    assert(isPlainObject(parsed));\n                } catch (err) {\n                    throw new RPError({\n                        message: \"failed to parse userinfo JWE payload as JSON\",\n                        jwt: userinfo\n                    });\n                }\n            } else {\n                ({ payload: parsed } = await this.validateJWTUserinfo(userinfo));\n            }\n        } else {\n            try {\n                parsed = JSON.parse(response.body);\n            } catch (err) {\n                Object.defineProperty(err, \"response\", {\n                    value: response\n                });\n                throw err;\n            }\n        }\n        if (accessToken instanceof TokenSet && accessToken.id_token) {\n            const expectedSub = accessToken.claims().sub;\n            if (parsed.sub !== expectedSub) {\n                throw new RPError({\n                    printf: [\n                        \"userinfo sub mismatch, expected %s, got: %s\",\n                        expectedSub,\n                        parsed.sub\n                    ],\n                    body: parsed,\n                    jwt: accessToken.id_token\n                });\n            }\n        }\n        return parsed;\n    }\n    encryptionSecret(len) {\n        const hash = len <= 256 ? \"sha256\" : len <= 384 ? \"sha384\" : len <= 512 ? \"sha512\" : false;\n        if (!hash) {\n            throw new Error(\"unsupported symmetric encryption key derivation\");\n        }\n        return crypto.createHash(hash).update(this.client_secret).digest().slice(0, len / 8);\n    }\n    secretForAlg(alg) {\n        if (!this.client_secret) {\n            throw new TypeError(\"client_secret is required\");\n        }\n        if (/^A(\\d{3})(?:GCM)?KW$/.test(alg)) {\n            return this.encryptionSecret(parseInt(RegExp.$1, 10));\n        }\n        if (/^A(\\d{3})(?:GCM|CBC-HS(\\d{3}))$/.test(alg)) {\n            return this.encryptionSecret(parseInt(RegExp.$2 || RegExp.$1, 10));\n        }\n        return new TextEncoder().encode(this.client_secret);\n    }\n    async grant(body, { clientAssertionPayload, DPoP } = {}, retry) {\n        assertIssuerConfiguration(this.issuer, \"token_endpoint\");\n        const response = await authenticatedPost.call(this, \"token\", {\n            form: body,\n            responseType: \"json\"\n        }, {\n            clientAssertionPayload,\n            DPoP\n        });\n        let responseBody;\n        try {\n            responseBody = processResponse(response);\n        } catch (err) {\n            if (retry !== retryAttempt && err instanceof OPError && err.error === \"use_dpop_nonce\") {\n                return this.grant(body, {\n                    clientAssertionPayload,\n                    DPoP\n                }, retryAttempt);\n            }\n            throw err;\n        }\n        return new TokenSet(responseBody);\n    }\n    async deviceAuthorization(params = {}, { exchangeBody, clientAssertionPayload, DPoP } = {}) {\n        assertIssuerConfiguration(this.issuer, \"device_authorization_endpoint\");\n        assertIssuerConfiguration(this.issuer, \"token_endpoint\");\n        const body = authorizationParams.call(this, {\n            client_id: this.client_id,\n            redirect_uri: null,\n            response_type: null,\n            ...params\n        });\n        const response = await authenticatedPost.call(this, \"device_authorization\", {\n            responseType: \"json\",\n            form: body\n        }, {\n            clientAssertionPayload,\n            endpointAuthMethod: \"token\"\n        });\n        const responseBody = processResponse(response);\n        return new DeviceFlowHandle({\n            client: this,\n            exchangeBody,\n            clientAssertionPayload,\n            response: responseBody,\n            maxAge: params.max_age,\n            DPoP\n        });\n    }\n    async revoke(token, hint, { revokeBody, clientAssertionPayload } = {}) {\n        assertIssuerConfiguration(this.issuer, \"revocation_endpoint\");\n        if (hint !== undefined && typeof hint !== \"string\") {\n            throw new TypeError(\"hint must be a string\");\n        }\n        const form = {\n            ...revokeBody,\n            token\n        };\n        if (hint) {\n            form.token_type_hint = hint;\n        }\n        const response = await authenticatedPost.call(this, \"revocation\", {\n            form\n        }, {\n            clientAssertionPayload\n        });\n        processResponse(response, {\n            body: false\n        });\n    }\n    async introspect(token, hint, { introspectBody, clientAssertionPayload } = {}) {\n        assertIssuerConfiguration(this.issuer, \"introspection_endpoint\");\n        if (hint !== undefined && typeof hint !== \"string\") {\n            throw new TypeError(\"hint must be a string\");\n        }\n        const form = {\n            ...introspectBody,\n            token\n        };\n        if (hint) {\n            form.token_type_hint = hint;\n        }\n        const response = await authenticatedPost.call(this, \"introspection\", {\n            form,\n            responseType: \"json\"\n        }, {\n            clientAssertionPayload\n        });\n        const responseBody = processResponse(response);\n        return responseBody;\n    }\n    static async register(metadata, options = {}) {\n        const { initialAccessToken, jwks, ...clientOptions } = options;\n        assertIssuerConfiguration(this.issuer, \"registration_endpoint\");\n        if (jwks !== undefined && !(metadata.jwks || metadata.jwks_uri)) {\n            const keystore = await getKeystore.call(this, jwks);\n            metadata.jwks = keystore.toJWKS();\n        }\n        const response = await request.call(this, {\n            headers: {\n                Accept: \"application/json\",\n                ...initialAccessToken ? {\n                    Authorization: authorizationHeaderValue(initialAccessToken)\n                } : undefined\n            },\n            responseType: \"json\",\n            json: metadata,\n            url: this.issuer.registration_endpoint,\n            method: \"POST\"\n        });\n        const responseBody = processResponse(response, {\n            statusCode: 201,\n            bearer: true\n        });\n        return new this(responseBody, jwks, clientOptions);\n    }\n    get metadata() {\n        return clone(Object.fromEntries(this.#metadata.entries()));\n    }\n    static async fromUri(registrationClientUri, registrationAccessToken, jwks, clientOptions) {\n        const response = await request.call(this, {\n            method: \"GET\",\n            url: registrationClientUri,\n            responseType: \"json\",\n            headers: {\n                Authorization: authorizationHeaderValue(registrationAccessToken),\n                Accept: \"application/json\"\n            }\n        });\n        const responseBody = processResponse(response, {\n            bearer: true\n        });\n        return new this(responseBody, jwks, clientOptions);\n    }\n    async requestObject(requestObject = {}, { sign: signingAlgorithm = this.request_object_signing_alg || \"none\", encrypt: { alg: eKeyManagement = this.request_object_encryption_alg, enc: eContentEncryption = this.request_object_encryption_enc || \"A128CBC-HS256\" } = {} } = {}) {\n        if (!isPlainObject(requestObject)) {\n            throw new TypeError(\"requestObject must be a plain object\");\n        }\n        let signed;\n        let key;\n        const unix = now();\n        const header = {\n            alg: signingAlgorithm,\n            typ: \"oauth-authz-req+jwt\"\n        };\n        const payload = JSON.stringify(defaults({}, requestObject, {\n            iss: this.client_id,\n            aud: this.issuer.issuer,\n            client_id: this.client_id,\n            jti: random(),\n            iat: unix,\n            exp: unix + 300,\n            ...this.fapi() ? {\n                nbf: unix\n            } : undefined\n        }));\n        if (signingAlgorithm === \"none\") {\n            signed = [\n                base64url.encode(JSON.stringify(header)),\n                base64url.encode(payload),\n                \"\"\n            ].join(\".\");\n        } else {\n            const symmetric = signingAlgorithm.startsWith(\"HS\");\n            if (symmetric) {\n                key = this.secretForAlg(signingAlgorithm);\n            } else {\n                const keystore = await keystores.get(this);\n                if (!keystore) {\n                    throw new TypeError(`no keystore present for client, cannot sign using alg ${signingAlgorithm}`);\n                }\n                key = keystore.get({\n                    alg: signingAlgorithm,\n                    use: \"sig\"\n                });\n                if (!key) {\n                    throw new TypeError(`no key to sign with found for alg ${signingAlgorithm}`);\n                }\n            }\n            signed = await new jose.CompactSign(new TextEncoder().encode(payload)).setProtectedHeader({\n                ...header,\n                kid: symmetric ? undefined : key.jwk.kid\n            }).sign(symmetric ? key : await key.keyObject(signingAlgorithm));\n        }\n        if (!eKeyManagement) {\n            return signed;\n        }\n        const fields = {\n            alg: eKeyManagement,\n            enc: eContentEncryption,\n            cty: \"oauth-authz-req+jwt\"\n        };\n        if (fields.alg.match(/^(RSA|ECDH)/)) {\n            [key] = await queryKeyStore.call(this.issuer, {\n                alg: fields.alg,\n                use: \"enc\"\n            }, {\n                allowMulti: true\n            });\n        } else {\n            key = this.secretForAlg(fields.alg === \"dir\" ? fields.enc : fields.alg);\n        }\n        return new jose.CompactEncrypt(new TextEncoder().encode(signed)).setProtectedHeader({\n            ...fields,\n            kid: key instanceof Uint8Array ? undefined : key.jwk.kid\n        }).encrypt(key instanceof Uint8Array ? key : await key.keyObject(fields.alg));\n    }\n    async pushedAuthorizationRequest(params = {}, { clientAssertionPayload } = {}) {\n        assertIssuerConfiguration(this.issuer, \"pushed_authorization_request_endpoint\");\n        const body = {\n            ...\"request\" in params ? params : authorizationParams.call(this, params),\n            client_id: this.client_id\n        };\n        const response = await authenticatedPost.call(this, \"pushed_authorization_request\", {\n            responseType: \"json\",\n            form: body\n        }, {\n            clientAssertionPayload,\n            endpointAuthMethod: \"token\"\n        });\n        const responseBody = processResponse(response, {\n            statusCode: 201\n        });\n        if (!(\"expires_in\" in responseBody)) {\n            throw new RPError({\n                message: \"expected expires_in in Pushed Authorization Successful Response\",\n                response\n            });\n        }\n        if (typeof responseBody.expires_in !== \"number\") {\n            throw new RPError({\n                message: \"invalid expires_in value in Pushed Authorization Successful Response\",\n                response\n            });\n        }\n        if (!(\"request_uri\" in responseBody)) {\n            throw new RPError({\n                message: \"expected request_uri in Pushed Authorization Successful Response\",\n                response\n            });\n        }\n        if (typeof responseBody.request_uri !== \"string\") {\n            throw new RPError({\n                message: \"invalid request_uri value in Pushed Authorization Successful Response\",\n                response\n            });\n        }\n        return responseBody;\n    }\n    get issuer() {\n        return this.#issuer;\n    }\n    /* istanbul ignore next */ [inspect.custom]() {\n        return `${this.constructor.name} ${inspect(this.metadata, {\n            depth: Infinity,\n            colors: process.stdout.isTTY,\n            compact: false,\n            sorted: true\n        })}`;\n    }\n    fapi() {\n        return this.fapi1() || this.fapi2();\n    }\n    fapi1() {\n        return this.constructor.name === \"FAPI1Client\";\n    }\n    fapi2() {\n        return this.constructor.name === \"FAPI2Client\";\n    }\n    async validateJARM(response) {\n        const expectedAlg = this.authorization_signed_response_alg;\n        const { payload } = await this.validateJWT(response, expectedAlg, [\n            \"iss\",\n            \"exp\",\n            \"aud\"\n        ]);\n        return pickCb(payload);\n    }\n    /**\n   * @name dpopProof\n   * @api private\n   */ async dpopProof(payload, privateKeyInput, accessToken) {\n        if (!isPlainObject(payload)) {\n            throw new TypeError(\"payload must be a plain object\");\n        }\n        let privateKey;\n        if (isKeyObject(privateKeyInput)) {\n            privateKey = privateKeyInput;\n        } else if (privateKeyInput[Symbol.toStringTag] === \"CryptoKey\") {\n            privateKey = privateKeyInput;\n        } else if (jose.cryptoRuntime === \"node:crypto\") {\n            privateKey = crypto.createPrivateKey(privateKeyInput);\n        } else {\n            throw new TypeError(\"unrecognized crypto runtime\");\n        }\n        if (privateKey.type !== \"private\") {\n            throw new TypeError('\"DPoP\" option must be a private key');\n        }\n        let alg = determineDPoPAlgorithm.call(this, privateKey, privateKeyInput);\n        if (!alg) {\n            throw new TypeError(\"could not determine DPoP JWS Algorithm\");\n        }\n        return new jose.SignJWT({\n            ath: accessToken ? base64url.encode(crypto.createHash(\"sha256\").update(accessToken).digest()) : undefined,\n            ...payload\n        }).setProtectedHeader({\n            alg,\n            typ: \"dpop+jwt\",\n            jwk: await getJwk(privateKey, privateKeyInput)\n        }).setIssuedAt().setJti(random()).sign(privateKey);\n    }\n}\nfunction determineDPoPAlgorithmFromCryptoKey(cryptoKey) {\n    switch(cryptoKey.algorithm.name){\n        case \"Ed25519\":\n        case \"Ed448\":\n            return \"EdDSA\";\n        case \"ECDSA\":\n            {\n                switch(cryptoKey.algorithm.namedCurve){\n                    case \"P-256\":\n                        return \"ES256\";\n                    case \"P-384\":\n                        return \"ES384\";\n                    case \"P-521\":\n                        return \"ES512\";\n                    default:\n                        break;\n                }\n                break;\n            }\n        case \"RSASSA-PKCS1-v1_5\":\n            return `RS${cryptoKey.algorithm.hash.name.slice(4)}`;\n        case \"RSA-PSS\":\n            return `PS${cryptoKey.algorithm.hash.name.slice(4)}`;\n        default:\n            throw new TypeError(\"unsupported DPoP private key\");\n    }\n}\nlet determineDPoPAlgorithm;\nif (jose.cryptoRuntime === \"node:crypto\") {\n    determineDPoPAlgorithm = function(privateKey, privateKeyInput) {\n        if (privateKeyInput[Symbol.toStringTag] === \"CryptoKey\") {\n            return determineDPoPAlgorithmFromCryptoKey(privateKey);\n        }\n        switch(privateKey.asymmetricKeyType){\n            case \"ed25519\":\n            case \"ed448\":\n                return \"EdDSA\";\n            case \"ec\":\n                return determineEcAlgorithm(privateKey, privateKeyInput);\n            case \"rsa\":\n            case rsaPssParams && \"rsa-pss\":\n                return determineRsaAlgorithm(privateKey, privateKeyInput, this.issuer.dpop_signing_alg_values_supported);\n            default:\n                throw new TypeError(\"unsupported DPoP private key\");\n        }\n    };\n    const RSPS = /^(?:RS|PS)(?:256|384|512)$/;\n    function determineRsaAlgorithm(privateKey, privateKeyInput, valuesSupported) {\n        if (typeof privateKeyInput === \"object\" && privateKeyInput.format === \"jwk\" && privateKeyInput.key && privateKeyInput.key.alg) {\n            return privateKeyInput.key.alg;\n        }\n        if (Array.isArray(valuesSupported)) {\n            let candidates = valuesSupported.filter(RegExp.prototype.test.bind(RSPS));\n            if (privateKey.asymmetricKeyType === \"rsa-pss\") {\n                candidates = candidates.filter((value)=>value.startsWith(\"PS\"));\n            }\n            return [\n                \"PS256\",\n                \"PS384\",\n                \"PS512\",\n                \"RS256\",\n                \"RS384\",\n                \"RS384\"\n            ].find((preferred)=>candidates.includes(preferred));\n        }\n        return \"PS256\";\n    }\n    const p256 = Buffer.from([\n        42,\n        134,\n        72,\n        206,\n        61,\n        3,\n        1,\n        7\n    ]);\n    const p384 = Buffer.from([\n        43,\n        129,\n        4,\n        0,\n        34\n    ]);\n    const p521 = Buffer.from([\n        43,\n        129,\n        4,\n        0,\n        35\n    ]);\n    const secp256k1 = Buffer.from([\n        43,\n        129,\n        4,\n        0,\n        10\n    ]);\n    function determineEcAlgorithm(privateKey, privateKeyInput) {\n        // If input was a JWK\n        switch(typeof privateKeyInput === \"object\" && typeof privateKeyInput.key === \"object\" && privateKeyInput.key.crv){\n            case \"P-256\":\n                return \"ES256\";\n            case \"secp256k1\":\n                return \"ES256K\";\n            case \"P-384\":\n                return \"ES384\";\n            case \"P-512\":\n                return \"ES512\";\n            default:\n                break;\n        }\n        const buf = privateKey.export({\n            format: \"der\",\n            type: \"pkcs8\"\n        });\n        const i = buf[1] < 128 ? 17 : 18;\n        const len = buf[i];\n        const curveOid = buf.slice(i + 1, i + 1 + len);\n        if (curveOid.equals(p256)) {\n            return \"ES256\";\n        }\n        if (curveOid.equals(p384)) {\n            return \"ES384\";\n        }\n        if (curveOid.equals(p521)) {\n            return \"ES512\";\n        }\n        if (curveOid.equals(secp256k1)) {\n            return \"ES256K\";\n        }\n        throw new TypeError(\"unsupported DPoP private key curve\");\n    }\n} else {\n    determineDPoPAlgorithm = determineDPoPAlgorithmFromCryptoKey;\n}\nconst jwkCache = new WeakMap();\nasync function getJwk(keyObject, privateKeyInput) {\n    if (jose.cryptoRuntime === \"node:crypto\" && typeof privateKeyInput === \"object\" && typeof privateKeyInput.key === \"object\" && privateKeyInput.format === \"jwk\") {\n        return pick(privateKeyInput.key, \"kty\", \"crv\", \"x\", \"y\", \"e\", \"n\");\n    }\n    if (jwkCache.has(privateKeyInput)) {\n        return jwkCache.get(privateKeyInput);\n    }\n    const jwk = pick(await jose.exportJWK(keyObject), \"kty\", \"crv\", \"x\", \"y\", \"e\", \"n\");\n    if (isKeyObject(privateKeyInput) || jose.cryptoRuntime === \"WebCryptoAPI\") {\n        jwkCache.set(privateKeyInput, jwk);\n    }\n    return jwk;\n}\nmodule.exports = (issuer, aadIssValidation = false)=>class Client extends BaseClient {\n        constructor(...args){\n            super(issuer, aadIssValidation, ...args);\n        }\n        static get issuer() {\n            return issuer;\n        }\n    };\nmodule.exports.BaseClient = BaseClient;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/openid-client/lib/client.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/openid-client/lib/device_flow_handle.js":
/*!**************************************************************!*\
  !*** ./node_modules/openid-client/lib/device_flow_handle.js ***!
  \**************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst { inspect } = __webpack_require__(/*! util */ \"util\");\nconst { RPError, OPError } = __webpack_require__(/*! ./errors */ \"(ssr)/./node_modules/openid-client/lib/errors.js\");\nconst now = __webpack_require__(/*! ./helpers/unix_timestamp */ \"(ssr)/./node_modules/openid-client/lib/helpers/unix_timestamp.js\");\nclass DeviceFlowHandle {\n    #aborted;\n    #client;\n    #clientAssertionPayload;\n    #DPoP;\n    #exchangeBody;\n    #expires_at;\n    #interval;\n    #maxAge;\n    #response;\n    constructor({ client, exchangeBody, clientAssertionPayload, response, maxAge, DPoP }){\n        [\n            \"verification_uri\",\n            \"user_code\",\n            \"device_code\"\n        ].forEach((prop)=>{\n            if (typeof response[prop] !== \"string\" || !response[prop]) {\n                throw new RPError(`expected ${prop} string to be returned by Device Authorization Response, got %j`, response[prop]);\n            }\n        });\n        if (!Number.isSafeInteger(response.expires_in)) {\n            throw new RPError(\"expected expires_in number to be returned by Device Authorization Response, got %j\", response.expires_in);\n        }\n        this.#expires_at = now() + response.expires_in;\n        this.#client = client;\n        this.#DPoP = DPoP;\n        this.#maxAge = maxAge;\n        this.#exchangeBody = exchangeBody;\n        this.#clientAssertionPayload = clientAssertionPayload;\n        this.#response = response;\n        this.#interval = response.interval * 1000 || 5000;\n    }\n    abort() {\n        this.#aborted = true;\n    }\n    async poll({ signal } = {}) {\n        if (signal && signal.aborted || this.#aborted) {\n            throw new RPError(\"polling aborted\");\n        }\n        if (this.expired()) {\n            throw new RPError(\"the device code %j has expired and the device authorization session has concluded\", this.device_code);\n        }\n        await new Promise((resolve)=>setTimeout(resolve, this.#interval));\n        let tokenset;\n        try {\n            tokenset = await this.#client.grant({\n                ...this.#exchangeBody,\n                grant_type: \"urn:ietf:params:oauth:grant-type:device_code\",\n                device_code: this.device_code\n            }, {\n                clientAssertionPayload: this.#clientAssertionPayload,\n                DPoP: this.#DPoP\n            });\n        } catch (err) {\n            switch(err instanceof OPError && err.error){\n                case \"slow_down\":\n                    this.#interval += 5000;\n                case \"authorization_pending\":\n                    return this.poll({\n                        signal\n                    });\n                default:\n                    throw err;\n            }\n        }\n        if (\"id_token\" in tokenset) {\n            await this.#client.decryptIdToken(tokenset);\n            await this.#client.validateIdToken(tokenset, undefined, \"token\", this.#maxAge);\n        }\n        return tokenset;\n    }\n    get device_code() {\n        return this.#response.device_code;\n    }\n    get user_code() {\n        return this.#response.user_code;\n    }\n    get verification_uri() {\n        return this.#response.verification_uri;\n    }\n    get verification_uri_complete() {\n        return this.#response.verification_uri_complete;\n    }\n    get expires_in() {\n        return Math.max.apply(null, [\n            this.#expires_at - now(),\n            0\n        ]);\n    }\n    expired() {\n        return this.expires_in === 0;\n    }\n    /* istanbul ignore next */ [inspect.custom]() {\n        return `${this.constructor.name} ${inspect(this.#response, {\n            depth: Infinity,\n            colors: process.stdout.isTTY,\n            compact: false,\n            sorted: true\n        })}`;\n    }\n}\nmodule.exports = DeviceFlowHandle;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvZGV2aWNlX2Zsb3dfaGFuZGxlLmpzIiwibWFwcGluZ3MiOiI7QUFBQSxNQUFNLEVBQUVBLE9BQU8sRUFBRSxHQUFHQyxtQkFBT0EsQ0FBQztBQUU1QixNQUFNLEVBQUVDLE9BQU8sRUFBRUMsT0FBTyxFQUFFLEdBQUdGLG1CQUFPQSxDQUFDO0FBQ3JDLE1BQU1HLE1BQU1ILG1CQUFPQSxDQUFDO0FBRXBCLE1BQU1JO0lBQ0osQ0FBQ0MsT0FBTyxDQUFDO0lBQ1QsQ0FBQ0MsTUFBTSxDQUFDO0lBQ1IsQ0FBQ0Msc0JBQXNCLENBQUM7SUFDeEIsQ0FBQ0MsSUFBSSxDQUFDO0lBQ04sQ0FBQ0MsWUFBWSxDQUFDO0lBQ2QsQ0FBQ0MsVUFBVSxDQUFDO0lBQ1osQ0FBQ0MsUUFBUSxDQUFDO0lBQ1YsQ0FBQ0MsTUFBTSxDQUFDO0lBQ1IsQ0FBQ0MsUUFBUSxDQUFDO0lBQ1ZDLFlBQVksRUFBRVIsTUFBTSxFQUFFRyxZQUFZLEVBQUVGLHNCQUFzQixFQUFFTSxRQUFRLEVBQUVELE1BQU0sRUFBRUosSUFBSSxFQUFFLENBQUU7UUFDcEY7WUFBQztZQUFvQjtZQUFhO1NBQWMsQ0FBQ08sT0FBTyxDQUFDLENBQUNDO1lBQ3hELElBQUksT0FBT0gsUUFBUSxDQUFDRyxLQUFLLEtBQUssWUFBWSxDQUFDSCxRQUFRLENBQUNHLEtBQUssRUFBRTtnQkFDekQsTUFBTSxJQUFJZixRQUNSLENBQUMsU0FBUyxFQUFFZSxLQUFLLCtEQUErRCxDQUFDLEVBQ2pGSCxRQUFRLENBQUNHLEtBQUs7WUFFbEI7UUFDRjtRQUVBLElBQUksQ0FBQ0MsT0FBT0MsYUFBYSxDQUFDTCxTQUFTTSxVQUFVLEdBQUc7WUFDOUMsTUFBTSxJQUFJbEIsUUFDUixzRkFDQVksU0FBU00sVUFBVTtRQUV2QjtRQUVBLElBQUksQ0FBQyxDQUFDVCxVQUFVLEdBQUdQLFFBQVFVLFNBQVNNLFVBQVU7UUFDOUMsSUFBSSxDQUFDLENBQUNiLE1BQU0sR0FBR0E7UUFDZixJQUFJLENBQUMsQ0FBQ0UsSUFBSSxHQUFHQTtRQUNiLElBQUksQ0FBQyxDQUFDSSxNQUFNLEdBQUdBO1FBQ2YsSUFBSSxDQUFDLENBQUNILFlBQVksR0FBR0E7UUFDckIsSUFBSSxDQUFDLENBQUNGLHNCQUFzQixHQUFHQTtRQUMvQixJQUFJLENBQUMsQ0FBQ00sUUFBUSxHQUFHQTtRQUNqQixJQUFJLENBQUMsQ0FBQ0YsUUFBUSxHQUFHRSxTQUFTRixRQUFRLEdBQUcsUUFBUTtJQUMvQztJQUVBUyxRQUFRO1FBQ04sSUFBSSxDQUFDLENBQUNmLE9BQU8sR0FBRztJQUNsQjtJQUVBLE1BQU1nQixLQUFLLEVBQUVDLE1BQU0sRUFBRSxHQUFHLENBQUMsQ0FBQyxFQUFFO1FBQzFCLElBQUksVUFBV0EsT0FBT2pCLE9BQU8sSUFBSyxJQUFJLENBQUMsQ0FBQ0EsT0FBTyxFQUFFO1lBQy9DLE1BQU0sSUFBSUosUUFBUTtRQUNwQjtRQUVBLElBQUksSUFBSSxDQUFDc0IsT0FBTyxJQUFJO1lBQ2xCLE1BQU0sSUFBSXRCLFFBQ1IscUZBQ0EsSUFBSSxDQUFDdUIsV0FBVztRQUVwQjtRQUVBLE1BQU0sSUFBSUMsUUFBUSxDQUFDQyxVQUFZQyxXQUFXRCxTQUFTLElBQUksQ0FBQyxDQUFDZixRQUFRO1FBRWpFLElBQUlpQjtRQUNKLElBQUk7WUFDRkEsV0FBVyxNQUFNLElBQUksQ0FBQyxDQUFDdEIsTUFBTSxDQUFDdUIsS0FBSyxDQUNqQztnQkFDRSxHQUFHLElBQUksQ0FBQyxDQUFDcEIsWUFBWTtnQkFDckJxQixZQUFZO2dCQUNaTixhQUFhLElBQUksQ0FBQ0EsV0FBVztZQUMvQixHQUNBO2dCQUFFakIsd0JBQXdCLElBQUksQ0FBQyxDQUFDQSxzQkFBc0I7Z0JBQUVDLE1BQU0sSUFBSSxDQUFDLENBQUNBLElBQUk7WUFBQztRQUU3RSxFQUFFLE9BQU91QixLQUFLO1lBQ1osT0FBUUEsZUFBZTdCLFdBQVc2QixJQUFJQyxLQUFLO2dCQUN6QyxLQUFLO29CQUNILElBQUksQ0FBQyxDQUFDckIsUUFBUSxJQUFJO2dCQUNwQixLQUFLO29CQUNILE9BQU8sSUFBSSxDQUFDVSxJQUFJLENBQUM7d0JBQUVDO29CQUFPO2dCQUM1QjtvQkFDRSxNQUFNUztZQUNWO1FBQ0Y7UUFFQSxJQUFJLGNBQWNILFVBQVU7WUFDMUIsTUFBTSxJQUFJLENBQUMsQ0FBQ3RCLE1BQU0sQ0FBQzJCLGNBQWMsQ0FBQ0w7WUFDbEMsTUFBTSxJQUFJLENBQUMsQ0FBQ3RCLE1BQU0sQ0FBQzRCLGVBQWUsQ0FBQ04sVUFBVU8sV0FBVyxTQUFTLElBQUksQ0FBQyxDQUFDdkIsTUFBTTtRQUMvRTtRQUVBLE9BQU9nQjtJQUNUO0lBRUEsSUFBSUosY0FBYztRQUNoQixPQUFPLElBQUksQ0FBQyxDQUFDWCxRQUFRLENBQUNXLFdBQVc7SUFDbkM7SUFFQSxJQUFJWSxZQUFZO1FBQ2QsT0FBTyxJQUFJLENBQUMsQ0FBQ3ZCLFFBQVEsQ0FBQ3VCLFNBQVM7SUFDakM7SUFFQSxJQUFJQyxtQkFBbUI7UUFDckIsT0FBTyxJQUFJLENBQUMsQ0FBQ3hCLFFBQVEsQ0FBQ3dCLGdCQUFnQjtJQUN4QztJQUVBLElBQUlDLDRCQUE0QjtRQUM5QixPQUFPLElBQUksQ0FBQyxDQUFDekIsUUFBUSxDQUFDeUIseUJBQXlCO0lBQ2pEO0lBRUEsSUFBSW5CLGFBQWE7UUFDZixPQUFPb0IsS0FBS0MsR0FBRyxDQUFDQyxLQUFLLENBQUMsTUFBTTtZQUFDLElBQUksQ0FBQyxDQUFDL0IsVUFBVSxHQUFHUDtZQUFPO1NBQUU7SUFDM0Q7SUFFQW9CLFVBQVU7UUFDUixPQUFPLElBQUksQ0FBQ0osVUFBVSxLQUFLO0lBQzdCO0lBRUEsd0JBQXdCLEdBQ3hCLENBQUNwQixRQUFRMkMsTUFBTSxDQUFDLEdBQUc7UUFDakIsT0FBTyxDQUFDLEVBQUUsSUFBSSxDQUFDNUIsV0FBVyxDQUFDNkIsSUFBSSxDQUFDLENBQUMsRUFBRTVDLFFBQVEsSUFBSSxDQUFDLENBQUNjLFFBQVEsRUFBRTtZQUN6RCtCLE9BQU9DO1lBQ1BDLFFBQVFDLFFBQVFDLE1BQU0sQ0FBQ0MsS0FBSztZQUM1QkMsU0FBUztZQUNUQyxRQUFRO1FBQ1YsR0FBRyxDQUFDO0lBQ047QUFDRjtBQUVBQyxPQUFPQyxPQUFPLEdBQUdqRCIsInNvdXJjZXMiOlsid2VicGFjazovL21hcmtldHBsYWNlLy4vbm9kZV9tb2R1bGVzL29wZW5pZC1jbGllbnQvbGliL2RldmljZV9mbG93X2hhbmRsZS5qcz83Mzc2Il0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IHsgaW5zcGVjdCB9ID0gcmVxdWlyZSgndXRpbCcpO1xuXG5jb25zdCB7IFJQRXJyb3IsIE9QRXJyb3IgfSA9IHJlcXVpcmUoJy4vZXJyb3JzJyk7XG5jb25zdCBub3cgPSByZXF1aXJlKCcuL2hlbHBlcnMvdW5peF90aW1lc3RhbXAnKTtcblxuY2xhc3MgRGV2aWNlRmxvd0hhbmRsZSB7XG4gICNhYm9ydGVkO1xuICAjY2xpZW50O1xuICAjY2xpZW50QXNzZXJ0aW9uUGF5bG9hZDtcbiAgI0RQb1A7XG4gICNleGNoYW5nZUJvZHk7XG4gICNleHBpcmVzX2F0O1xuICAjaW50ZXJ2YWw7XG4gICNtYXhBZ2U7XG4gICNyZXNwb25zZTtcbiAgY29uc3RydWN0b3IoeyBjbGllbnQsIGV4Y2hhbmdlQm9keSwgY2xpZW50QXNzZXJ0aW9uUGF5bG9hZCwgcmVzcG9uc2UsIG1heEFnZSwgRFBvUCB9KSB7XG4gICAgWyd2ZXJpZmljYXRpb25fdXJpJywgJ3VzZXJfY29kZScsICdkZXZpY2VfY29kZSddLmZvckVhY2goKHByb3ApID0+IHtcbiAgICAgIGlmICh0eXBlb2YgcmVzcG9uc2VbcHJvcF0gIT09ICdzdHJpbmcnIHx8ICFyZXNwb25zZVtwcm9wXSkge1xuICAgICAgICB0aHJvdyBuZXcgUlBFcnJvcihcbiAgICAgICAgICBgZXhwZWN0ZWQgJHtwcm9wfSBzdHJpbmcgdG8gYmUgcmV0dXJuZWQgYnkgRGV2aWNlIEF1dGhvcml6YXRpb24gUmVzcG9uc2UsIGdvdCAlamAsXG4gICAgICAgICAgcmVzcG9uc2VbcHJvcF0sXG4gICAgICAgICk7XG4gICAgICB9XG4gICAgfSk7XG5cbiAgICBpZiAoIU51bWJlci5pc1NhZmVJbnRlZ2VyKHJlc3BvbnNlLmV4cGlyZXNfaW4pKSB7XG4gICAgICB0aHJvdyBuZXcgUlBFcnJvcihcbiAgICAgICAgJ2V4cGVjdGVkIGV4cGlyZXNfaW4gbnVtYmVyIHRvIGJlIHJldHVybmVkIGJ5IERldmljZSBBdXRob3JpemF0aW9uIFJlc3BvbnNlLCBnb3QgJWonLFxuICAgICAgICByZXNwb25zZS5leHBpcmVzX2luLFxuICAgICAgKTtcbiAgICB9XG5cbiAgICB0aGlzLiNleHBpcmVzX2F0ID0gbm93KCkgKyByZXNwb25zZS5leHBpcmVzX2luO1xuICAgIHRoaXMuI2NsaWVudCA9IGNsaWVudDtcbiAgICB0aGlzLiNEUG9QID0gRFBvUDtcbiAgICB0aGlzLiNtYXhBZ2UgPSBtYXhBZ2U7XG4gICAgdGhpcy4jZXhjaGFuZ2VCb2R5ID0gZXhjaGFuZ2VCb2R5O1xuICAgIHRoaXMuI2NsaWVudEFzc2VydGlvblBheWxvYWQgPSBjbGllbnRBc3NlcnRpb25QYXlsb2FkO1xuICAgIHRoaXMuI3Jlc3BvbnNlID0gcmVzcG9uc2U7XG4gICAgdGhpcy4jaW50ZXJ2YWwgPSByZXNwb25zZS5pbnRlcnZhbCAqIDEwMDAgfHwgNTAwMDtcbiAgfVxuXG4gIGFib3J0KCkge1xuICAgIHRoaXMuI2Fib3J0ZWQgPSB0cnVlO1xuICB9XG5cbiAgYXN5bmMgcG9sbCh7IHNpZ25hbCB9ID0ge30pIHtcbiAgICBpZiAoKHNpZ25hbCAmJiBzaWduYWwuYWJvcnRlZCkgfHwgdGhpcy4jYWJvcnRlZCkge1xuICAgICAgdGhyb3cgbmV3IFJQRXJyb3IoJ3BvbGxpbmcgYWJvcnRlZCcpO1xuICAgIH1cblxuICAgIGlmICh0aGlzLmV4cGlyZWQoKSkge1xuICAgICAgdGhyb3cgbmV3IFJQRXJyb3IoXG4gICAgICAgICd0aGUgZGV2aWNlIGNvZGUgJWogaGFzIGV4cGlyZWQgYW5kIHRoZSBkZXZpY2UgYXV0aG9yaXphdGlvbiBzZXNzaW9uIGhhcyBjb25jbHVkZWQnLFxuICAgICAgICB0aGlzLmRldmljZV9jb2RlLFxuICAgICAgKTtcbiAgICB9XG5cbiAgICBhd2FpdCBuZXcgUHJvbWlzZSgocmVzb2x2ZSkgPT4gc2V0VGltZW91dChyZXNvbHZlLCB0aGlzLiNpbnRlcnZhbCkpO1xuXG4gICAgbGV0IHRva2Vuc2V0O1xuICAgIHRyeSB7XG4gICAgICB0b2tlbnNldCA9IGF3YWl0IHRoaXMuI2NsaWVudC5ncmFudChcbiAgICAgICAge1xuICAgICAgICAgIC4uLnRoaXMuI2V4Y2hhbmdlQm9keSxcbiAgICAgICAgICBncmFudF90eXBlOiAndXJuOmlldGY6cGFyYW1zOm9hdXRoOmdyYW50LXR5cGU6ZGV2aWNlX2NvZGUnLFxuICAgICAgICAgIGRldmljZV9jb2RlOiB0aGlzLmRldmljZV9jb2RlLFxuICAgICAgICB9LFxuICAgICAgICB7IGNsaWVudEFzc2VydGlvblBheWxvYWQ6IHRoaXMuI2NsaWVudEFzc2VydGlvblBheWxvYWQsIERQb1A6IHRoaXMuI0RQb1AgfSxcbiAgICAgICk7XG4gICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICBzd2l0Y2ggKGVyciBpbnN0YW5jZW9mIE9QRXJyb3IgJiYgZXJyLmVycm9yKSB7XG4gICAgICAgIGNhc2UgJ3Nsb3dfZG93bic6XG4gICAgICAgICAgdGhpcy4jaW50ZXJ2YWwgKz0gNTAwMDtcbiAgICAgICAgY2FzZSAnYXV0aG9yaXphdGlvbl9wZW5kaW5nJzpcbiAgICAgICAgICByZXR1cm4gdGhpcy5wb2xsKHsgc2lnbmFsIH0pO1xuICAgICAgICBkZWZhdWx0OlxuICAgICAgICAgIHRocm93IGVycjtcbiAgICAgIH1cbiAgICB9XG5cbiAgICBpZiAoJ2lkX3Rva2VuJyBpbiB0b2tlbnNldCkge1xuICAgICAgYXdhaXQgdGhpcy4jY2xpZW50LmRlY3J5cHRJZFRva2VuKHRva2Vuc2V0KTtcbiAgICAgIGF3YWl0IHRoaXMuI2NsaWVudC52YWxpZGF0ZUlkVG9rZW4odG9rZW5zZXQsIHVuZGVmaW5lZCwgJ3Rva2VuJywgdGhpcy4jbWF4QWdlKTtcbiAgICB9XG5cbiAgICByZXR1cm4gdG9rZW5zZXQ7XG4gIH1cblxuICBnZXQgZGV2aWNlX2NvZGUoKSB7XG4gICAgcmV0dXJuIHRoaXMuI3Jlc3BvbnNlLmRldmljZV9jb2RlO1xuICB9XG5cbiAgZ2V0IHVzZXJfY29kZSgpIHtcbiAgICByZXR1cm4gdGhpcy4jcmVzcG9uc2UudXNlcl9jb2RlO1xuICB9XG5cbiAgZ2V0IHZlcmlmaWNhdGlvbl91cmkoKSB7XG4gICAgcmV0dXJuIHRoaXMuI3Jlc3BvbnNlLnZlcmlmaWNhdGlvbl91cmk7XG4gIH1cblxuICBnZXQgdmVyaWZpY2F0aW9uX3VyaV9jb21wbGV0ZSgpIHtcbiAgICByZXR1cm4gdGhpcy4jcmVzcG9uc2UudmVyaWZpY2F0aW9uX3VyaV9jb21wbGV0ZTtcbiAgfVxuXG4gIGdldCBleHBpcmVzX2luKCkge1xuICAgIHJldHVybiBNYXRoLm1heC5hcHBseShudWxsLCBbdGhpcy4jZXhwaXJlc19hdCAtIG5vdygpLCAwXSk7XG4gIH1cblxuICBleHBpcmVkKCkge1xuICAgIHJldHVybiB0aGlzLmV4cGlyZXNfaW4gPT09IDA7XG4gIH1cblxuICAvKiBpc3RhbmJ1bCBpZ25vcmUgbmV4dCAqL1xuICBbaW5zcGVjdC5jdXN0b21dKCkge1xuICAgIHJldHVybiBgJHt0aGlzLmNvbnN0cnVjdG9yLm5hbWV9ICR7aW5zcGVjdCh0aGlzLiNyZXNwb25zZSwge1xuICAgICAgZGVwdGg6IEluZmluaXR5LFxuICAgICAgY29sb3JzOiBwcm9jZXNzLnN0ZG91dC5pc1RUWSxcbiAgICAgIGNvbXBhY3Q6IGZhbHNlLFxuICAgICAgc29ydGVkOiB0cnVlLFxuICAgIH0pfWA7XG4gIH1cbn1cblxubW9kdWxlLmV4cG9ydHMgPSBEZXZpY2VGbG93SGFuZGxlO1xuIl0sIm5hbWVzIjpbImluc3BlY3QiLCJyZXF1aXJlIiwiUlBFcnJvciIsIk9QRXJyb3IiLCJub3ciLCJEZXZpY2VGbG93SGFuZGxlIiwiYWJvcnRlZCIsImNsaWVudCIsImNsaWVudEFzc2VydGlvblBheWxvYWQiLCJEUG9QIiwiZXhjaGFuZ2VCb2R5IiwiZXhwaXJlc19hdCIsImludGVydmFsIiwibWF4QWdlIiwicmVzcG9uc2UiLCJjb25zdHJ1Y3RvciIsImZvckVhY2giLCJwcm9wIiwiTnVtYmVyIiwiaXNTYWZlSW50ZWdlciIsImV4cGlyZXNfaW4iLCJhYm9ydCIsInBvbGwiLCJzaWduYWwiLCJleHBpcmVkIiwiZGV2aWNlX2NvZGUiLCJQcm9taXNlIiwicmVzb2x2ZSIsInNldFRpbWVvdXQiLCJ0b2tlbnNldCIsImdyYW50IiwiZ3JhbnRfdHlwZSIsImVyciIsImVycm9yIiwiZGVjcnlwdElkVG9rZW4iLCJ2YWxpZGF0ZUlkVG9rZW4iLCJ1bmRlZmluZWQiLCJ1c2VyX2NvZGUiLCJ2ZXJpZmljYXRpb25fdXJpIiwidmVyaWZpY2F0aW9uX3VyaV9jb21wbGV0ZSIsIk1hdGgiLCJtYXgiLCJhcHBseSIsImN1c3RvbSIsIm5hbWUiLCJkZXB0aCIsIkluZmluaXR5IiwiY29sb3JzIiwicHJvY2VzcyIsInN0ZG91dCIsImlzVFRZIiwiY29tcGFjdCIsInNvcnRlZCIsIm1vZHVsZSIsImV4cG9ydHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/openid-client/lib/device_flow_handle.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/openid-client/lib/errors.js":
/*!**************************************************!*\
  !*** ./node_modules/openid-client/lib/errors.js ***!
  \**************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst { format } = __webpack_require__(/*! util */ \"util\");\nclass OPError extends Error {\n    constructor({ error_description, error, error_uri, session_state, state, scope }, response){\n        super(!error_description ? error : `${error} (${error_description})`);\n        Object.assign(this, {\n            error\n        }, error_description && {\n            error_description\n        }, error_uri && {\n            error_uri\n        }, state && {\n            state\n        }, scope && {\n            scope\n        }, session_state && {\n            session_state\n        });\n        if (response) {\n            Object.defineProperty(this, \"response\", {\n                value: response\n            });\n        }\n        this.name = this.constructor.name;\n        Error.captureStackTrace(this, this.constructor);\n    }\n}\nclass RPError extends Error {\n    constructor(...args){\n        if (typeof args[0] === \"string\") {\n            super(format(...args));\n        } else {\n            const { message, printf, response, ...rest } = args[0];\n            if (printf) {\n                super(format(...printf));\n            } else {\n                super(message);\n            }\n            Object.assign(this, rest);\n            if (response) {\n                Object.defineProperty(this, \"response\", {\n                    value: response\n                });\n            }\n        }\n        this.name = this.constructor.name;\n        Error.captureStackTrace(this, this.constructor);\n    }\n}\nmodule.exports = {\n    OPError,\n    RPError\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/openid-client/lib/errors.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/openid-client/lib/helpers/assert.js":
/*!**********************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/assert.js ***!
  \**********************************************************/
/***/ ((module) => {

eval("\nfunction assertSigningAlgValuesSupport(endpoint, issuer, properties) {\n    if (!issuer[`${endpoint}_endpoint`]) return;\n    const eam = `${endpoint}_endpoint_auth_method`;\n    const easa = `${endpoint}_endpoint_auth_signing_alg`;\n    const easavs = `${endpoint}_endpoint_auth_signing_alg_values_supported`;\n    if (properties[eam] && properties[eam].endsWith(\"_jwt\") && !properties[easa] && !issuer[easavs]) {\n        throw new TypeError(`${easavs} must be configured on the issuer if ${easa} is not defined on a client`);\n    }\n}\nfunction assertIssuerConfiguration(issuer, endpoint) {\n    if (!issuer[endpoint]) {\n        throw new TypeError(`${endpoint} must be configured on the issuer`);\n    }\n}\nmodule.exports = {\n    assertSigningAlgValuesSupport,\n    assertIssuerConfiguration\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/openid-client/lib/helpers/assert.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/openid-client/lib/helpers/base64url.js":
/*!*************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/base64url.js ***!
  \*************************************************************/
/***/ ((module) => {

eval("\nlet encode;\nif (Buffer.isEncoding(\"base64url\")) {\n    encode = (input, encoding = \"utf8\")=>Buffer.from(input, encoding).toString(\"base64url\");\n} else {\n    const fromBase64 = (base64)=>base64.replace(/=/g, \"\").replace(/\\+/g, \"-\").replace(/\\//g, \"_\");\n    encode = (input, encoding = \"utf8\")=>fromBase64(Buffer.from(input, encoding).toString(\"base64\"));\n}\nconst decode = (input)=>Buffer.from(input, \"base64\");\nmodule.exports.decode = decode;\nmodule.exports.encode = encode;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9iYXNlNjR1cmwuanMiLCJtYXBwaW5ncyI6IjtBQUFBLElBQUlBO0FBQ0osSUFBSUMsT0FBT0MsVUFBVSxDQUFDLGNBQWM7SUFDbENGLFNBQVMsQ0FBQ0csT0FBT0MsV0FBVyxNQUFNLEdBQUtILE9BQU9JLElBQUksQ0FBQ0YsT0FBT0MsVUFBVUUsUUFBUSxDQUFDO0FBQy9FLE9BQU87SUFDTCxNQUFNQyxhQUFhLENBQUNDLFNBQVdBLE9BQU9DLE9BQU8sQ0FBQyxNQUFNLElBQUlBLE9BQU8sQ0FBQyxPQUFPLEtBQUtBLE9BQU8sQ0FBQyxPQUFPO0lBQzNGVCxTQUFTLENBQUNHLE9BQU9DLFdBQVcsTUFBTSxHQUNoQ0csV0FBV04sT0FBT0ksSUFBSSxDQUFDRixPQUFPQyxVQUFVRSxRQUFRLENBQUM7QUFDckQ7QUFFQSxNQUFNSSxTQUFTLENBQUNQLFFBQVVGLE9BQU9JLElBQUksQ0FBQ0YsT0FBTztBQUU3Q1EscUJBQXFCLEdBQUdEO0FBQ3hCQyxxQkFBcUIsR0FBR1giLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJrZXRwbGFjZS8uL25vZGVfbW9kdWxlcy9vcGVuaWQtY2xpZW50L2xpYi9oZWxwZXJzL2Jhc2U2NHVybC5qcz8zYjVlIl0sInNvdXJjZXNDb250ZW50IjpbImxldCBlbmNvZGU7XG5pZiAoQnVmZmVyLmlzRW5jb2RpbmcoJ2Jhc2U2NHVybCcpKSB7XG4gIGVuY29kZSA9IChpbnB1dCwgZW5jb2RpbmcgPSAndXRmOCcpID0+IEJ1ZmZlci5mcm9tKGlucHV0LCBlbmNvZGluZykudG9TdHJpbmcoJ2Jhc2U2NHVybCcpO1xufSBlbHNlIHtcbiAgY29uc3QgZnJvbUJhc2U2NCA9IChiYXNlNjQpID0+IGJhc2U2NC5yZXBsYWNlKC89L2csICcnKS5yZXBsYWNlKC9cXCsvZywgJy0nKS5yZXBsYWNlKC9cXC8vZywgJ18nKTtcbiAgZW5jb2RlID0gKGlucHV0LCBlbmNvZGluZyA9ICd1dGY4JykgPT5cbiAgICBmcm9tQmFzZTY0KEJ1ZmZlci5mcm9tKGlucHV0LCBlbmNvZGluZykudG9TdHJpbmcoJ2Jhc2U2NCcpKTtcbn1cblxuY29uc3QgZGVjb2RlID0gKGlucHV0KSA9PiBCdWZmZXIuZnJvbShpbnB1dCwgJ2Jhc2U2NCcpO1xuXG5tb2R1bGUuZXhwb3J0cy5kZWNvZGUgPSBkZWNvZGU7XG5tb2R1bGUuZXhwb3J0cy5lbmNvZGUgPSBlbmNvZGU7XG4iXSwibmFtZXMiOlsiZW5jb2RlIiwiQnVmZmVyIiwiaXNFbmNvZGluZyIsImlucHV0IiwiZW5jb2RpbmciLCJmcm9tIiwidG9TdHJpbmciLCJmcm9tQmFzZTY0IiwiYmFzZTY0IiwicmVwbGFjZSIsImRlY29kZSIsIm1vZHVsZSIsImV4cG9ydHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/openid-client/lib/helpers/base64url.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/openid-client/lib/helpers/client.js":
/*!**********************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/client.js ***!
  \**********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst jose = __webpack_require__(/*! jose */ \"(ssr)/./node_modules/jose/dist/node/cjs/index.js\");\nconst { RPError } = __webpack_require__(/*! ../errors */ \"(ssr)/./node_modules/openid-client/lib/errors.js\");\nconst { assertIssuerConfiguration } = __webpack_require__(/*! ./assert */ \"(ssr)/./node_modules/openid-client/lib/helpers/assert.js\");\nconst { random } = __webpack_require__(/*! ./generators */ \"(ssr)/./node_modules/openid-client/lib/helpers/generators.js\");\nconst now = __webpack_require__(/*! ./unix_timestamp */ \"(ssr)/./node_modules/openid-client/lib/helpers/unix_timestamp.js\");\nconst request = __webpack_require__(/*! ./request */ \"(ssr)/./node_modules/openid-client/lib/helpers/request.js\");\nconst { keystores } = __webpack_require__(/*! ./weak_cache */ \"(ssr)/./node_modules/openid-client/lib/helpers/weak_cache.js\");\nconst merge = __webpack_require__(/*! ./merge */ \"(ssr)/./node_modules/openid-client/lib/helpers/merge.js\");\n// TODO: in v6.x additionally encode the `- _ . ! ~ * ' ( )` characters\n// https://github.com/panva/node-openid-client/commit/5a2ea80ef5e59ec0c03dbd97d82f551e24a9d348\nconst formUrlEncode = (value)=>encodeURIComponent(value).replace(/%20/g, \"+\");\nasync function clientAssertion(endpoint, payload) {\n    let alg = this[`${endpoint}_endpoint_auth_signing_alg`];\n    if (!alg) {\n        assertIssuerConfiguration(this.issuer, `${endpoint}_endpoint_auth_signing_alg_values_supported`);\n    }\n    if (this[`${endpoint}_endpoint_auth_method`] === \"client_secret_jwt\") {\n        if (!alg) {\n            const supported = this.issuer[`${endpoint}_endpoint_auth_signing_alg_values_supported`];\n            alg = Array.isArray(supported) && supported.find((signAlg)=>/^HS(?:256|384|512)/.test(signAlg));\n        }\n        if (!alg) {\n            throw new RPError(`failed to determine a JWS Algorithm to use for ${this[`${endpoint}_endpoint_auth_method`]} Client Assertion`);\n        }\n        return new jose.CompactSign(Buffer.from(JSON.stringify(payload))).setProtectedHeader({\n            alg\n        }).sign(this.secretForAlg(alg));\n    }\n    const keystore = await keystores.get(this);\n    if (!keystore) {\n        throw new TypeError(\"no client jwks provided for signing a client assertion with\");\n    }\n    if (!alg) {\n        const supported = this.issuer[`${endpoint}_endpoint_auth_signing_alg_values_supported`];\n        alg = Array.isArray(supported) && supported.find((signAlg)=>keystore.get({\n                alg: signAlg,\n                use: \"sig\"\n            }));\n    }\n    if (!alg) {\n        throw new RPError(`failed to determine a JWS Algorithm to use for ${this[`${endpoint}_endpoint_auth_method`]} Client Assertion`);\n    }\n    const key = keystore.get({\n        alg,\n        use: \"sig\"\n    });\n    if (!key) {\n        throw new RPError(`no key found in client jwks to sign a client assertion with using alg ${alg}`);\n    }\n    return new jose.CompactSign(Buffer.from(JSON.stringify(payload))).setProtectedHeader({\n        alg,\n        kid: key.jwk && key.jwk.kid\n    }).sign(await key.keyObject(alg));\n}\nasync function authFor(endpoint, { clientAssertionPayload } = {}) {\n    const authMethod = this[`${endpoint}_endpoint_auth_method`];\n    switch(authMethod){\n        case \"self_signed_tls_client_auth\":\n        case \"tls_client_auth\":\n        case \"none\":\n            return {\n                form: {\n                    client_id: this.client_id\n                }\n            };\n        case \"client_secret_post\":\n            if (typeof this.client_secret !== \"string\") {\n                throw new TypeError(\"client_secret_post client authentication method requires a client_secret\");\n            }\n            return {\n                form: {\n                    client_id: this.client_id,\n                    client_secret: this.client_secret\n                }\n            };\n        case \"private_key_jwt\":\n        case \"client_secret_jwt\":\n            {\n                const timestamp = now();\n                const assertion = await clientAssertion.call(this, endpoint, {\n                    iat: timestamp,\n                    exp: timestamp + 60,\n                    jti: random(),\n                    iss: this.client_id,\n                    sub: this.client_id,\n                    aud: this.issuer.issuer,\n                    ...clientAssertionPayload\n                });\n                return {\n                    form: {\n                        client_id: this.client_id,\n                        client_assertion: assertion,\n                        client_assertion_type: \"urn:ietf:params:oauth:client-assertion-type:jwt-bearer\"\n                    }\n                };\n            }\n        case \"client_secret_basic\":\n            {\n                // This is correct behaviour, see https://tools.ietf.org/html/rfc6749#section-2.3.1 and the\n                // related appendix. (also https://github.com/panva/node-openid-client/pull/91)\n                // > The client identifier is encoded using the\n                // > \"application/x-www-form-urlencoded\" encoding algorithm per\n                // > Appendix B, and the encoded value is used as the username; the client\n                // > password is encoded using the same algorithm and used as the\n                // > password.\n                if (typeof this.client_secret !== \"string\") {\n                    throw new TypeError(\"client_secret_basic client authentication method requires a client_secret\");\n                }\n                const encoded = `${formUrlEncode(this.client_id)}:${formUrlEncode(this.client_secret)}`;\n                const value = Buffer.from(encoded).toString(\"base64\");\n                return {\n                    headers: {\n                        Authorization: `Basic ${value}`\n                    }\n                };\n            }\n        default:\n            {\n                throw new TypeError(`missing, or unsupported, ${endpoint}_endpoint_auth_method`);\n            }\n    }\n}\nfunction resolveResponseType() {\n    const { length, 0: value } = this.response_types;\n    if (length === 1) {\n        return value;\n    }\n    return undefined;\n}\nfunction resolveRedirectUri() {\n    const { length, 0: value } = this.redirect_uris || [];\n    if (length === 1) {\n        return value;\n    }\n    return undefined;\n}\nasync function authenticatedPost(endpoint, opts, { clientAssertionPayload, endpointAuthMethod = endpoint, DPoP } = {}) {\n    const auth = await authFor.call(this, endpointAuthMethod, {\n        clientAssertionPayload\n    });\n    const requestOpts = merge(opts, auth);\n    const mTLS = this[`${endpointAuthMethod}_endpoint_auth_method`].includes(\"tls_client_auth\") || endpoint === \"token\" && this.tls_client_certificate_bound_access_tokens;\n    let targetUrl;\n    if (mTLS && this.issuer.mtls_endpoint_aliases) {\n        targetUrl = this.issuer.mtls_endpoint_aliases[`${endpoint}_endpoint`];\n    }\n    targetUrl = targetUrl || this.issuer[`${endpoint}_endpoint`];\n    if (\"form\" in requestOpts) {\n        for (const [key, value] of Object.entries(requestOpts.form)){\n            if (typeof value === \"undefined\") {\n                delete requestOpts.form[key];\n            }\n        }\n    }\n    return request.call(this, {\n        ...requestOpts,\n        method: \"POST\",\n        url: targetUrl,\n        headers: {\n            ...endpoint !== \"revocation\" ? {\n                Accept: \"application/json\"\n            } : undefined,\n            ...requestOpts.headers\n        }\n    }, {\n        mTLS,\n        DPoP\n    });\n}\nmodule.exports = {\n    resolveResponseType,\n    resolveRedirectUri,\n    authFor,\n    authenticatedPost\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/openid-client/lib/helpers/client.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/openid-client/lib/helpers/consts.js":
/*!**********************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/consts.js ***!
  \**********************************************************/
/***/ ((module) => {

eval("\nconst HTTP_OPTIONS = Symbol();\nconst CLOCK_TOLERANCE = Symbol();\nmodule.exports = {\n    CLOCK_TOLERANCE,\n    HTTP_OPTIONS\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9jb25zdHMuanMiLCJtYXBwaW5ncyI6IjtBQUFBLE1BQU1BLGVBQWVDO0FBQ3JCLE1BQU1DLGtCQUFrQkQ7QUFFeEJFLE9BQU9DLE9BQU8sR0FBRztJQUNmRjtJQUNBRjtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFya2V0cGxhY2UvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9jb25zdHMuanM/ZTA1MyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBIVFRQX09QVElPTlMgPSBTeW1ib2woKTtcbmNvbnN0IENMT0NLX1RPTEVSQU5DRSA9IFN5bWJvbCgpO1xuXG5tb2R1bGUuZXhwb3J0cyA9IHtcbiAgQ0xPQ0tfVE9MRVJBTkNFLFxuICBIVFRQX09QVElPTlMsXG59O1xuIl0sIm5hbWVzIjpbIkhUVFBfT1BUSU9OUyIsIlN5bWJvbCIsIkNMT0NLX1RPTEVSQU5DRSIsIm1vZHVsZSIsImV4cG9ydHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/openid-client/lib/helpers/consts.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/openid-client/lib/helpers/decode_jwt.js":
/*!**************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/decode_jwt.js ***!
  \**************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst base64url = __webpack_require__(/*! ./base64url */ \"(ssr)/./node_modules/openid-client/lib/helpers/base64url.js\");\nmodule.exports = (token)=>{\n    if (typeof token !== \"string\" || !token) {\n        throw new TypeError(\"JWT must be a string\");\n    }\n    const { 0: header, 1: payload, 2: signature, length } = token.split(\".\");\n    if (length === 5) {\n        throw new TypeError(\"encrypted JWTs cannot be decoded\");\n    }\n    if (length !== 3) {\n        throw new Error(\"JWTs must have three components\");\n    }\n    try {\n        return {\n            header: JSON.parse(base64url.decode(header)),\n            payload: JSON.parse(base64url.decode(payload)),\n            signature\n        };\n    } catch (err) {\n        throw new Error(\"JWT is malformed\");\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/openid-client/lib/helpers/decode_jwt.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/openid-client/lib/helpers/deep_clone.js":
/*!**************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/deep_clone.js ***!
  \**************************************************************/
/***/ ((module) => {

eval("\nmodule.exports = globalThis.structuredClone || ((obj)=>JSON.parse(JSON.stringify(obj)));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9kZWVwX2Nsb25lLmpzIiwibWFwcGluZ3MiOiI7QUFBQUEsT0FBT0MsT0FBTyxHQUFHQyxXQUFXQyxlQUFlLElBQUssRUFBQ0MsTUFBUUMsS0FBS0MsS0FBSyxDQUFDRCxLQUFLRSxTQUFTLENBQUNILEtBQUkiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJrZXRwbGFjZS8uL25vZGVfbW9kdWxlcy9vcGVuaWQtY2xpZW50L2xpYi9oZWxwZXJzL2RlZXBfY2xvbmUuanM/OGU0YiJdLCJzb3VyY2VzQ29udGVudCI6WyJtb2R1bGUuZXhwb3J0cyA9IGdsb2JhbFRoaXMuc3RydWN0dXJlZENsb25lIHx8ICgob2JqKSA9PiBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KG9iaikpKTtcbiJdLCJuYW1lcyI6WyJtb2R1bGUiLCJleHBvcnRzIiwiZ2xvYmFsVGhpcyIsInN0cnVjdHVyZWRDbG9uZSIsIm9iaiIsIkpTT04iLCJwYXJzZSIsInN0cmluZ2lmeSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/openid-client/lib/helpers/deep_clone.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/openid-client/lib/helpers/defaults.js":
/*!************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/defaults.js ***!
  \************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst isPlainObject = __webpack_require__(/*! ./is_plain_object */ \"(ssr)/./node_modules/openid-client/lib/helpers/is_plain_object.js\");\nfunction defaults(deep, target, ...sources) {\n    for (const source of sources){\n        if (!isPlainObject(source)) {\n            continue;\n        }\n        for (const [key, value] of Object.entries(source)){\n            /* istanbul ignore if */ if (key === \"__proto__\" || key === \"constructor\") {\n                continue;\n            }\n            if (typeof target[key] === \"undefined\" && typeof value !== \"undefined\") {\n                target[key] = value;\n            }\n            if (deep && isPlainObject(target[key]) && isPlainObject(value)) {\n                defaults(true, target[key], value);\n            }\n        }\n    }\n    return target;\n}\nmodule.exports = defaults.bind(undefined, false);\nmodule.exports.deep = defaults.bind(undefined, true);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/openid-client/lib/helpers/defaults.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/openid-client/lib/helpers/generators.js":
/*!**************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/generators.js ***!
  \**************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst { createHash, randomBytes } = __webpack_require__(/*! crypto */ \"crypto\");\nconst base64url = __webpack_require__(/*! ./base64url */ \"(ssr)/./node_modules/openid-client/lib/helpers/base64url.js\");\nconst random = (bytes = 32)=>base64url.encode(randomBytes(bytes));\nmodule.exports = {\n    random,\n    state: random,\n    nonce: random,\n    codeVerifier: random,\n    codeChallenge: (codeVerifier)=>base64url.encode(createHash(\"sha256\").update(codeVerifier).digest())\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9nZW5lcmF0b3JzLmpzIiwibWFwcGluZ3MiOiI7QUFBQSxNQUFNLEVBQUVBLFVBQVUsRUFBRUMsV0FBVyxFQUFFLEdBQUdDLG1CQUFPQSxDQUFDO0FBRTVDLE1BQU1DLFlBQVlELG1CQUFPQSxDQUFDO0FBRTFCLE1BQU1FLFNBQVMsQ0FBQ0MsUUFBUSxFQUFFLEdBQUtGLFVBQVVHLE1BQU0sQ0FBQ0wsWUFBWUk7QUFFNURFLE9BQU9DLE9BQU8sR0FBRztJQUNmSjtJQUNBSyxPQUFPTDtJQUNQTSxPQUFPTjtJQUNQTyxjQUFjUDtJQUNkUSxlQUFlLENBQUNELGVBQ2RSLFVBQVVHLE1BQU0sQ0FBQ04sV0FBVyxVQUFVYSxNQUFNLENBQUNGLGNBQWNHLE1BQU07QUFDckUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJrZXRwbGFjZS8uL25vZGVfbW9kdWxlcy9vcGVuaWQtY2xpZW50L2xpYi9oZWxwZXJzL2dlbmVyYXRvcnMuanM/ODJmOCJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCB7IGNyZWF0ZUhhc2gsIHJhbmRvbUJ5dGVzIH0gPSByZXF1aXJlKCdjcnlwdG8nKTtcblxuY29uc3QgYmFzZTY0dXJsID0gcmVxdWlyZSgnLi9iYXNlNjR1cmwnKTtcblxuY29uc3QgcmFuZG9tID0gKGJ5dGVzID0gMzIpID0+IGJhc2U2NHVybC5lbmNvZGUocmFuZG9tQnl0ZXMoYnl0ZXMpKTtcblxubW9kdWxlLmV4cG9ydHMgPSB7XG4gIHJhbmRvbSxcbiAgc3RhdGU6IHJhbmRvbSxcbiAgbm9uY2U6IHJhbmRvbSxcbiAgY29kZVZlcmlmaWVyOiByYW5kb20sXG4gIGNvZGVDaGFsbGVuZ2U6IChjb2RlVmVyaWZpZXIpID0+XG4gICAgYmFzZTY0dXJsLmVuY29kZShjcmVhdGVIYXNoKCdzaGEyNTYnKS51cGRhdGUoY29kZVZlcmlmaWVyKS5kaWdlc3QoKSksXG59O1xuIl0sIm5hbWVzIjpbImNyZWF0ZUhhc2giLCJyYW5kb21CeXRlcyIsInJlcXVpcmUiLCJiYXNlNjR1cmwiLCJyYW5kb20iLCJieXRlcyIsImVuY29kZSIsIm1vZHVsZSIsImV4cG9ydHMiLCJzdGF0ZSIsIm5vbmNlIiwiY29kZVZlcmlmaWVyIiwiY29kZUNoYWxsZW5nZSIsInVwZGF0ZSIsImRpZ2VzdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/openid-client/lib/helpers/generators.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/openid-client/lib/helpers/is_key_object.js":
/*!*****************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/is_key_object.js ***!
  \*****************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst util = __webpack_require__(/*! util */ \"util\");\nconst crypto = __webpack_require__(/*! crypto */ \"crypto\");\nmodule.exports = util.types.isKeyObject || ((obj)=>obj && obj instanceof crypto.KeyObject);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9pc19rZXlfb2JqZWN0LmpzIiwibWFwcGluZ3MiOiI7QUFBQSxNQUFNQSxPQUFPQyxtQkFBT0EsQ0FBQztBQUNyQixNQUFNQyxTQUFTRCxtQkFBT0EsQ0FBQztBQUV2QkUsT0FBT0MsT0FBTyxHQUFHSixLQUFLSyxLQUFLLENBQUNDLFdBQVcsSUFBSyxFQUFDQyxNQUFRQSxPQUFPQSxlQUFlTCxPQUFPTSxTQUFRIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFya2V0cGxhY2UvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9pc19rZXlfb2JqZWN0LmpzP2EwMGIiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgdXRpbCA9IHJlcXVpcmUoJ3V0aWwnKTtcbmNvbnN0IGNyeXB0byA9IHJlcXVpcmUoJ2NyeXB0bycpO1xuXG5tb2R1bGUuZXhwb3J0cyA9IHV0aWwudHlwZXMuaXNLZXlPYmplY3QgfHwgKChvYmopID0+IG9iaiAmJiBvYmogaW5zdGFuY2VvZiBjcnlwdG8uS2V5T2JqZWN0KTtcbiJdLCJuYW1lcyI6WyJ1dGlsIiwicmVxdWlyZSIsImNyeXB0byIsIm1vZHVsZSIsImV4cG9ydHMiLCJ0eXBlcyIsImlzS2V5T2JqZWN0Iiwib2JqIiwiS2V5T2JqZWN0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/openid-client/lib/helpers/is_key_object.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/openid-client/lib/helpers/is_plain_object.js":
/*!*******************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/is_plain_object.js ***!
  \*******************************************************************/
/***/ ((module) => {

eval("\nmodule.exports = (a)=>!!a && a.constructor === Object;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9pc19wbGFpbl9vYmplY3QuanMiLCJtYXBwaW5ncyI6IjtBQUFBQSxPQUFPQyxPQUFPLEdBQUcsQ0FBQ0MsSUFBTSxDQUFDLENBQUNBLEtBQUtBLEVBQUVDLFdBQVcsS0FBS0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJrZXRwbGFjZS8uL25vZGVfbW9kdWxlcy9vcGVuaWQtY2xpZW50L2xpYi9oZWxwZXJzL2lzX3BsYWluX29iamVjdC5qcz9lM2U4Il0sInNvdXJjZXNDb250ZW50IjpbIm1vZHVsZS5leHBvcnRzID0gKGEpID0+ICEhYSAmJiBhLmNvbnN0cnVjdG9yID09PSBPYmplY3Q7XG4iXSwibmFtZXMiOlsibW9kdWxlIiwiZXhwb3J0cyIsImEiLCJjb25zdHJ1Y3RvciIsIk9iamVjdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/openid-client/lib/helpers/is_plain_object.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/openid-client/lib/helpers/issuer.js":
/*!**********************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/issuer.js ***!
  \**********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst objectHash = __webpack_require__(/*! object-hash */ \"(ssr)/./node_modules/openid-client/node_modules/object-hash/index.js\");\nconst LRU = __webpack_require__(/*! lru-cache */ \"(ssr)/./node_modules/openid-client/node_modules/lru-cache/index.js\");\nconst { RPError } = __webpack_require__(/*! ../errors */ \"(ssr)/./node_modules/openid-client/lib/errors.js\");\nconst { assertIssuerConfiguration } = __webpack_require__(/*! ./assert */ \"(ssr)/./node_modules/openid-client/lib/helpers/assert.js\");\nconst KeyStore = __webpack_require__(/*! ./keystore */ \"(ssr)/./node_modules/openid-client/lib/helpers/keystore.js\");\nconst { keystores } = __webpack_require__(/*! ./weak_cache */ \"(ssr)/./node_modules/openid-client/lib/helpers/weak_cache.js\");\nconst processResponse = __webpack_require__(/*! ./process_response */ \"(ssr)/./node_modules/openid-client/lib/helpers/process_response.js\");\nconst request = __webpack_require__(/*! ./request */ \"(ssr)/./node_modules/openid-client/lib/helpers/request.js\");\nconst inFlight = new WeakMap();\nconst caches = new WeakMap();\nconst lrus = (ctx)=>{\n    if (!caches.has(ctx)) {\n        caches.set(ctx, new LRU({\n            max: 100\n        }));\n    }\n    return caches.get(ctx);\n};\nasync function getKeyStore(reload = false) {\n    assertIssuerConfiguration(this, \"jwks_uri\");\n    const keystore = keystores.get(this);\n    const cache = lrus(this);\n    if (reload || !keystore) {\n        if (inFlight.has(this)) {\n            return inFlight.get(this);\n        }\n        cache.reset();\n        inFlight.set(this, (async ()=>{\n            const response = await request.call(this, {\n                method: \"GET\",\n                responseType: \"json\",\n                url: this.jwks_uri,\n                headers: {\n                    Accept: \"application/json, application/jwk-set+json\"\n                }\n            }).finally(()=>{\n                inFlight.delete(this);\n            });\n            const jwks = processResponse(response);\n            const joseKeyStore = KeyStore.fromJWKS(jwks, {\n                onlyPublic: true\n            });\n            cache.set(\"throttle\", true, 60 * 1000);\n            keystores.set(this, joseKeyStore);\n            return joseKeyStore;\n        })());\n        return inFlight.get(this);\n    }\n    return keystore;\n}\nasync function queryKeyStore({ kid, kty, alg, use }, { allowMulti = false } = {}) {\n    const cache = lrus(this);\n    const def = {\n        kid,\n        kty,\n        alg,\n        use\n    };\n    const defHash = objectHash(def, {\n        algorithm: \"sha256\",\n        ignoreUnknown: true,\n        unorderedArrays: true,\n        unorderedSets: true,\n        respectType: false\n    });\n    // refresh keystore on every unknown key but also only upto once every minute\n    const freshJwksUri = cache.get(defHash) || cache.get(\"throttle\");\n    const keystore = await getKeyStore.call(this, !freshJwksUri);\n    const keys = keystore.all(def);\n    delete def.use;\n    if (keys.length === 0) {\n        throw new RPError({\n            printf: [\n                \"no valid key found in issuer's jwks_uri for key parameters %j\",\n                def\n            ],\n            jwks: keystore\n        });\n    }\n    if (!allowMulti && keys.length > 1 && !kid) {\n        throw new RPError({\n            printf: [\n                \"multiple matching keys found in issuer's jwks_uri for key parameters %j, kid must be provided in this case\",\n                def\n            ],\n            jwks: keystore\n        });\n    }\n    cache.set(defHash, true);\n    return keys;\n}\nmodule.exports.queryKeyStore = queryKeyStore;\nmodule.exports.keystore = getKeyStore;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/openid-client/lib/helpers/issuer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/openid-client/lib/helpers/keystore.js":
/*!************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/keystore.js ***!
  \************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst jose = __webpack_require__(/*! jose */ \"(ssr)/./node_modules/jose/dist/node/cjs/index.js\");\nconst clone = __webpack_require__(/*! ./deep_clone */ \"(ssr)/./node_modules/openid-client/lib/helpers/deep_clone.js\");\nconst isPlainObject = __webpack_require__(/*! ./is_plain_object */ \"(ssr)/./node_modules/openid-client/lib/helpers/is_plain_object.js\");\nconst internal = Symbol();\nconst keyscore = (key, { alg, use })=>{\n    let score = 0;\n    if (alg && key.alg) {\n        score++;\n    }\n    if (use && key.use) {\n        score++;\n    }\n    return score;\n};\nfunction getKtyFromAlg(alg) {\n    switch(typeof alg === \"string\" && alg.slice(0, 2)){\n        case \"RS\":\n        case \"PS\":\n            return \"RSA\";\n        case \"ES\":\n            return \"EC\";\n        case \"Ed\":\n            return \"OKP\";\n        default:\n            return undefined;\n    }\n}\nfunction getAlgorithms(use, alg, kty, crv) {\n    // Ed25519, Ed448, and secp256k1 always have \"alg\"\n    // OKP always has \"use\"\n    if (alg) {\n        return new Set([\n            alg\n        ]);\n    }\n    switch(kty){\n        case \"EC\":\n            {\n                let algs = [];\n                if (use === \"enc\" || use === undefined) {\n                    algs = algs.concat([\n                        \"ECDH-ES\",\n                        \"ECDH-ES+A128KW\",\n                        \"ECDH-ES+A192KW\",\n                        \"ECDH-ES+A256KW\"\n                    ]);\n                }\n                if (use === \"sig\" || use === undefined) {\n                    switch(crv){\n                        case \"P-256\":\n                        case \"P-384\":\n                            algs = algs.concat([\n                                `ES${crv.slice(-3)}`\n                            ]);\n                            break;\n                        case \"P-521\":\n                            algs = algs.concat([\n                                \"ES512\"\n                            ]);\n                            break;\n                        case \"secp256k1\":\n                            if (jose.cryptoRuntime === \"node:crypto\") {\n                                algs = algs.concat([\n                                    \"ES256K\"\n                                ]);\n                            }\n                            break;\n                    }\n                }\n                return new Set(algs);\n            }\n        case \"OKP\":\n            {\n                return new Set([\n                    \"ECDH-ES\",\n                    \"ECDH-ES+A128KW\",\n                    \"ECDH-ES+A192KW\",\n                    \"ECDH-ES+A256KW\"\n                ]);\n            }\n        case \"RSA\":\n            {\n                let algs = [];\n                if (use === \"enc\" || use === undefined) {\n                    algs = algs.concat([\n                        \"RSA-OAEP\",\n                        \"RSA-OAEP-256\",\n                        \"RSA-OAEP-384\",\n                        \"RSA-OAEP-512\"\n                    ]);\n                    if (jose.cryptoRuntime === \"node:crypto\") {\n                        algs = algs.concat([\n                            \"RSA1_5\"\n                        ]);\n                    }\n                }\n                if (use === \"sig\" || use === undefined) {\n                    algs = algs.concat([\n                        \"PS256\",\n                        \"PS384\",\n                        \"PS512\",\n                        \"RS256\",\n                        \"RS384\",\n                        \"RS512\"\n                    ]);\n                }\n                return new Set(algs);\n            }\n        default:\n            throw new Error(\"unreachable\");\n    }\n}\nmodule.exports = class KeyStore {\n    #keys;\n    constructor(i, keys){\n        if (i !== internal) throw new Error(\"invalid constructor call\");\n        this.#keys = keys;\n    }\n    toJWKS() {\n        return {\n            keys: this.map(({ jwk: { d, p, q, dp, dq, qi, ...jwk } })=>jwk)\n        };\n    }\n    all({ alg, kid, use } = {}) {\n        if (!use || !alg) {\n            throw new Error();\n        }\n        const kty = getKtyFromAlg(alg);\n        const search = {\n            alg,\n            use\n        };\n        return this.filter((key)=>{\n            let candidate = true;\n            if (candidate && kty !== undefined && key.jwk.kty !== kty) {\n                candidate = false;\n            }\n            if (candidate && kid !== undefined && key.jwk.kid !== kid) {\n                candidate = false;\n            }\n            if (candidate && use !== undefined && key.jwk.use !== undefined && key.jwk.use !== use) {\n                candidate = false;\n            }\n            if (candidate && key.jwk.alg && key.jwk.alg !== alg) {\n                candidate = false;\n            } else if (!key.algorithms.has(alg)) {\n                candidate = false;\n            }\n            return candidate;\n        }).sort((first, second)=>keyscore(second, search) - keyscore(first, search));\n    }\n    get(...args) {\n        return this.all(...args)[0];\n    }\n    static async fromJWKS(jwks, { onlyPublic = false, onlyPrivate = false } = {}) {\n        if (!isPlainObject(jwks) || !Array.isArray(jwks.keys) || jwks.keys.some((k)=>!isPlainObject(k) || !(\"kty\" in k))) {\n            throw new TypeError(\"jwks must be a JSON Web Key Set formatted object\");\n        }\n        const keys = [];\n        for (let jwk of jwks.keys){\n            jwk = clone(jwk);\n            const { kty, kid, crv } = jwk;\n            let { alg, use } = jwk;\n            if (typeof kty !== \"string\" || !kty) {\n                continue;\n            }\n            if (use !== undefined && use !== \"sig\" && use !== \"enc\") {\n                continue;\n            }\n            if (typeof alg !== \"string\" && alg !== undefined) {\n                continue;\n            }\n            if (typeof kid !== \"string\" && kid !== undefined) {\n                continue;\n            }\n            if (kty === \"EC\" && use === \"sig\") {\n                switch(crv){\n                    case \"P-256\":\n                        alg = \"ES256\";\n                        break;\n                    case \"P-384\":\n                        alg = \"ES384\";\n                        break;\n                    case \"P-521\":\n                        alg = \"ES512\";\n                        break;\n                    default:\n                        break;\n                }\n            }\n            if (crv === \"secp256k1\") {\n                use = \"sig\";\n                alg = \"ES256K\";\n            }\n            if (kty === \"OKP\") {\n                switch(crv){\n                    case \"Ed25519\":\n                    case \"Ed448\":\n                        use = \"sig\";\n                        alg = \"EdDSA\";\n                        break;\n                    case \"X25519\":\n                    case \"X448\":\n                        use = \"enc\";\n                        break;\n                    default:\n                        break;\n                }\n            }\n            if (alg && !use) {\n                switch(true){\n                    case alg.startsWith(\"ECDH\"):\n                        use = \"enc\";\n                        break;\n                    case alg.startsWith(\"RSA\"):\n                        use = \"enc\";\n                        break;\n                    default:\n                        break;\n                }\n            }\n            if (onlyPrivate && (jwk.kty === \"oct\" || !jwk.d)) {\n                throw new Error(\"jwks must only contain private keys\");\n            }\n            if (onlyPublic && (jwk.d || jwk.k)) {\n                continue;\n            }\n            keys.push({\n                jwk: {\n                    ...jwk,\n                    alg,\n                    use\n                },\n                async keyObject (alg) {\n                    if (this[alg]) {\n                        return this[alg];\n                    }\n                    const keyObject = await jose.importJWK(this.jwk, alg);\n                    this[alg] = keyObject;\n                    return keyObject;\n                },\n                get algorithms () {\n                    Object.defineProperty(this, \"algorithms\", {\n                        value: getAlgorithms(this.jwk.use, this.jwk.alg, this.jwk.kty, this.jwk.crv),\n                        enumerable: true,\n                        configurable: false\n                    });\n                    return this.algorithms;\n                }\n            });\n        }\n        return new this(internal, keys);\n    }\n    filter(...args) {\n        return this.#keys.filter(...args);\n    }\n    find(...args) {\n        return this.#keys.find(...args);\n    }\n    every(...args) {\n        return this.#keys.every(...args);\n    }\n    some(...args) {\n        return this.#keys.some(...args);\n    }\n    map(...args) {\n        return this.#keys.map(...args);\n    }\n    forEach(...args) {\n        return this.#keys.forEach(...args);\n    }\n    reduce(...args) {\n        return this.#keys.reduce(...args);\n    }\n    sort(...args) {\n        return this.#keys.sort(...args);\n    }\n    *[Symbol.iterator]() {\n        for (const key of this.#keys){\n            yield key;\n        }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9rZXlzdG9yZS5qcyIsIm1hcHBpbmdzIjoiO0FBQUEsTUFBTUEsT0FBT0MsbUJBQU9BLENBQUM7QUFFckIsTUFBTUMsUUFBUUQsbUJBQU9BLENBQUM7QUFDdEIsTUFBTUUsZ0JBQWdCRixtQkFBT0EsQ0FBQztBQUU5QixNQUFNRyxXQUFXQztBQUVqQixNQUFNQyxXQUFXLENBQUNDLEtBQUssRUFBRUMsR0FBRyxFQUFFQyxHQUFHLEVBQUU7SUFDakMsSUFBSUMsUUFBUTtJQUVaLElBQUlGLE9BQU9ELElBQUlDLEdBQUcsRUFBRTtRQUNsQkU7SUFDRjtJQUVBLElBQUlELE9BQU9GLElBQUlFLEdBQUcsRUFBRTtRQUNsQkM7SUFDRjtJQUVBLE9BQU9BO0FBQ1Q7QUFFQSxTQUFTQyxjQUFjSCxHQUFHO0lBQ3hCLE9BQVEsT0FBT0EsUUFBUSxZQUFZQSxJQUFJSSxLQUFLLENBQUMsR0FBRztRQUM5QyxLQUFLO1FBQ0wsS0FBSztZQUNILE9BQU87UUFDVCxLQUFLO1lBQ0gsT0FBTztRQUNULEtBQUs7WUFDSCxPQUFPO1FBQ1Q7WUFDRSxPQUFPQztJQUNYO0FBQ0Y7QUFFQSxTQUFTQyxjQUFjTCxHQUFHLEVBQUVELEdBQUcsRUFBRU8sR0FBRyxFQUFFQyxHQUFHO0lBQ3ZDLGtEQUFrRDtJQUNsRCx1QkFBdUI7SUFDdkIsSUFBSVIsS0FBSztRQUNQLE9BQU8sSUFBSVMsSUFBSTtZQUFDVDtTQUFJO0lBQ3RCO0lBRUEsT0FBUU87UUFDTixLQUFLO1lBQU07Z0JBQ1QsSUFBSUcsT0FBTyxFQUFFO2dCQUViLElBQUlULFFBQVEsU0FBU0EsUUFBUUksV0FBVztvQkFDdENLLE9BQU9BLEtBQUtDLE1BQU0sQ0FBQzt3QkFBQzt3QkFBVzt3QkFBa0I7d0JBQWtCO3FCQUFpQjtnQkFDdEY7Z0JBRUEsSUFBSVYsUUFBUSxTQUFTQSxRQUFRSSxXQUFXO29CQUN0QyxPQUFRRzt3QkFDTixLQUFLO3dCQUNMLEtBQUs7NEJBQ0hFLE9BQU9BLEtBQUtDLE1BQU0sQ0FBQztnQ0FBQyxDQUFDLEVBQUUsRUFBRUgsSUFBSUosS0FBSyxDQUFDLENBQUMsR0FBRyxDQUFDOzZCQUFDOzRCQUN6Qzt3QkFDRixLQUFLOzRCQUNITSxPQUFPQSxLQUFLQyxNQUFNLENBQUM7Z0NBQUM7NkJBQVE7NEJBQzVCO3dCQUNGLEtBQUs7NEJBQ0gsSUFBSW5CLEtBQUtvQixhQUFhLEtBQUssZUFBZTtnQ0FDeENGLE9BQU9BLEtBQUtDLE1BQU0sQ0FBQztvQ0FBQztpQ0FBUzs0QkFDL0I7NEJBQ0E7b0JBQ0o7Z0JBQ0Y7Z0JBRUEsT0FBTyxJQUFJRixJQUFJQztZQUNqQjtRQUNBLEtBQUs7WUFBTztnQkFDVixPQUFPLElBQUlELElBQUk7b0JBQUM7b0JBQVc7b0JBQWtCO29CQUFrQjtpQkFBaUI7WUFDbEY7UUFDQSxLQUFLO1lBQU87Z0JBQ1YsSUFBSUMsT0FBTyxFQUFFO2dCQUViLElBQUlULFFBQVEsU0FBU0EsUUFBUUksV0FBVztvQkFDdENLLE9BQU9BLEtBQUtDLE1BQU0sQ0FBQzt3QkFBQzt3QkFBWTt3QkFBZ0I7d0JBQWdCO3FCQUFlO29CQUMvRSxJQUFJbkIsS0FBS29CLGFBQWEsS0FBSyxlQUFlO3dCQUN4Q0YsT0FBT0EsS0FBS0MsTUFBTSxDQUFDOzRCQUFDO3lCQUFTO29CQUMvQjtnQkFDRjtnQkFFQSxJQUFJVixRQUFRLFNBQVNBLFFBQVFJLFdBQVc7b0JBQ3RDSyxPQUFPQSxLQUFLQyxNQUFNLENBQUM7d0JBQUM7d0JBQVM7d0JBQVM7d0JBQVM7d0JBQVM7d0JBQVM7cUJBQVE7Z0JBQzNFO2dCQUVBLE9BQU8sSUFBSUYsSUFBSUM7WUFDakI7UUFDQTtZQUNFLE1BQU0sSUFBSUcsTUFBTTtJQUNwQjtBQUNGO0FBRUFDLE9BQU9DLE9BQU8sR0FBRyxNQUFNQztJQUNyQixDQUFDQyxJQUFJLENBQUM7SUFFTkMsWUFBWUMsQ0FBQyxFQUFFRixJQUFJLENBQUU7UUFDbkIsSUFBSUUsTUFBTXZCLFVBQVUsTUFBTSxJQUFJaUIsTUFBTTtRQUNwQyxJQUFJLENBQUMsQ0FBQ0ksSUFBSSxHQUFHQTtJQUNmO0lBRUFHLFNBQVM7UUFDUCxPQUFPO1lBQ0xILE1BQU0sSUFBSSxDQUFDSSxHQUFHLENBQUMsQ0FBQyxFQUFFQyxLQUFLLEVBQUVDLENBQUMsRUFBRUMsQ0FBQyxFQUFFQyxDQUFDLEVBQUVDLEVBQUUsRUFBRUMsRUFBRSxFQUFFQyxFQUFFLEVBQUUsR0FBR04sS0FBSyxFQUFFLEdBQUtBO1FBQy9EO0lBQ0Y7SUFFQU8sSUFBSSxFQUFFN0IsR0FBRyxFQUFFOEIsR0FBRyxFQUFFN0IsR0FBRyxFQUFFLEdBQUcsQ0FBQyxDQUFDLEVBQUU7UUFDMUIsSUFBSSxDQUFDQSxPQUFPLENBQUNELEtBQUs7WUFDaEIsTUFBTSxJQUFJYTtRQUNaO1FBRUEsTUFBTU4sTUFBTUosY0FBY0g7UUFFMUIsTUFBTStCLFNBQVM7WUFBRS9CO1lBQUtDO1FBQUk7UUFDMUIsT0FBTyxJQUFJLENBQUMrQixNQUFNLENBQUMsQ0FBQ2pDO1lBQ2xCLElBQUlrQyxZQUFZO1lBRWhCLElBQUlBLGFBQWExQixRQUFRRixhQUFhTixJQUFJdUIsR0FBRyxDQUFDZixHQUFHLEtBQUtBLEtBQUs7Z0JBQ3pEMEIsWUFBWTtZQUNkO1lBRUEsSUFBSUEsYUFBYUgsUUFBUXpCLGFBQWFOLElBQUl1QixHQUFHLENBQUNRLEdBQUcsS0FBS0EsS0FBSztnQkFDekRHLFlBQVk7WUFDZDtZQUVBLElBQUlBLGFBQWFoQyxRQUFRSSxhQUFhTixJQUFJdUIsR0FBRyxDQUFDckIsR0FBRyxLQUFLSSxhQUFhTixJQUFJdUIsR0FBRyxDQUFDckIsR0FBRyxLQUFLQSxLQUFLO2dCQUN0RmdDLFlBQVk7WUFDZDtZQUVBLElBQUlBLGFBQWFsQyxJQUFJdUIsR0FBRyxDQUFDdEIsR0FBRyxJQUFJRCxJQUFJdUIsR0FBRyxDQUFDdEIsR0FBRyxLQUFLQSxLQUFLO2dCQUNuRGlDLFlBQVk7WUFDZCxPQUFPLElBQUksQ0FBQ2xDLElBQUltQyxVQUFVLENBQUNDLEdBQUcsQ0FBQ25DLE1BQU07Z0JBQ25DaUMsWUFBWTtZQUNkO1lBRUEsT0FBT0E7UUFDVCxHQUFHRyxJQUFJLENBQUMsQ0FBQ0MsT0FBT0MsU0FBV3hDLFNBQVN3QyxRQUFRUCxVQUFVakMsU0FBU3VDLE9BQU9OO0lBQ3hFO0lBRUFRLElBQUksR0FBR0MsSUFBSSxFQUFFO1FBQ1gsT0FBTyxJQUFJLENBQUNYLEdBQUcsSUFBSVcsS0FBSyxDQUFDLEVBQUU7SUFDN0I7SUFFQSxhQUFhQyxTQUFTQyxJQUFJLEVBQUUsRUFBRUMsYUFBYSxLQUFLLEVBQUVDLGNBQWMsS0FBSyxFQUFFLEdBQUcsQ0FBQyxDQUFDLEVBQUU7UUFDNUUsSUFDRSxDQUFDakQsY0FBYytDLFNBQ2YsQ0FBQ0csTUFBTUMsT0FBTyxDQUFDSixLQUFLekIsSUFBSSxLQUN4QnlCLEtBQUt6QixJQUFJLENBQUM4QixJQUFJLENBQUMsQ0FBQ0MsSUFBTSxDQUFDckQsY0FBY3FELE1BQU0sQ0FBRSxVQUFTQSxDQUFBQSxJQUN0RDtZQUNBLE1BQU0sSUFBSUMsVUFBVTtRQUN0QjtRQUVBLE1BQU1oQyxPQUFPLEVBQUU7UUFFZixLQUFLLElBQUlLLE9BQU9vQixLQUFLekIsSUFBSSxDQUFFO1lBQ3pCSyxNQUFNNUIsTUFBTTRCO1lBQ1osTUFBTSxFQUFFZixHQUFHLEVBQUV1QixHQUFHLEVBQUV0QixHQUFHLEVBQUUsR0FBR2M7WUFFMUIsSUFBSSxFQUFFdEIsR0FBRyxFQUFFQyxHQUFHLEVBQUUsR0FBR3FCO1lBRW5CLElBQUksT0FBT2YsUUFBUSxZQUFZLENBQUNBLEtBQUs7Z0JBQ25DO1lBQ0Y7WUFFQSxJQUFJTixRQUFRSSxhQUFhSixRQUFRLFNBQVNBLFFBQVEsT0FBTztnQkFDdkQ7WUFDRjtZQUVBLElBQUksT0FBT0QsUUFBUSxZQUFZQSxRQUFRSyxXQUFXO2dCQUNoRDtZQUNGO1lBRUEsSUFBSSxPQUFPeUIsUUFBUSxZQUFZQSxRQUFRekIsV0FBVztnQkFDaEQ7WUFDRjtZQUVBLElBQUlFLFFBQVEsUUFBUU4sUUFBUSxPQUFPO2dCQUNqQyxPQUFRTztvQkFDTixLQUFLO3dCQUNIUixNQUFNO3dCQUNOO29CQUNGLEtBQUs7d0JBQ0hBLE1BQU07d0JBQ047b0JBQ0YsS0FBSzt3QkFDSEEsTUFBTTt3QkFDTjtvQkFDRjt3QkFDRTtnQkFDSjtZQUNGO1lBRUEsSUFBSVEsUUFBUSxhQUFhO2dCQUN2QlAsTUFBTTtnQkFDTkQsTUFBTTtZQUNSO1lBRUEsSUFBSU8sUUFBUSxPQUFPO2dCQUNqQixPQUFRQztvQkFDTixLQUFLO29CQUNMLEtBQUs7d0JBQ0hQLE1BQU07d0JBQ05ELE1BQU07d0JBQ047b0JBQ0YsS0FBSztvQkFDTCxLQUFLO3dCQUNIQyxNQUFNO3dCQUNOO29CQUNGO3dCQUNFO2dCQUNKO1lBQ0Y7WUFFQSxJQUFJRCxPQUFPLENBQUNDLEtBQUs7Z0JBQ2YsT0FBUTtvQkFDTixLQUFLRCxJQUFJa0QsVUFBVSxDQUFDO3dCQUNsQmpELE1BQU07d0JBQ047b0JBQ0YsS0FBS0QsSUFBSWtELFVBQVUsQ0FBQzt3QkFDbEJqRCxNQUFNO3dCQUNOO29CQUNGO3dCQUNFO2dCQUNKO1lBQ0Y7WUFFQSxJQUFJMkMsZUFBZ0J0QixDQUFBQSxJQUFJZixHQUFHLEtBQUssU0FBUyxDQUFDZSxJQUFJQyxDQUFDLEdBQUc7Z0JBQ2hELE1BQU0sSUFBSVYsTUFBTTtZQUNsQjtZQUVBLElBQUk4QixjQUFlckIsQ0FBQUEsSUFBSUMsQ0FBQyxJQUFJRCxJQUFJMEIsQ0FBQyxHQUFHO2dCQUNsQztZQUNGO1lBRUEvQixLQUFLa0MsSUFBSSxDQUFDO2dCQUNSN0IsS0FBSztvQkFBRSxHQUFHQSxHQUFHO29CQUFFdEI7b0JBQUtDO2dCQUFJO2dCQUN4QixNQUFNbUQsV0FBVXBELEdBQUc7b0JBQ2pCLElBQUksSUFBSSxDQUFDQSxJQUFJLEVBQUU7d0JBQ2IsT0FBTyxJQUFJLENBQUNBLElBQUk7b0JBQ2xCO29CQUVBLE1BQU1vRCxZQUFZLE1BQU01RCxLQUFLNkQsU0FBUyxDQUFDLElBQUksQ0FBQy9CLEdBQUcsRUFBRXRCO29CQUNqRCxJQUFJLENBQUNBLElBQUksR0FBR29EO29CQUNaLE9BQU9BO2dCQUNUO2dCQUNBLElBQUlsQixjQUFhO29CQUNmb0IsT0FBT0MsY0FBYyxDQUFDLElBQUksRUFBRSxjQUFjO3dCQUN4Q0MsT0FBT2xELGNBQWMsSUFBSSxDQUFDZ0IsR0FBRyxDQUFDckIsR0FBRyxFQUFFLElBQUksQ0FBQ3FCLEdBQUcsQ0FBQ3RCLEdBQUcsRUFBRSxJQUFJLENBQUNzQixHQUFHLENBQUNmLEdBQUcsRUFBRSxJQUFJLENBQUNlLEdBQUcsQ0FBQ2QsR0FBRzt3QkFDM0VpRCxZQUFZO3dCQUNaQyxjQUFjO29CQUNoQjtvQkFDQSxPQUFPLElBQUksQ0FBQ3hCLFVBQVU7Z0JBQ3hCO1lBQ0Y7UUFDRjtRQUVBLE9BQU8sSUFBSSxJQUFJLENBQUN0QyxVQUFVcUI7SUFDNUI7SUFFQWUsT0FBTyxHQUFHUSxJQUFJLEVBQUU7UUFDZCxPQUFPLElBQUksQ0FBQyxDQUFDdkIsSUFBSSxDQUFDZSxNQUFNLElBQUlRO0lBQzlCO0lBRUFtQixLQUFLLEdBQUduQixJQUFJLEVBQUU7UUFDWixPQUFPLElBQUksQ0FBQyxDQUFDdkIsSUFBSSxDQUFDMEMsSUFBSSxJQUFJbkI7SUFDNUI7SUFFQW9CLE1BQU0sR0FBR3BCLElBQUksRUFBRTtRQUNiLE9BQU8sSUFBSSxDQUFDLENBQUN2QixJQUFJLENBQUMyQyxLQUFLLElBQUlwQjtJQUM3QjtJQUVBTyxLQUFLLEdBQUdQLElBQUksRUFBRTtRQUNaLE9BQU8sSUFBSSxDQUFDLENBQUN2QixJQUFJLENBQUM4QixJQUFJLElBQUlQO0lBQzVCO0lBRUFuQixJQUFJLEdBQUdtQixJQUFJLEVBQUU7UUFDWCxPQUFPLElBQUksQ0FBQyxDQUFDdkIsSUFBSSxDQUFDSSxHQUFHLElBQUltQjtJQUMzQjtJQUVBcUIsUUFBUSxHQUFHckIsSUFBSSxFQUFFO1FBQ2YsT0FBTyxJQUFJLENBQUMsQ0FBQ3ZCLElBQUksQ0FBQzRDLE9BQU8sSUFBSXJCO0lBQy9CO0lBRUFzQixPQUFPLEdBQUd0QixJQUFJLEVBQUU7UUFDZCxPQUFPLElBQUksQ0FBQyxDQUFDdkIsSUFBSSxDQUFDNkMsTUFBTSxJQUFJdEI7SUFDOUI7SUFFQUosS0FBSyxHQUFHSSxJQUFJLEVBQUU7UUFDWixPQUFPLElBQUksQ0FBQyxDQUFDdkIsSUFBSSxDQUFDbUIsSUFBSSxJQUFJSTtJQUM1QjtJQUVBLENBQUMsQ0FBQzNDLE9BQU9rRSxRQUFRLENBQUMsR0FBRztRQUNuQixLQUFLLE1BQU1oRSxPQUFPLElBQUksQ0FBQyxDQUFDa0IsSUFBSSxDQUFFO1lBQzVCLE1BQU1sQjtRQUNSO0lBQ0Y7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL21hcmtldHBsYWNlLy4vbm9kZV9tb2R1bGVzL29wZW5pZC1jbGllbnQvbGliL2hlbHBlcnMva2V5c3RvcmUuanM/OGY4YiJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBqb3NlID0gcmVxdWlyZSgnam9zZScpO1xuXG5jb25zdCBjbG9uZSA9IHJlcXVpcmUoJy4vZGVlcF9jbG9uZScpO1xuY29uc3QgaXNQbGFpbk9iamVjdCA9IHJlcXVpcmUoJy4vaXNfcGxhaW5fb2JqZWN0Jyk7XG5cbmNvbnN0IGludGVybmFsID0gU3ltYm9sKCk7XG5cbmNvbnN0IGtleXNjb3JlID0gKGtleSwgeyBhbGcsIHVzZSB9KSA9PiB7XG4gIGxldCBzY29yZSA9IDA7XG5cbiAgaWYgKGFsZyAmJiBrZXkuYWxnKSB7XG4gICAgc2NvcmUrKztcbiAgfVxuXG4gIGlmICh1c2UgJiYga2V5LnVzZSkge1xuICAgIHNjb3JlKys7XG4gIH1cblxuICByZXR1cm4gc2NvcmU7XG59O1xuXG5mdW5jdGlvbiBnZXRLdHlGcm9tQWxnKGFsZykge1xuICBzd2l0Y2ggKHR5cGVvZiBhbGcgPT09ICdzdHJpbmcnICYmIGFsZy5zbGljZSgwLCAyKSkge1xuICAgIGNhc2UgJ1JTJzpcbiAgICBjYXNlICdQUyc6XG4gICAgICByZXR1cm4gJ1JTQSc7XG4gICAgY2FzZSAnRVMnOlxuICAgICAgcmV0dXJuICdFQyc7XG4gICAgY2FzZSAnRWQnOlxuICAgICAgcmV0dXJuICdPS1AnO1xuICAgIGRlZmF1bHQ6XG4gICAgICByZXR1cm4gdW5kZWZpbmVkO1xuICB9XG59XG5cbmZ1bmN0aW9uIGdldEFsZ29yaXRobXModXNlLCBhbGcsIGt0eSwgY3J2KSB7XG4gIC8vIEVkMjU1MTksIEVkNDQ4LCBhbmQgc2VjcDI1NmsxIGFsd2F5cyBoYXZlIFwiYWxnXCJcbiAgLy8gT0tQIGFsd2F5cyBoYXMgXCJ1c2VcIlxuICBpZiAoYWxnKSB7XG4gICAgcmV0dXJuIG5ldyBTZXQoW2FsZ10pO1xuICB9XG5cbiAgc3dpdGNoIChrdHkpIHtcbiAgICBjYXNlICdFQyc6IHtcbiAgICAgIGxldCBhbGdzID0gW107XG5cbiAgICAgIGlmICh1c2UgPT09ICdlbmMnIHx8IHVzZSA9PT0gdW5kZWZpbmVkKSB7XG4gICAgICAgIGFsZ3MgPSBhbGdzLmNvbmNhdChbJ0VDREgtRVMnLCAnRUNESC1FUytBMTI4S1cnLCAnRUNESC1FUytBMTkyS1cnLCAnRUNESC1FUytBMjU2S1cnXSk7XG4gICAgICB9XG5cbiAgICAgIGlmICh1c2UgPT09ICdzaWcnIHx8IHVzZSA9PT0gdW5kZWZpbmVkKSB7XG4gICAgICAgIHN3aXRjaCAoY3J2KSB7XG4gICAgICAgICAgY2FzZSAnUC0yNTYnOlxuICAgICAgICAgIGNhc2UgJ1AtMzg0JzpcbiAgICAgICAgICAgIGFsZ3MgPSBhbGdzLmNvbmNhdChbYEVTJHtjcnYuc2xpY2UoLTMpfWBdKTtcbiAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgIGNhc2UgJ1AtNTIxJzpcbiAgICAgICAgICAgIGFsZ3MgPSBhbGdzLmNvbmNhdChbJ0VTNTEyJ10pO1xuICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgY2FzZSAnc2VjcDI1NmsxJzpcbiAgICAgICAgICAgIGlmIChqb3NlLmNyeXB0b1J1bnRpbWUgPT09ICdub2RlOmNyeXB0bycpIHtcbiAgICAgICAgICAgICAgYWxncyA9IGFsZ3MuY29uY2F0KFsnRVMyNTZLJ10pO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgcmV0dXJuIG5ldyBTZXQoYWxncyk7XG4gICAgfVxuICAgIGNhc2UgJ09LUCc6IHtcbiAgICAgIHJldHVybiBuZXcgU2V0KFsnRUNESC1FUycsICdFQ0RILUVTK0ExMjhLVycsICdFQ0RILUVTK0ExOTJLVycsICdFQ0RILUVTK0EyNTZLVyddKTtcbiAgICB9XG4gICAgY2FzZSAnUlNBJzoge1xuICAgICAgbGV0IGFsZ3MgPSBbXTtcblxuICAgICAgaWYgKHVzZSA9PT0gJ2VuYycgfHwgdXNlID09PSB1bmRlZmluZWQpIHtcbiAgICAgICAgYWxncyA9IGFsZ3MuY29uY2F0KFsnUlNBLU9BRVAnLCAnUlNBLU9BRVAtMjU2JywgJ1JTQS1PQUVQLTM4NCcsICdSU0EtT0FFUC01MTInXSk7XG4gICAgICAgIGlmIChqb3NlLmNyeXB0b1J1bnRpbWUgPT09ICdub2RlOmNyeXB0bycpIHtcbiAgICAgICAgICBhbGdzID0gYWxncy5jb25jYXQoWydSU0ExXzUnXSk7XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgaWYgKHVzZSA9PT0gJ3NpZycgfHwgdXNlID09PSB1bmRlZmluZWQpIHtcbiAgICAgICAgYWxncyA9IGFsZ3MuY29uY2F0KFsnUFMyNTYnLCAnUFMzODQnLCAnUFM1MTInLCAnUlMyNTYnLCAnUlMzODQnLCAnUlM1MTInXSk7XG4gICAgICB9XG5cbiAgICAgIHJldHVybiBuZXcgU2V0KGFsZ3MpO1xuICAgIH1cbiAgICBkZWZhdWx0OlxuICAgICAgdGhyb3cgbmV3IEVycm9yKCd1bnJlYWNoYWJsZScpO1xuICB9XG59XG5cbm1vZHVsZS5leHBvcnRzID0gY2xhc3MgS2V5U3RvcmUge1xuICAja2V5cztcblxuICBjb25zdHJ1Y3RvcihpLCBrZXlzKSB7XG4gICAgaWYgKGkgIT09IGludGVybmFsKSB0aHJvdyBuZXcgRXJyb3IoJ2ludmFsaWQgY29uc3RydWN0b3IgY2FsbCcpO1xuICAgIHRoaXMuI2tleXMgPSBrZXlzO1xuICB9XG5cbiAgdG9KV0tTKCkge1xuICAgIHJldHVybiB7XG4gICAgICBrZXlzOiB0aGlzLm1hcCgoeyBqd2s6IHsgZCwgcCwgcSwgZHAsIGRxLCBxaSwgLi4uandrIH0gfSkgPT4gandrKSxcbiAgICB9O1xuICB9XG5cbiAgYWxsKHsgYWxnLCBraWQsIHVzZSB9ID0ge30pIHtcbiAgICBpZiAoIXVzZSB8fCAhYWxnKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoKTtcbiAgICB9XG5cbiAgICBjb25zdCBrdHkgPSBnZXRLdHlGcm9tQWxnKGFsZyk7XG5cbiAgICBjb25zdCBzZWFyY2ggPSB7IGFsZywgdXNlIH07XG4gICAgcmV0dXJuIHRoaXMuZmlsdGVyKChrZXkpID0+IHtcbiAgICAgIGxldCBjYW5kaWRhdGUgPSB0cnVlO1xuXG4gICAgICBpZiAoY2FuZGlkYXRlICYmIGt0eSAhPT0gdW5kZWZpbmVkICYmIGtleS5qd2sua3R5ICE9PSBrdHkpIHtcbiAgICAgICAgY2FuZGlkYXRlID0gZmFsc2U7XG4gICAgICB9XG5cbiAgICAgIGlmIChjYW5kaWRhdGUgJiYga2lkICE9PSB1bmRlZmluZWQgJiYga2V5Lmp3ay5raWQgIT09IGtpZCkge1xuICAgICAgICBjYW5kaWRhdGUgPSBmYWxzZTtcbiAgICAgIH1cblxuICAgICAgaWYgKGNhbmRpZGF0ZSAmJiB1c2UgIT09IHVuZGVmaW5lZCAmJiBrZXkuandrLnVzZSAhPT0gdW5kZWZpbmVkICYmIGtleS5qd2sudXNlICE9PSB1c2UpIHtcbiAgICAgICAgY2FuZGlkYXRlID0gZmFsc2U7XG4gICAgICB9XG5cbiAgICAgIGlmIChjYW5kaWRhdGUgJiYga2V5Lmp3ay5hbGcgJiYga2V5Lmp3ay5hbGcgIT09IGFsZykge1xuICAgICAgICBjYW5kaWRhdGUgPSBmYWxzZTtcbiAgICAgIH0gZWxzZSBpZiAoIWtleS5hbGdvcml0aG1zLmhhcyhhbGcpKSB7XG4gICAgICAgIGNhbmRpZGF0ZSA9IGZhbHNlO1xuICAgICAgfVxuXG4gICAgICByZXR1cm4gY2FuZGlkYXRlO1xuICAgIH0pLnNvcnQoKGZpcnN0LCBzZWNvbmQpID0+IGtleXNjb3JlKHNlY29uZCwgc2VhcmNoKSAtIGtleXNjb3JlKGZpcnN0LCBzZWFyY2gpKTtcbiAgfVxuXG4gIGdldCguLi5hcmdzKSB7XG4gICAgcmV0dXJuIHRoaXMuYWxsKC4uLmFyZ3MpWzBdO1xuICB9XG5cbiAgc3RhdGljIGFzeW5jIGZyb21KV0tTKGp3a3MsIHsgb25seVB1YmxpYyA9IGZhbHNlLCBvbmx5UHJpdmF0ZSA9IGZhbHNlIH0gPSB7fSkge1xuICAgIGlmIChcbiAgICAgICFpc1BsYWluT2JqZWN0KGp3a3MpIHx8XG4gICAgICAhQXJyYXkuaXNBcnJheShqd2tzLmtleXMpIHx8XG4gICAgICBqd2tzLmtleXMuc29tZSgoaykgPT4gIWlzUGxhaW5PYmplY3QoaykgfHwgISgna3R5JyBpbiBrKSlcbiAgICApIHtcbiAgICAgIHRocm93IG5ldyBUeXBlRXJyb3IoJ2p3a3MgbXVzdCBiZSBhIEpTT04gV2ViIEtleSBTZXQgZm9ybWF0dGVkIG9iamVjdCcpO1xuICAgIH1cblxuICAgIGNvbnN0IGtleXMgPSBbXTtcblxuICAgIGZvciAobGV0IGp3ayBvZiBqd2tzLmtleXMpIHtcbiAgICAgIGp3ayA9IGNsb25lKGp3ayk7XG4gICAgICBjb25zdCB7IGt0eSwga2lkLCBjcnYgfSA9IGp3aztcblxuICAgICAgbGV0IHsgYWxnLCB1c2UgfSA9IGp3aztcblxuICAgICAgaWYgKHR5cGVvZiBrdHkgIT09ICdzdHJpbmcnIHx8ICFrdHkpIHtcbiAgICAgICAgY29udGludWU7XG4gICAgICB9XG5cbiAgICAgIGlmICh1c2UgIT09IHVuZGVmaW5lZCAmJiB1c2UgIT09ICdzaWcnICYmIHVzZSAhPT0gJ2VuYycpIHtcbiAgICAgICAgY29udGludWU7XG4gICAgICB9XG5cbiAgICAgIGlmICh0eXBlb2YgYWxnICE9PSAnc3RyaW5nJyAmJiBhbGcgIT09IHVuZGVmaW5lZCkge1xuICAgICAgICBjb250aW51ZTtcbiAgICAgIH1cblxuICAgICAgaWYgKHR5cGVvZiBraWQgIT09ICdzdHJpbmcnICYmIGtpZCAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgIGNvbnRpbnVlO1xuICAgICAgfVxuXG4gICAgICBpZiAoa3R5ID09PSAnRUMnICYmIHVzZSA9PT0gJ3NpZycpIHtcbiAgICAgICAgc3dpdGNoIChjcnYpIHtcbiAgICAgICAgICBjYXNlICdQLTI1Nic6XG4gICAgICAgICAgICBhbGcgPSAnRVMyNTYnO1xuICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgY2FzZSAnUC0zODQnOlxuICAgICAgICAgICAgYWxnID0gJ0VTMzg0JztcbiAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgIGNhc2UgJ1AtNTIxJzpcbiAgICAgICAgICAgIGFsZyA9ICdFUzUxMic7XG4gICAgICAgICAgICBicmVhaztcbiAgICAgICAgICBkZWZhdWx0OlxuICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgaWYgKGNydiA9PT0gJ3NlY3AyNTZrMScpIHtcbiAgICAgICAgdXNlID0gJ3NpZyc7XG4gICAgICAgIGFsZyA9ICdFUzI1NksnO1xuICAgICAgfVxuXG4gICAgICBpZiAoa3R5ID09PSAnT0tQJykge1xuICAgICAgICBzd2l0Y2ggKGNydikge1xuICAgICAgICAgIGNhc2UgJ0VkMjU1MTknOlxuICAgICAgICAgIGNhc2UgJ0VkNDQ4JzpcbiAgICAgICAgICAgIHVzZSA9ICdzaWcnO1xuICAgICAgICAgICAgYWxnID0gJ0VkRFNBJztcbiAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgIGNhc2UgJ1gyNTUxOSc6XG4gICAgICAgICAgY2FzZSAnWDQ0OCc6XG4gICAgICAgICAgICB1c2UgPSAnZW5jJztcbiAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgIGRlZmF1bHQ6XG4gICAgICAgICAgICBicmVhaztcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICBpZiAoYWxnICYmICF1c2UpIHtcbiAgICAgICAgc3dpdGNoICh0cnVlKSB7XG4gICAgICAgICAgY2FzZSBhbGcuc3RhcnRzV2l0aCgnRUNESCcpOlxuICAgICAgICAgICAgdXNlID0gJ2VuYyc7XG4gICAgICAgICAgICBicmVhaztcbiAgICAgICAgICBjYXNlIGFsZy5zdGFydHNXaXRoKCdSU0EnKTpcbiAgICAgICAgICAgIHVzZSA9ICdlbmMnO1xuICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIGlmIChvbmx5UHJpdmF0ZSAmJiAoandrLmt0eSA9PT0gJ29jdCcgfHwgIWp3ay5kKSkge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ2p3a3MgbXVzdCBvbmx5IGNvbnRhaW4gcHJpdmF0ZSBrZXlzJyk7XG4gICAgICB9XG5cbiAgICAgIGlmIChvbmx5UHVibGljICYmIChqd2suZCB8fCBqd2suaykpIHtcbiAgICAgICAgY29udGludWU7XG4gICAgICB9XG5cbiAgICAgIGtleXMucHVzaCh7XG4gICAgICAgIGp3azogeyAuLi5qd2ssIGFsZywgdXNlIH0sXG4gICAgICAgIGFzeW5jIGtleU9iamVjdChhbGcpIHtcbiAgICAgICAgICBpZiAodGhpc1thbGddKSB7XG4gICAgICAgICAgICByZXR1cm4gdGhpc1thbGddO1xuICAgICAgICAgIH1cblxuICAgICAgICAgIGNvbnN0IGtleU9iamVjdCA9IGF3YWl0IGpvc2UuaW1wb3J0SldLKHRoaXMuandrLCBhbGcpO1xuICAgICAgICAgIHRoaXNbYWxnXSA9IGtleU9iamVjdDtcbiAgICAgICAgICByZXR1cm4ga2V5T2JqZWN0O1xuICAgICAgICB9LFxuICAgICAgICBnZXQgYWxnb3JpdGhtcygpIHtcbiAgICAgICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkodGhpcywgJ2FsZ29yaXRobXMnLCB7XG4gICAgICAgICAgICB2YWx1ZTogZ2V0QWxnb3JpdGhtcyh0aGlzLmp3ay51c2UsIHRoaXMuandrLmFsZywgdGhpcy5qd2sua3R5LCB0aGlzLmp3ay5jcnYpLFxuICAgICAgICAgICAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIGNvbmZpZ3VyYWJsZTogZmFsc2UsXG4gICAgICAgICAgfSk7XG4gICAgICAgICAgcmV0dXJuIHRoaXMuYWxnb3JpdGhtcztcbiAgICAgICAgfSxcbiAgICAgIH0pO1xuICAgIH1cblxuICAgIHJldHVybiBuZXcgdGhpcyhpbnRlcm5hbCwga2V5cyk7XG4gIH1cblxuICBmaWx0ZXIoLi4uYXJncykge1xuICAgIHJldHVybiB0aGlzLiNrZXlzLmZpbHRlciguLi5hcmdzKTtcbiAgfVxuXG4gIGZpbmQoLi4uYXJncykge1xuICAgIHJldHVybiB0aGlzLiNrZXlzLmZpbmQoLi4uYXJncyk7XG4gIH1cblxuICBldmVyeSguLi5hcmdzKSB7XG4gICAgcmV0dXJuIHRoaXMuI2tleXMuZXZlcnkoLi4uYXJncyk7XG4gIH1cblxuICBzb21lKC4uLmFyZ3MpIHtcbiAgICByZXR1cm4gdGhpcy4ja2V5cy5zb21lKC4uLmFyZ3MpO1xuICB9XG5cbiAgbWFwKC4uLmFyZ3MpIHtcbiAgICByZXR1cm4gdGhpcy4ja2V5cy5tYXAoLi4uYXJncyk7XG4gIH1cblxuICBmb3JFYWNoKC4uLmFyZ3MpIHtcbiAgICByZXR1cm4gdGhpcy4ja2V5cy5mb3JFYWNoKC4uLmFyZ3MpO1xuICB9XG5cbiAgcmVkdWNlKC4uLmFyZ3MpIHtcbiAgICByZXR1cm4gdGhpcy4ja2V5cy5yZWR1Y2UoLi4uYXJncyk7XG4gIH1cblxuICBzb3J0KC4uLmFyZ3MpIHtcbiAgICByZXR1cm4gdGhpcy4ja2V5cy5zb3J0KC4uLmFyZ3MpO1xuICB9XG5cbiAgKltTeW1ib2wuaXRlcmF0b3JdKCkge1xuICAgIGZvciAoY29uc3Qga2V5IG9mIHRoaXMuI2tleXMpIHtcbiAgICAgIHlpZWxkIGtleTtcbiAgICB9XG4gIH1cbn07XG4iXSwibmFtZXMiOlsiam9zZSIsInJlcXVpcmUiLCJjbG9uZSIsImlzUGxhaW5PYmplY3QiLCJpbnRlcm5hbCIsIlN5bWJvbCIsImtleXNjb3JlIiwia2V5IiwiYWxnIiwidXNlIiwic2NvcmUiLCJnZXRLdHlGcm9tQWxnIiwic2xpY2UiLCJ1bmRlZmluZWQiLCJnZXRBbGdvcml0aG1zIiwia3R5IiwiY3J2IiwiU2V0IiwiYWxncyIsImNvbmNhdCIsImNyeXB0b1J1bnRpbWUiLCJFcnJvciIsIm1vZHVsZSIsImV4cG9ydHMiLCJLZXlTdG9yZSIsImtleXMiLCJjb25zdHJ1Y3RvciIsImkiLCJ0b0pXS1MiLCJtYXAiLCJqd2siLCJkIiwicCIsInEiLCJkcCIsImRxIiwicWkiLCJhbGwiLCJraWQiLCJzZWFyY2giLCJmaWx0ZXIiLCJjYW5kaWRhdGUiLCJhbGdvcml0aG1zIiwiaGFzIiwic29ydCIsImZpcnN0Iiwic2Vjb25kIiwiZ2V0IiwiYXJncyIsImZyb21KV0tTIiwiandrcyIsIm9ubHlQdWJsaWMiLCJvbmx5UHJpdmF0ZSIsIkFycmF5IiwiaXNBcnJheSIsInNvbWUiLCJrIiwiVHlwZUVycm9yIiwic3RhcnRzV2l0aCIsInB1c2giLCJrZXlPYmplY3QiLCJpbXBvcnRKV0siLCJPYmplY3QiLCJkZWZpbmVQcm9wZXJ0eSIsInZhbHVlIiwiZW51bWVyYWJsZSIsImNvbmZpZ3VyYWJsZSIsImZpbmQiLCJldmVyeSIsImZvckVhY2giLCJyZWR1Y2UiLCJpdGVyYXRvciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/openid-client/lib/helpers/keystore.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/openid-client/lib/helpers/merge.js":
/*!*********************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/merge.js ***!
  \*********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst isPlainObject = __webpack_require__(/*! ./is_plain_object */ \"(ssr)/./node_modules/openid-client/lib/helpers/is_plain_object.js\");\nfunction merge(target, ...sources) {\n    for (const source of sources){\n        if (!isPlainObject(source)) {\n            continue;\n        }\n        for (const [key, value] of Object.entries(source)){\n            /* istanbul ignore if */ if (key === \"__proto__\" || key === \"constructor\") {\n                continue;\n            }\n            if (isPlainObject(target[key]) && isPlainObject(value)) {\n                target[key] = merge(target[key], value);\n            } else if (typeof value !== \"undefined\") {\n                target[key] = value;\n            }\n        }\n    }\n    return target;\n}\nmodule.exports = merge;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9tZXJnZS5qcyIsIm1hcHBpbmdzIjoiO0FBQUEsTUFBTUEsZ0JBQWdCQyxtQkFBT0EsQ0FBQztBQUU5QixTQUFTQyxNQUFNQyxNQUFNLEVBQUUsR0FBR0MsT0FBTztJQUMvQixLQUFLLE1BQU1DLFVBQVVELFFBQVM7UUFDNUIsSUFBSSxDQUFDSixjQUFjSyxTQUFTO1lBQzFCO1FBQ0Y7UUFDQSxLQUFLLE1BQU0sQ0FBQ0MsS0FBS0MsTUFBTSxJQUFJQyxPQUFPQyxPQUFPLENBQUNKLFFBQVM7WUFDakQsc0JBQXNCLEdBQ3RCLElBQUlDLFFBQVEsZUFBZUEsUUFBUSxlQUFlO2dCQUNoRDtZQUNGO1lBQ0EsSUFBSU4sY0FBY0csTUFBTSxDQUFDRyxJQUFJLEtBQUtOLGNBQWNPLFFBQVE7Z0JBQ3RESixNQUFNLENBQUNHLElBQUksR0FBR0osTUFBTUMsTUFBTSxDQUFDRyxJQUFJLEVBQUVDO1lBQ25DLE9BQU8sSUFBSSxPQUFPQSxVQUFVLGFBQWE7Z0JBQ3ZDSixNQUFNLENBQUNHLElBQUksR0FBR0M7WUFDaEI7UUFDRjtJQUNGO0lBRUEsT0FBT0o7QUFDVDtBQUVBTyxPQUFPQyxPQUFPLEdBQUdUIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFya2V0cGxhY2UvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9tZXJnZS5qcz9hNThmIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IGlzUGxhaW5PYmplY3QgPSByZXF1aXJlKCcuL2lzX3BsYWluX29iamVjdCcpO1xuXG5mdW5jdGlvbiBtZXJnZSh0YXJnZXQsIC4uLnNvdXJjZXMpIHtcbiAgZm9yIChjb25zdCBzb3VyY2Ugb2Ygc291cmNlcykge1xuICAgIGlmICghaXNQbGFpbk9iamVjdChzb3VyY2UpKSB7XG4gICAgICBjb250aW51ZTtcbiAgICB9XG4gICAgZm9yIChjb25zdCBba2V5LCB2YWx1ZV0gb2YgT2JqZWN0LmVudHJpZXMoc291cmNlKSkge1xuICAgICAgLyogaXN0YW5idWwgaWdub3JlIGlmICovXG4gICAgICBpZiAoa2V5ID09PSAnX19wcm90b19fJyB8fCBrZXkgPT09ICdjb25zdHJ1Y3RvcicpIHtcbiAgICAgICAgY29udGludWU7XG4gICAgICB9XG4gICAgICBpZiAoaXNQbGFpbk9iamVjdCh0YXJnZXRba2V5XSkgJiYgaXNQbGFpbk9iamVjdCh2YWx1ZSkpIHtcbiAgICAgICAgdGFyZ2V0W2tleV0gPSBtZXJnZSh0YXJnZXRba2V5XSwgdmFsdWUpO1xuICAgICAgfSBlbHNlIGlmICh0eXBlb2YgdmFsdWUgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgIHRhcmdldFtrZXldID0gdmFsdWU7XG4gICAgICB9XG4gICAgfVxuICB9XG5cbiAgcmV0dXJuIHRhcmdldDtcbn1cblxubW9kdWxlLmV4cG9ydHMgPSBtZXJnZTtcbiJdLCJuYW1lcyI6WyJpc1BsYWluT2JqZWN0IiwicmVxdWlyZSIsIm1lcmdlIiwidGFyZ2V0Iiwic291cmNlcyIsInNvdXJjZSIsImtleSIsInZhbHVlIiwiT2JqZWN0IiwiZW50cmllcyIsIm1vZHVsZSIsImV4cG9ydHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/openid-client/lib/helpers/merge.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/openid-client/lib/helpers/pick.js":
/*!********************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/pick.js ***!
  \********************************************************/
/***/ ((module) => {

eval("\nmodule.exports = function pick(object, ...paths) {\n    const obj = {};\n    for (const path of paths){\n        if (object[path] !== undefined) {\n            obj[path] = object[path];\n        }\n    }\n    return obj;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9waWNrLmpzIiwibWFwcGluZ3MiOiI7QUFBQUEsT0FBT0MsT0FBTyxHQUFHLFNBQVNDLEtBQUtDLE1BQU0sRUFBRSxHQUFHQyxLQUFLO0lBQzdDLE1BQU1DLE1BQU0sQ0FBQztJQUNiLEtBQUssTUFBTUMsUUFBUUYsTUFBTztRQUN4QixJQUFJRCxNQUFNLENBQUNHLEtBQUssS0FBS0MsV0FBVztZQUM5QkYsR0FBRyxDQUFDQyxLQUFLLEdBQUdILE1BQU0sQ0FBQ0csS0FBSztRQUMxQjtJQUNGO0lBQ0EsT0FBT0Q7QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL21hcmtldHBsYWNlLy4vbm9kZV9tb2R1bGVzL29wZW5pZC1jbGllbnQvbGliL2hlbHBlcnMvcGljay5qcz82ODY3Il0sInNvdXJjZXNDb250ZW50IjpbIm1vZHVsZS5leHBvcnRzID0gZnVuY3Rpb24gcGljayhvYmplY3QsIC4uLnBhdGhzKSB7XG4gIGNvbnN0IG9iaiA9IHt9O1xuICBmb3IgKGNvbnN0IHBhdGggb2YgcGF0aHMpIHtcbiAgICBpZiAob2JqZWN0W3BhdGhdICE9PSB1bmRlZmluZWQpIHtcbiAgICAgIG9ialtwYXRoXSA9IG9iamVjdFtwYXRoXTtcbiAgICB9XG4gIH1cbiAgcmV0dXJuIG9iajtcbn07XG4iXSwibmFtZXMiOlsibW9kdWxlIiwiZXhwb3J0cyIsInBpY2siLCJvYmplY3QiLCJwYXRocyIsIm9iaiIsInBhdGgiLCJ1bmRlZmluZWQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/openid-client/lib/helpers/pick.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/openid-client/lib/helpers/process_response.js":
/*!********************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/process_response.js ***!
  \********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst { STATUS_CODES } = __webpack_require__(/*! http */ \"http\");\nconst { format } = __webpack_require__(/*! util */ \"util\");\nconst { OPError } = __webpack_require__(/*! ../errors */ \"(ssr)/./node_modules/openid-client/lib/errors.js\");\nconst parseWwwAuthenticate = __webpack_require__(/*! ./www_authenticate_parser */ \"(ssr)/./node_modules/openid-client/lib/helpers/www_authenticate_parser.js\");\nconst throwAuthenticateErrors = (response)=>{\n    const params = parseWwwAuthenticate(response.headers[\"www-authenticate\"]);\n    if (params.error) {\n        throw new OPError(params, response);\n    }\n};\nconst isStandardBodyError = (response)=>{\n    let result = false;\n    try {\n        let jsonbody;\n        if (typeof response.body !== \"object\" || Buffer.isBuffer(response.body)) {\n            jsonbody = JSON.parse(response.body);\n        } else {\n            jsonbody = response.body;\n        }\n        result = typeof jsonbody.error === \"string\" && jsonbody.error.length;\n        if (result) Object.defineProperty(response, \"body\", {\n            value: jsonbody,\n            configurable: true\n        });\n    } catch (err) {}\n    return result;\n};\nfunction processResponse(response, { statusCode = 200, body = true, bearer = false } = {}) {\n    if (response.statusCode !== statusCode) {\n        if (bearer) {\n            throwAuthenticateErrors(response);\n        }\n        if (isStandardBodyError(response)) {\n            throw new OPError(response.body, response);\n        }\n        throw new OPError({\n            error: format(\"expected %i %s, got: %i %s\", statusCode, STATUS_CODES[statusCode], response.statusCode, STATUS_CODES[response.statusCode])\n        }, response);\n    }\n    if (body && !response.body) {\n        throw new OPError({\n            error: format(\"expected %i %s with body but no body was returned\", statusCode, STATUS_CODES[statusCode])\n        }, response);\n    }\n    return response.body;\n}\nmodule.exports = processResponse;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/openid-client/lib/helpers/process_response.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/openid-client/lib/helpers/request.js":
/*!***********************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/request.js ***!
  \***********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst assert = __webpack_require__(/*! assert */ \"assert\");\nconst querystring = __webpack_require__(/*! querystring */ \"querystring\");\nconst http = __webpack_require__(/*! http */ \"http\");\nconst https = __webpack_require__(/*! https */ \"https\");\nconst { once } = __webpack_require__(/*! events */ \"events\");\nconst { URL } = __webpack_require__(/*! url */ \"url\");\nconst LRU = __webpack_require__(/*! lru-cache */ \"(ssr)/./node_modules/openid-client/node_modules/lru-cache/index.js\");\nconst pkg = __webpack_require__(/*! ../../package.json */ \"(ssr)/./node_modules/openid-client/package.json\");\nconst { RPError } = __webpack_require__(/*! ../errors */ \"(ssr)/./node_modules/openid-client/lib/errors.js\");\nconst pick = __webpack_require__(/*! ./pick */ \"(ssr)/./node_modules/openid-client/lib/helpers/pick.js\");\nconst { deep: defaultsDeep } = __webpack_require__(/*! ./defaults */ \"(ssr)/./node_modules/openid-client/lib/helpers/defaults.js\");\nconst { HTTP_OPTIONS } = __webpack_require__(/*! ./consts */ \"(ssr)/./node_modules/openid-client/lib/helpers/consts.js\");\nlet DEFAULT_HTTP_OPTIONS;\nconst NQCHAR = /^[\\x21\\x23-\\x5B\\x5D-\\x7E]+$/;\nconst allowed = [\n    \"agent\",\n    \"ca\",\n    \"cert\",\n    \"crl\",\n    \"headers\",\n    \"key\",\n    \"lookup\",\n    \"passphrase\",\n    \"pfx\",\n    \"timeout\"\n];\nconst setDefaults = (props, options)=>{\n    DEFAULT_HTTP_OPTIONS = defaultsDeep({}, props.length ? pick(options, ...props) : options, DEFAULT_HTTP_OPTIONS);\n};\nsetDefaults([], {\n    headers: {\n        \"User-Agent\": `${pkg.name}/${pkg.version} (${pkg.homepage})`,\n        \"Accept-Encoding\": \"identity\"\n    },\n    timeout: 3500\n});\nfunction send(req, body, contentType) {\n    if (contentType) {\n        req.removeHeader(\"content-type\");\n        req.setHeader(\"content-type\", contentType);\n    }\n    if (body) {\n        req.removeHeader(\"content-length\");\n        req.setHeader(\"content-length\", Buffer.byteLength(body));\n        req.write(body);\n    }\n    req.end();\n}\nconst nonces = new LRU({\n    max: 100\n});\nmodule.exports = async function request(options, { accessToken, mTLS = false, DPoP } = {}) {\n    let url;\n    try {\n        url = new URL(options.url);\n        delete options.url;\n        assert(/^(https?:)$/.test(url.protocol));\n    } catch (err) {\n        throw new TypeError(\"only valid absolute URLs can be requested\");\n    }\n    const optsFn = this[HTTP_OPTIONS];\n    let opts = options;\n    const nonceKey = `${url.origin}${url.pathname}`;\n    if (DPoP && \"dpopProof\" in this) {\n        opts.headers = opts.headers || {};\n        opts.headers.DPoP = await this.dpopProof({\n            htu: `${url.origin}${url.pathname}`,\n            htm: options.method || \"GET\",\n            nonce: nonces.get(nonceKey)\n        }, DPoP, accessToken);\n    }\n    let userOptions;\n    if (optsFn) {\n        userOptions = pick(optsFn.call(this, url, defaultsDeep({}, opts, DEFAULT_HTTP_OPTIONS)), ...allowed);\n    }\n    opts = defaultsDeep({}, userOptions, opts, DEFAULT_HTTP_OPTIONS);\n    if (mTLS && !opts.pfx && !(opts.key && opts.cert)) {\n        throw new TypeError(\"mutual-TLS certificate and key not set\");\n    }\n    if (opts.searchParams) {\n        for (const [key, value] of Object.entries(opts.searchParams)){\n            url.searchParams.delete(key);\n            url.searchParams.set(key, value);\n        }\n    }\n    let responseType;\n    let form;\n    let json;\n    let body;\n    ({ form, responseType, json, body, ...opts } = opts);\n    for (const [key, value] of Object.entries(opts.headers || {})){\n        if (value === undefined) {\n            delete opts.headers[key];\n        }\n    }\n    let response;\n    const req = (url.protocol === \"https:\" ? https.request : http.request)(url.href, opts);\n    return (async ()=>{\n        if (json) {\n            send(req, JSON.stringify(json), \"application/json\");\n        } else if (form) {\n            send(req, querystring.stringify(form), \"application/x-www-form-urlencoded\");\n        } else if (body) {\n            send(req, body);\n        } else {\n            send(req);\n        }\n        [response] = await Promise.race([\n            once(req, \"response\"),\n            once(req, \"timeout\")\n        ]);\n        // timeout reached\n        if (!response) {\n            req.destroy();\n            throw new RPError(`outgoing request timed out after ${opts.timeout}ms`);\n        }\n        const parts = [];\n        for await (const part of response){\n            parts.push(part);\n        }\n        if (parts.length) {\n            switch(responseType){\n                case \"json\":\n                    {\n                        Object.defineProperty(response, \"body\", {\n                            get () {\n                                let value = Buffer.concat(parts);\n                                try {\n                                    value = JSON.parse(value);\n                                } catch (err) {\n                                    Object.defineProperty(err, \"response\", {\n                                        value: response\n                                    });\n                                    throw err;\n                                } finally{\n                                    Object.defineProperty(response, \"body\", {\n                                        value,\n                                        configurable: true\n                                    });\n                                }\n                                return value;\n                            },\n                            configurable: true\n                        });\n                        break;\n                    }\n                case undefined:\n                case \"buffer\":\n                    {\n                        Object.defineProperty(response, \"body\", {\n                            get () {\n                                const value = Buffer.concat(parts);\n                                Object.defineProperty(response, \"body\", {\n                                    value,\n                                    configurable: true\n                                });\n                                return value;\n                            },\n                            configurable: true\n                        });\n                        break;\n                    }\n                default:\n                    throw new TypeError(\"unsupported responseType request option\");\n            }\n        }\n        return response;\n    })().catch((err)=>{\n        if (response) Object.defineProperty(err, \"response\", {\n            value: response\n        });\n        throw err;\n    }).finally(()=>{\n        const dpopNonce = response && response.headers[\"dpop-nonce\"];\n        if (dpopNonce && NQCHAR.test(dpopNonce)) {\n            nonces.set(nonceKey, dpopNonce);\n        }\n    });\n};\nmodule.exports.setDefaults = setDefaults.bind(undefined, allowed);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/openid-client/lib/helpers/request.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/openid-client/lib/helpers/unix_timestamp.js":
/*!******************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/unix_timestamp.js ***!
  \******************************************************************/
/***/ ((module) => {

eval("\nmodule.exports = ()=>Math.floor(Date.now() / 1000);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy91bml4X3RpbWVzdGFtcC5qcyIsIm1hcHBpbmdzIjoiO0FBQUFBLE9BQU9DLE9BQU8sR0FBRyxJQUFNQyxLQUFLQyxLQUFLLENBQUNDLEtBQUtDLEdBQUcsS0FBSyIsInNvdXJjZXMiOlsid2VicGFjazovL21hcmtldHBsYWNlLy4vbm9kZV9tb2R1bGVzL29wZW5pZC1jbGllbnQvbGliL2hlbHBlcnMvdW5peF90aW1lc3RhbXAuanM/NGU3ZiJdLCJzb3VyY2VzQ29udGVudCI6WyJtb2R1bGUuZXhwb3J0cyA9ICgpID0+IE1hdGguZmxvb3IoRGF0ZS5ub3coKSAvIDEwMDApO1xuIl0sIm5hbWVzIjpbIm1vZHVsZSIsImV4cG9ydHMiLCJNYXRoIiwiZmxvb3IiLCJEYXRlIiwibm93Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/openid-client/lib/helpers/unix_timestamp.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/openid-client/lib/helpers/weak_cache.js":
/*!**************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/weak_cache.js ***!
  \**************************************************************/
/***/ ((module) => {

eval("\nmodule.exports.keystores = new WeakMap();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy93ZWFrX2NhY2hlLmpzIiwibWFwcGluZ3MiOiI7QUFBQUEsd0JBQXdCLEdBQUcsSUFBSUciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJrZXRwbGFjZS8uL25vZGVfbW9kdWxlcy9vcGVuaWQtY2xpZW50L2xpYi9oZWxwZXJzL3dlYWtfY2FjaGUuanM/YzkzMCJdLCJzb3VyY2VzQ29udGVudCI6WyJtb2R1bGUuZXhwb3J0cy5rZXlzdG9yZXMgPSBuZXcgV2Vha01hcCgpO1xuIl0sIm5hbWVzIjpbIm1vZHVsZSIsImV4cG9ydHMiLCJrZXlzdG9yZXMiLCJXZWFrTWFwIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/openid-client/lib/helpers/weak_cache.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/openid-client/lib/helpers/webfinger_normalize.js":
/*!***********************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/webfinger_normalize.js ***!
  \***********************************************************************/
/***/ ((module) => {

eval("// Credit: https://github.com/rohe/pyoidc/blob/master/src/oic/utils/webfinger.py\n// -- Normalization --\n// A string of any other type is interpreted as a URI either the form of scheme\n// \"://\" authority path-abempty [ \"?\" query ] [ \"#\" fragment ] or authority\n// path-abempty [ \"?\" query ] [ \"#\" fragment ] per RFC 3986 [RFC3986] and is\n// normalized according to the following rules:\n//\n// If the user input Identifier does not have an RFC 3986 [RFC3986] scheme\n// portion, the string is interpreted as [userinfo \"@\"] host [\":\" port]\n// path-abempty [ \"?\" query ] [ \"#\" fragment ] per RFC 3986 [RFC3986].\n// If the userinfo component is present and all of the path component, query\n// component, and port component are empty, the acct scheme is assumed. In this\n// case, the normalized URI is formed by prefixing acct: to the string as the\n// scheme. Per the 'acct' URI Scheme [I‑D.ietf‑appsawg‑acct‑uri], if there is an\n// at-sign character ('@') in the userinfo component, it needs to be\n// percent-encoded as described in RFC 3986 [RFC3986].\n// For all other inputs without a scheme portion, the https scheme is assumed,\n// and the normalized URI is formed by prefixing https:// to the string as the\n// scheme.\n// If the resulting URI contains a fragment portion, it MUST be stripped off\n// together with the fragment delimiter character \"#\".\n// The WebFinger [I‑D.ietf‑appsawg‑webfinger] Resource in this case is the\n// resulting URI, and the WebFinger Host is the authority component.\n//\n// Note: Since the definition of authority in RFC 3986 [RFC3986] is\n// [ userinfo \"@\" ] host [ \":\" port ], it is legal to have a user input\n// identifier like userinfo@host:port, e.g., <EMAIL>:8080.\n\nconst PORT = /^\\d+$/;\nfunction hasScheme(input) {\n    if (input.includes(\"://\")) return true;\n    const authority = input.replace(/(\\/|\\?)/g, \"#\").split(\"#\")[0];\n    if (authority.includes(\":\")) {\n        const index = authority.indexOf(\":\");\n        const hostOrPort = authority.slice(index + 1);\n        if (!PORT.test(hostOrPort)) {\n            return true;\n        }\n    }\n    return false;\n}\nfunction acctSchemeAssumed(input) {\n    if (!input.includes(\"@\")) return false;\n    const parts = input.split(\"@\");\n    const host = parts[parts.length - 1];\n    return !(host.includes(\":\") || host.includes(\"/\") || host.includes(\"?\"));\n}\nfunction normalize(input) {\n    if (typeof input !== \"string\") {\n        throw new TypeError(\"input must be a string\");\n    }\n    let output;\n    if (hasScheme(input)) {\n        output = input;\n    } else if (acctSchemeAssumed(input)) {\n        output = `acct:${input}`;\n    } else {\n        output = `https://${input}`;\n    }\n    return output.split(\"#\")[0];\n}\nmodule.exports = normalize;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/openid-client/lib/helpers/webfinger_normalize.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/openid-client/lib/helpers/www_authenticate_parser.js":
/*!***************************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/www_authenticate_parser.js ***!
  \***************************************************************************/
/***/ ((module) => {

eval("\nconst REGEXP = /(\\w+)=(\"[^\"]*\")/g;\nmodule.exports = (wwwAuthenticate)=>{\n    const params = {};\n    try {\n        while(REGEXP.exec(wwwAuthenticate) !== null){\n            if (RegExp.$1 && RegExp.$2) {\n                params[RegExp.$1] = RegExp.$2.slice(1, -1);\n            }\n        }\n    } catch (err) {}\n    return params;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy93d3dfYXV0aGVudGljYXRlX3BhcnNlci5qcyIsIm1hcHBpbmdzIjoiO0FBQUEsTUFBTUEsU0FBUztBQUVmQyxPQUFPQyxPQUFPLEdBQUcsQ0FBQ0M7SUFDaEIsTUFBTUMsU0FBUyxDQUFDO0lBQ2hCLElBQUk7UUFDRixNQUFPSixPQUFPSyxJQUFJLENBQUNGLHFCQUFxQixLQUFNO1lBQzVDLElBQUlHLE9BQU9DLEVBQUUsSUFBSUQsT0FBT0UsRUFBRSxFQUFFO2dCQUMxQkosTUFBTSxDQUFDRSxPQUFPQyxFQUFFLENBQUMsR0FBR0QsT0FBT0UsRUFBRSxDQUFDQyxLQUFLLENBQUMsR0FBRyxDQUFDO1lBQzFDO1FBQ0Y7SUFDRixFQUFFLE9BQU9DLEtBQUssQ0FBQztJQUVmLE9BQU9OO0FBQ1QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJrZXRwbGFjZS8uL25vZGVfbW9kdWxlcy9vcGVuaWQtY2xpZW50L2xpYi9oZWxwZXJzL3d3d19hdXRoZW50aWNhdGVfcGFyc2VyLmpzP2YwMGQiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgUkVHRVhQID0gLyhcXHcrKT0oXCJbXlwiXSpcIikvZztcblxubW9kdWxlLmV4cG9ydHMgPSAod3d3QXV0aGVudGljYXRlKSA9PiB7XG4gIGNvbnN0IHBhcmFtcyA9IHt9O1xuICB0cnkge1xuICAgIHdoaWxlIChSRUdFWFAuZXhlYyh3d3dBdXRoZW50aWNhdGUpICE9PSBudWxsKSB7XG4gICAgICBpZiAoUmVnRXhwLiQxICYmIFJlZ0V4cC4kMikge1xuICAgICAgICBwYXJhbXNbUmVnRXhwLiQxXSA9IFJlZ0V4cC4kMi5zbGljZSgxLCAtMSk7XG4gICAgICB9XG4gICAgfVxuICB9IGNhdGNoIChlcnIpIHt9XG5cbiAgcmV0dXJuIHBhcmFtcztcbn07XG4iXSwibmFtZXMiOlsiUkVHRVhQIiwibW9kdWxlIiwiZXhwb3J0cyIsInd3d0F1dGhlbnRpY2F0ZSIsInBhcmFtcyIsImV4ZWMiLCJSZWdFeHAiLCIkMSIsIiQyIiwic2xpY2UiLCJlcnIiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/openid-client/lib/helpers/www_authenticate_parser.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/openid-client/lib/index.js":
/*!*************************************************!*\
  !*** ./node_modules/openid-client/lib/index.js ***!
  \*************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst Issuer = __webpack_require__(/*! ./issuer */ \"(ssr)/./node_modules/openid-client/lib/issuer.js\");\nconst { OPError, RPError } = __webpack_require__(/*! ./errors */ \"(ssr)/./node_modules/openid-client/lib/errors.js\");\nconst Strategy = __webpack_require__(/*! ./passport_strategy */ \"(ssr)/./node_modules/openid-client/lib/passport_strategy.js\");\nconst TokenSet = __webpack_require__(/*! ./token_set */ \"(ssr)/./node_modules/openid-client/lib/token_set.js\");\nconst { CLOCK_TOLERANCE, HTTP_OPTIONS } = __webpack_require__(/*! ./helpers/consts */ \"(ssr)/./node_modules/openid-client/lib/helpers/consts.js\");\nconst generators = __webpack_require__(/*! ./helpers/generators */ \"(ssr)/./node_modules/openid-client/lib/helpers/generators.js\");\nconst { setDefaults } = __webpack_require__(/*! ./helpers/request */ \"(ssr)/./node_modules/openid-client/lib/helpers/request.js\");\nmodule.exports = {\n    Issuer,\n    Strategy,\n    TokenSet,\n    errors: {\n        OPError,\n        RPError\n    },\n    custom: {\n        setHttpOptionsDefaults: setDefaults,\n        http_options: HTTP_OPTIONS,\n        clock_tolerance: CLOCK_TOLERANCE\n    },\n    generators\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaW5kZXguanMiLCJtYXBwaW5ncyI6IjtBQUFBLE1BQU1BLFNBQVNDLG1CQUFPQSxDQUFDO0FBQ3ZCLE1BQU0sRUFBRUMsT0FBTyxFQUFFQyxPQUFPLEVBQUUsR0FBR0YsbUJBQU9BLENBQUM7QUFDckMsTUFBTUcsV0FBV0gsbUJBQU9BLENBQUM7QUFDekIsTUFBTUksV0FBV0osbUJBQU9BLENBQUM7QUFDekIsTUFBTSxFQUFFSyxlQUFlLEVBQUVDLFlBQVksRUFBRSxHQUFHTixtQkFBT0EsQ0FBQztBQUNsRCxNQUFNTyxhQUFhUCxtQkFBT0EsQ0FBQztBQUMzQixNQUFNLEVBQUVRLFdBQVcsRUFBRSxHQUFHUixtQkFBT0EsQ0FBQztBQUVoQ1MsT0FBT0MsT0FBTyxHQUFHO0lBQ2ZYO0lBQ0FJO0lBQ0FDO0lBQ0FPLFFBQVE7UUFDTlY7UUFDQUM7SUFDRjtJQUNBVSxRQUFRO1FBQ05DLHdCQUF3Qkw7UUFDeEJNLGNBQWNSO1FBQ2RTLGlCQUFpQlY7SUFDbkI7SUFDQUU7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL21hcmtldHBsYWNlLy4vbm9kZV9tb2R1bGVzL29wZW5pZC1jbGllbnQvbGliL2luZGV4LmpzP2NmOTEiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgSXNzdWVyID0gcmVxdWlyZSgnLi9pc3N1ZXInKTtcbmNvbnN0IHsgT1BFcnJvciwgUlBFcnJvciB9ID0gcmVxdWlyZSgnLi9lcnJvcnMnKTtcbmNvbnN0IFN0cmF0ZWd5ID0gcmVxdWlyZSgnLi9wYXNzcG9ydF9zdHJhdGVneScpO1xuY29uc3QgVG9rZW5TZXQgPSByZXF1aXJlKCcuL3Rva2VuX3NldCcpO1xuY29uc3QgeyBDTE9DS19UT0xFUkFOQ0UsIEhUVFBfT1BUSU9OUyB9ID0gcmVxdWlyZSgnLi9oZWxwZXJzL2NvbnN0cycpO1xuY29uc3QgZ2VuZXJhdG9ycyA9IHJlcXVpcmUoJy4vaGVscGVycy9nZW5lcmF0b3JzJyk7XG5jb25zdCB7IHNldERlZmF1bHRzIH0gPSByZXF1aXJlKCcuL2hlbHBlcnMvcmVxdWVzdCcpO1xuXG5tb2R1bGUuZXhwb3J0cyA9IHtcbiAgSXNzdWVyLFxuICBTdHJhdGVneSxcbiAgVG9rZW5TZXQsXG4gIGVycm9yczoge1xuICAgIE9QRXJyb3IsXG4gICAgUlBFcnJvcixcbiAgfSxcbiAgY3VzdG9tOiB7XG4gICAgc2V0SHR0cE9wdGlvbnNEZWZhdWx0czogc2V0RGVmYXVsdHMsXG4gICAgaHR0cF9vcHRpb25zOiBIVFRQX09QVElPTlMsXG4gICAgY2xvY2tfdG9sZXJhbmNlOiBDTE9DS19UT0xFUkFOQ0UsXG4gIH0sXG4gIGdlbmVyYXRvcnMsXG59O1xuIl0sIm5hbWVzIjpbIklzc3VlciIsInJlcXVpcmUiLCJPUEVycm9yIiwiUlBFcnJvciIsIlN0cmF0ZWd5IiwiVG9rZW5TZXQiLCJDTE9DS19UT0xFUkFOQ0UiLCJIVFRQX09QVElPTlMiLCJnZW5lcmF0b3JzIiwic2V0RGVmYXVsdHMiLCJtb2R1bGUiLCJleHBvcnRzIiwiZXJyb3JzIiwiY3VzdG9tIiwic2V0SHR0cE9wdGlvbnNEZWZhdWx0cyIsImh0dHBfb3B0aW9ucyIsImNsb2NrX3RvbGVyYW5jZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/openid-client/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/openid-client/lib/issuer.js":
/*!**************************************************!*\
  !*** ./node_modules/openid-client/lib/issuer.js ***!
  \**************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst { inspect } = __webpack_require__(/*! util */ \"util\");\nconst url = __webpack_require__(/*! url */ \"url\");\nconst { RPError } = __webpack_require__(/*! ./errors */ \"(ssr)/./node_modules/openid-client/lib/errors.js\");\nconst getClient = __webpack_require__(/*! ./client */ \"(ssr)/./node_modules/openid-client/lib/client.js\");\nconst registry = __webpack_require__(/*! ./issuer_registry */ \"(ssr)/./node_modules/openid-client/lib/issuer_registry.js\");\nconst processResponse = __webpack_require__(/*! ./helpers/process_response */ \"(ssr)/./node_modules/openid-client/lib/helpers/process_response.js\");\nconst webfingerNormalize = __webpack_require__(/*! ./helpers/webfinger_normalize */ \"(ssr)/./node_modules/openid-client/lib/helpers/webfinger_normalize.js\");\nconst request = __webpack_require__(/*! ./helpers/request */ \"(ssr)/./node_modules/openid-client/lib/helpers/request.js\");\nconst clone = __webpack_require__(/*! ./helpers/deep_clone */ \"(ssr)/./node_modules/openid-client/lib/helpers/deep_clone.js\");\nconst { keystore } = __webpack_require__(/*! ./helpers/issuer */ \"(ssr)/./node_modules/openid-client/lib/helpers/issuer.js\");\nconst AAD_MULTITENANT_DISCOVERY = [\n    \"https://login.microsoftonline.com/common/.well-known/openid-configuration\",\n    \"https://login.microsoftonline.com/common/v2.0/.well-known/openid-configuration\",\n    \"https://login.microsoftonline.com/organizations/v2.0/.well-known/openid-configuration\",\n    \"https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration\"\n];\nconst AAD_MULTITENANT = Symbol();\nconst ISSUER_DEFAULTS = {\n    claim_types_supported: [\n        \"normal\"\n    ],\n    claims_parameter_supported: false,\n    grant_types_supported: [\n        \"authorization_code\",\n        \"implicit\"\n    ],\n    request_parameter_supported: false,\n    request_uri_parameter_supported: true,\n    require_request_uri_registration: false,\n    response_modes_supported: [\n        \"query\",\n        \"fragment\"\n    ],\n    token_endpoint_auth_methods_supported: [\n        \"client_secret_basic\"\n    ]\n};\nclass Issuer {\n    #metadata;\n    constructor(meta = {}){\n        const aadIssValidation = meta[AAD_MULTITENANT];\n        delete meta[AAD_MULTITENANT];\n        [\n            \"introspection\",\n            \"revocation\"\n        ].forEach((endpoint)=>{\n            // if intro/revocation endpoint auth specific meta is missing use the token ones if they\n            // are defined\n            if (meta[`${endpoint}_endpoint`] && meta[`${endpoint}_endpoint_auth_methods_supported`] === undefined && meta[`${endpoint}_endpoint_auth_signing_alg_values_supported`] === undefined) {\n                if (meta.token_endpoint_auth_methods_supported) {\n                    meta[`${endpoint}_endpoint_auth_methods_supported`] = meta.token_endpoint_auth_methods_supported;\n                }\n                if (meta.token_endpoint_auth_signing_alg_values_supported) {\n                    meta[`${endpoint}_endpoint_auth_signing_alg_values_supported`] = meta.token_endpoint_auth_signing_alg_values_supported;\n                }\n            }\n        });\n        this.#metadata = new Map();\n        Object.entries(meta).forEach(([key, value])=>{\n            this.#metadata.set(key, value);\n            if (!this[key]) {\n                Object.defineProperty(this, key, {\n                    get () {\n                        return this.#metadata.get(key);\n                    },\n                    enumerable: true\n                });\n            }\n        });\n        registry.set(this.issuer, this);\n        const Client = getClient(this, aadIssValidation);\n        Object.defineProperties(this, {\n            Client: {\n                value: Client,\n                enumerable: true\n            },\n            FAPI1Client: {\n                value: class FAPI1Client extends Client {\n                },\n                enumerable: true\n            },\n            FAPI2Client: {\n                value: class FAPI2Client extends Client {\n                },\n                enumerable: true\n            }\n        });\n    }\n    get metadata() {\n        return clone(Object.fromEntries(this.#metadata.entries()));\n    }\n    static async webfinger(input) {\n        const resource = webfingerNormalize(input);\n        const { host } = url.parse(resource);\n        const webfingerUrl = `https://${host}/.well-known/webfinger`;\n        const response = await request.call(this, {\n            method: \"GET\",\n            url: webfingerUrl,\n            responseType: \"json\",\n            searchParams: {\n                resource,\n                rel: \"http://openid.net/specs/connect/1.0/issuer\"\n            },\n            headers: {\n                Accept: \"application/json\"\n            }\n        });\n        const body = processResponse(response);\n        const location = Array.isArray(body.links) && body.links.find((link)=>typeof link === \"object\" && link.rel === \"http://openid.net/specs/connect/1.0/issuer\" && link.href);\n        if (!location) {\n            throw new RPError({\n                message: \"no issuer found in webfinger response\",\n                body\n            });\n        }\n        if (typeof location.href !== \"string\" || !location.href.startsWith(\"https://\")) {\n            throw new RPError({\n                printf: [\n                    \"invalid issuer location %s\",\n                    location.href\n                ],\n                body\n            });\n        }\n        const expectedIssuer = location.href;\n        if (registry.has(expectedIssuer)) {\n            return registry.get(expectedIssuer);\n        }\n        const issuer = await this.discover(expectedIssuer);\n        if (issuer.issuer !== expectedIssuer) {\n            registry.del(issuer.issuer);\n            throw new RPError(\"discovered issuer mismatch, expected %s, got: %s\", expectedIssuer, issuer.issuer);\n        }\n        return issuer;\n    }\n    static async discover(uri) {\n        const wellKnownUri = resolveWellKnownUri(uri);\n        const response = await request.call(this, {\n            method: \"GET\",\n            responseType: \"json\",\n            url: wellKnownUri,\n            headers: {\n                Accept: \"application/json\"\n            }\n        });\n        const body = processResponse(response);\n        return new Issuer({\n            ...ISSUER_DEFAULTS,\n            ...body,\n            [AAD_MULTITENANT]: !!AAD_MULTITENANT_DISCOVERY.find((discoveryURL)=>wellKnownUri.startsWith(discoveryURL))\n        });\n    }\n    async reloadJwksUri() {\n        await keystore.call(this, true);\n    }\n    /* istanbul ignore next */ [inspect.custom]() {\n        return `${this.constructor.name} ${inspect(this.metadata, {\n            depth: Infinity,\n            colors: process.stdout.isTTY,\n            compact: false,\n            sorted: true\n        })}`;\n    }\n}\nfunction resolveWellKnownUri(uri) {\n    const parsed = url.parse(uri);\n    if (parsed.pathname.includes(\"/.well-known/\")) {\n        return uri;\n    } else {\n        let pathname;\n        if (parsed.pathname.endsWith(\"/\")) {\n            pathname = `${parsed.pathname}.well-known/openid-configuration`;\n        } else {\n            pathname = `${parsed.pathname}/.well-known/openid-configuration`;\n        }\n        return url.format({\n            ...parsed,\n            pathname\n        });\n    }\n}\nmodule.exports = Issuer;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/openid-client/lib/issuer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/openid-client/lib/issuer_registry.js":
/*!***********************************************************!*\
  !*** ./node_modules/openid-client/lib/issuer_registry.js ***!
  \***********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst LRU = __webpack_require__(/*! lru-cache */ \"(ssr)/./node_modules/openid-client/node_modules/lru-cache/index.js\");\nmodule.exports = new LRU({\n    max: 100\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaXNzdWVyX3JlZ2lzdHJ5LmpzIiwibWFwcGluZ3MiOiI7QUFBQSxNQUFNQSxNQUFNQyxtQkFBT0EsQ0FBQztBQUVwQkMsT0FBT0MsT0FBTyxHQUFHLElBQUlILElBQUk7SUFBRUksS0FBSztBQUFJIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFya2V0cGxhY2UvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaXNzdWVyX3JlZ2lzdHJ5LmpzP2IwNmQiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgTFJVID0gcmVxdWlyZSgnbHJ1LWNhY2hlJyk7XG5cbm1vZHVsZS5leHBvcnRzID0gbmV3IExSVSh7IG1heDogMTAwIH0pO1xuIl0sIm5hbWVzIjpbIkxSVSIsInJlcXVpcmUiLCJtb2R1bGUiLCJleHBvcnRzIiwibWF4Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/openid-client/lib/issuer_registry.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/openid-client/lib/passport_strategy.js":
/*!*************************************************************!*\
  !*** ./node_modules/openid-client/lib/passport_strategy.js ***!
  \*************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst url = __webpack_require__(/*! url */ \"url\");\nconst { format } = __webpack_require__(/*! util */ \"util\");\nconst cloneDeep = __webpack_require__(/*! ./helpers/deep_clone */ \"(ssr)/./node_modules/openid-client/lib/helpers/deep_clone.js\");\nconst { RPError, OPError } = __webpack_require__(/*! ./errors */ \"(ssr)/./node_modules/openid-client/lib/errors.js\");\nconst { BaseClient } = __webpack_require__(/*! ./client */ \"(ssr)/./node_modules/openid-client/lib/client.js\");\nconst { random, codeChallenge } = __webpack_require__(/*! ./helpers/generators */ \"(ssr)/./node_modules/openid-client/lib/helpers/generators.js\");\nconst pick = __webpack_require__(/*! ./helpers/pick */ \"(ssr)/./node_modules/openid-client/lib/helpers/pick.js\");\nconst { resolveResponseType, resolveRedirectUri } = __webpack_require__(/*! ./helpers/client */ \"(ssr)/./node_modules/openid-client/lib/helpers/client.js\");\nfunction verified(err, user, info = {}) {\n    if (err) {\n        this.error(err);\n    } else if (!user) {\n        this.fail(info);\n    } else {\n        this.success(user, info);\n    }\n}\nfunction OpenIDConnectStrategy({ client, params = {}, passReqToCallback = false, sessionKey, usePKCE = true, extras = {} } = {}, verify) {\n    if (!(client instanceof BaseClient)) {\n        throw new TypeError(\"client must be an instance of openid-client Client\");\n    }\n    if (typeof verify !== \"function\") {\n        throw new TypeError(\"verify callback must be a function\");\n    }\n    if (!client.issuer || !client.issuer.issuer) {\n        throw new TypeError(\"client must have an issuer with an identifier\");\n    }\n    this._client = client;\n    this._issuer = client.issuer;\n    this._verify = verify;\n    this._passReqToCallback = passReqToCallback;\n    this._usePKCE = usePKCE;\n    this._key = sessionKey || `oidc:${url.parse(this._issuer.issuer).hostname}`;\n    this._params = cloneDeep(params);\n    // state and nonce are handled in authenticate()\n    delete this._params.state;\n    delete this._params.nonce;\n    this._extras = cloneDeep(extras);\n    if (!this._params.response_type) this._params.response_type = resolveResponseType.call(client);\n    if (!this._params.redirect_uri) this._params.redirect_uri = resolveRedirectUri.call(client);\n    if (!this._params.scope) this._params.scope = \"openid\";\n    if (this._usePKCE === true) {\n        const supportedMethods = Array.isArray(this._issuer.code_challenge_methods_supported) ? this._issuer.code_challenge_methods_supported : false;\n        if (supportedMethods && supportedMethods.includes(\"S256\")) {\n            this._usePKCE = \"S256\";\n        } else if (supportedMethods && supportedMethods.includes(\"plain\")) {\n            this._usePKCE = \"plain\";\n        } else if (supportedMethods) {\n            throw new TypeError(\"neither code_challenge_method supported by the client is supported by the issuer\");\n        } else {\n            this._usePKCE = \"S256\";\n        }\n    } else if (typeof this._usePKCE === \"string\" && ![\n        \"plain\",\n        \"S256\"\n    ].includes(this._usePKCE)) {\n        throw new TypeError(`${this._usePKCE} is not valid/implemented PKCE code_challenge_method`);\n    }\n    this.name = url.parse(client.issuer.issuer).hostname;\n}\nOpenIDConnectStrategy.prototype.authenticate = function authenticate(req, options) {\n    (async ()=>{\n        const client = this._client;\n        if (!req.session) {\n            throw new TypeError(\"authentication requires session support\");\n        }\n        const reqParams = client.callbackParams(req);\n        const sessionKey = this._key;\n        const { 0: parameter, length } = Object.keys(reqParams);\n        /**\n     * Start authentication request if this has no authorization response parameters or\n     * this might a login initiated from a third party as per\n     * https://openid.net/specs/openid-connect-core-1_0.html#ThirdPartyInitiatedLogin.\n     */ if (length === 0 || length === 1 && parameter === \"iss\") {\n            // provide options object with extra authentication parameters\n            const params = {\n                state: random(),\n                ...this._params,\n                ...options\n            };\n            if (!params.nonce && params.response_type.includes(\"id_token\")) {\n                params.nonce = random();\n            }\n            req.session[sessionKey] = pick(params, \"nonce\", \"state\", \"max_age\", \"response_type\");\n            if (this._usePKCE && params.response_type.includes(\"code\")) {\n                const verifier = random();\n                req.session[sessionKey].code_verifier = verifier;\n                switch(this._usePKCE){\n                    case \"S256\":\n                        params.code_challenge = codeChallenge(verifier);\n                        params.code_challenge_method = \"S256\";\n                        break;\n                    case \"plain\":\n                        params.code_challenge = verifier;\n                        break;\n                }\n            }\n            this.redirect(client.authorizationUrl(params));\n            return;\n        }\n        /* end authentication request */ /* start authentication response */ const session = req.session[sessionKey];\n        if (Object.keys(session || {}).length === 0) {\n            throw new Error(format('did not find expected authorization request details in session, req.session[\"%s\"] is %j', sessionKey, session));\n        }\n        const { state, nonce, max_age: maxAge, code_verifier: codeVerifier, response_type: responseType } = session;\n        try {\n            delete req.session[sessionKey];\n        } catch (err) {}\n        const opts = {\n            redirect_uri: this._params.redirect_uri,\n            ...options\n        };\n        const checks = {\n            state,\n            nonce,\n            max_age: maxAge,\n            code_verifier: codeVerifier,\n            response_type: responseType\n        };\n        const tokenset = await client.callback(opts.redirect_uri, reqParams, checks, this._extras);\n        const passReq = this._passReqToCallback;\n        const loadUserinfo = this._verify.length > (passReq ? 3 : 2) && client.issuer.userinfo_endpoint;\n        const args = [\n            tokenset,\n            verified.bind(this)\n        ];\n        if (loadUserinfo) {\n            if (!tokenset.access_token) {\n                throw new RPError({\n                    message: \"expected access_token to be returned when asking for userinfo in verify callback\",\n                    tokenset\n                });\n            }\n            const userinfo = await client.userinfo(tokenset);\n            args.splice(1, 0, userinfo);\n        }\n        if (passReq) {\n            args.unshift(req);\n        }\n        this._verify(...args);\n    /* end authentication response */ })().catch((error)=>{\n        if (error instanceof OPError && error.error !== \"server_error\" && !error.error.startsWith(\"invalid\") || error instanceof RPError) {\n            this.fail(error);\n        } else {\n            this.error(error);\n        }\n    });\n};\nmodule.exports = OpenIDConnectStrategy;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/openid-client/lib/passport_strategy.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/openid-client/lib/token_set.js":
/*!*****************************************************!*\
  !*** ./node_modules/openid-client/lib/token_set.js ***!
  \*****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst base64url = __webpack_require__(/*! ./helpers/base64url */ \"(ssr)/./node_modules/openid-client/lib/helpers/base64url.js\");\nconst now = __webpack_require__(/*! ./helpers/unix_timestamp */ \"(ssr)/./node_modules/openid-client/lib/helpers/unix_timestamp.js\");\nclass TokenSet {\n    constructor(values){\n        Object.assign(this, values);\n        const { constructor, ...properties } = Object.getOwnPropertyDescriptors(this.constructor.prototype);\n        Object.defineProperties(this, properties);\n    }\n    set expires_in(value) {\n        this.expires_at = now() + Number(value);\n    }\n    get expires_in() {\n        return Math.max.apply(null, [\n            this.expires_at - now(),\n            0\n        ]);\n    }\n    expired() {\n        return this.expires_in === 0;\n    }\n    claims() {\n        if (!this.id_token) {\n            throw new TypeError(\"id_token not present in TokenSet\");\n        }\n        return JSON.parse(base64url.decode(this.id_token.split(\".\")[1]));\n    }\n}\nmodule.exports = TokenSet;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvdG9rZW5fc2V0LmpzIiwibWFwcGluZ3MiOiI7QUFBQSxNQUFNQSxZQUFZQyxtQkFBT0EsQ0FBQztBQUMxQixNQUFNQyxNQUFNRCxtQkFBT0EsQ0FBQztBQUVwQixNQUFNRTtJQUNKQyxZQUFZQyxNQUFNLENBQUU7UUFDbEJDLE9BQU9DLE1BQU0sQ0FBQyxJQUFJLEVBQUVGO1FBQ3BCLE1BQU0sRUFBRUQsV0FBVyxFQUFFLEdBQUdJLFlBQVksR0FBR0YsT0FBT0cseUJBQXlCLENBQ3JFLElBQUksQ0FBQ0wsV0FBVyxDQUFDTSxTQUFTO1FBRzVCSixPQUFPSyxnQkFBZ0IsQ0FBQyxJQUFJLEVBQUVIO0lBQ2hDO0lBRUEsSUFBSUksV0FBV0MsS0FBSyxFQUFFO1FBQ3BCLElBQUksQ0FBQ0MsVUFBVSxHQUFHWixRQUFRYSxPQUFPRjtJQUNuQztJQUVBLElBQUlELGFBQWE7UUFDZixPQUFPSSxLQUFLQyxHQUFHLENBQUNDLEtBQUssQ0FBQyxNQUFNO1lBQUMsSUFBSSxDQUFDSixVQUFVLEdBQUdaO1lBQU87U0FBRTtJQUMxRDtJQUVBaUIsVUFBVTtRQUNSLE9BQU8sSUFBSSxDQUFDUCxVQUFVLEtBQUs7SUFDN0I7SUFFQVEsU0FBUztRQUNQLElBQUksQ0FBQyxJQUFJLENBQUNDLFFBQVEsRUFBRTtZQUNsQixNQUFNLElBQUlDLFVBQVU7UUFDdEI7UUFFQSxPQUFPQyxLQUFLQyxLQUFLLENBQUN4QixVQUFVeUIsTUFBTSxDQUFDLElBQUksQ0FBQ0osUUFBUSxDQUFDSyxLQUFLLENBQUMsSUFBSSxDQUFDLEVBQUU7SUFDaEU7QUFDRjtBQUVBQyxPQUFPQyxPQUFPLEdBQUd6QiIsInNvdXJjZXMiOlsid2VicGFjazovL21hcmtldHBsYWNlLy4vbm9kZV9tb2R1bGVzL29wZW5pZC1jbGllbnQvbGliL3Rva2VuX3NldC5qcz8yODNiIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IGJhc2U2NHVybCA9IHJlcXVpcmUoJy4vaGVscGVycy9iYXNlNjR1cmwnKTtcbmNvbnN0IG5vdyA9IHJlcXVpcmUoJy4vaGVscGVycy91bml4X3RpbWVzdGFtcCcpO1xuXG5jbGFzcyBUb2tlblNldCB7XG4gIGNvbnN0cnVjdG9yKHZhbHVlcykge1xuICAgIE9iamVjdC5hc3NpZ24odGhpcywgdmFsdWVzKTtcbiAgICBjb25zdCB7IGNvbnN0cnVjdG9yLCAuLi5wcm9wZXJ0aWVzIH0gPSBPYmplY3QuZ2V0T3duUHJvcGVydHlEZXNjcmlwdG9ycyhcbiAgICAgIHRoaXMuY29uc3RydWN0b3IucHJvdG90eXBlLFxuICAgICk7XG5cbiAgICBPYmplY3QuZGVmaW5lUHJvcGVydGllcyh0aGlzLCBwcm9wZXJ0aWVzKTtcbiAgfVxuXG4gIHNldCBleHBpcmVzX2luKHZhbHVlKSB7XG4gICAgdGhpcy5leHBpcmVzX2F0ID0gbm93KCkgKyBOdW1iZXIodmFsdWUpO1xuICB9XG5cbiAgZ2V0IGV4cGlyZXNfaW4oKSB7XG4gICAgcmV0dXJuIE1hdGgubWF4LmFwcGx5KG51bGwsIFt0aGlzLmV4cGlyZXNfYXQgLSBub3coKSwgMF0pO1xuICB9XG5cbiAgZXhwaXJlZCgpIHtcbiAgICByZXR1cm4gdGhpcy5leHBpcmVzX2luID09PSAwO1xuICB9XG5cbiAgY2xhaW1zKCkge1xuICAgIGlmICghdGhpcy5pZF90b2tlbikge1xuICAgICAgdGhyb3cgbmV3IFR5cGVFcnJvcignaWRfdG9rZW4gbm90IHByZXNlbnQgaW4gVG9rZW5TZXQnKTtcbiAgICB9XG5cbiAgICByZXR1cm4gSlNPTi5wYXJzZShiYXNlNjR1cmwuZGVjb2RlKHRoaXMuaWRfdG9rZW4uc3BsaXQoJy4nKVsxXSkpO1xuICB9XG59XG5cbm1vZHVsZS5leHBvcnRzID0gVG9rZW5TZXQ7XG4iXSwibmFtZXMiOlsiYmFzZTY0dXJsIiwicmVxdWlyZSIsIm5vdyIsIlRva2VuU2V0IiwiY29uc3RydWN0b3IiLCJ2YWx1ZXMiLCJPYmplY3QiLCJhc3NpZ24iLCJwcm9wZXJ0aWVzIiwiZ2V0T3duUHJvcGVydHlEZXNjcmlwdG9ycyIsInByb3RvdHlwZSIsImRlZmluZVByb3BlcnRpZXMiLCJleHBpcmVzX2luIiwidmFsdWUiLCJleHBpcmVzX2F0IiwiTnVtYmVyIiwiTWF0aCIsIm1heCIsImFwcGx5IiwiZXhwaXJlZCIsImNsYWltcyIsImlkX3Rva2VuIiwiVHlwZUVycm9yIiwiSlNPTiIsInBhcnNlIiwiZGVjb2RlIiwic3BsaXQiLCJtb2R1bGUiLCJleHBvcnRzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/openid-client/lib/token_set.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/openid-client/node_modules/lru-cache/index.js":
/*!********************************************************************!*\
  !*** ./node_modules/openid-client/node_modules/lru-cache/index.js ***!
  \********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n// A linked list to keep track of recently-used-ness\nconst Yallist = __webpack_require__(/*! yallist */ \"(ssr)/./node_modules/yallist/yallist.js\");\nconst MAX = Symbol(\"max\");\nconst LENGTH = Symbol(\"length\");\nconst LENGTH_CALCULATOR = Symbol(\"lengthCalculator\");\nconst ALLOW_STALE = Symbol(\"allowStale\");\nconst MAX_AGE = Symbol(\"maxAge\");\nconst DISPOSE = Symbol(\"dispose\");\nconst NO_DISPOSE_ON_SET = Symbol(\"noDisposeOnSet\");\nconst LRU_LIST = Symbol(\"lruList\");\nconst CACHE = Symbol(\"cache\");\nconst UPDATE_AGE_ON_GET = Symbol(\"updateAgeOnGet\");\nconst naiveLength = ()=>1;\n// lruList is a yallist where the head is the youngest\n// item, and the tail is the oldest.  the list contains the Hit\n// objects as the entries.\n// Each Hit object has a reference to its Yallist.Node.  This\n// never changes.\n//\n// cache is a Map (or PseudoMap) that matches the keys to\n// the Yallist.Node object.\nclass LRUCache {\n    constructor(options){\n        if (typeof options === \"number\") options = {\n            max: options\n        };\n        if (!options) options = {};\n        if (options.max && (typeof options.max !== \"number\" || options.max < 0)) throw new TypeError(\"max must be a non-negative number\");\n        // Kind of weird to have a default max of Infinity, but oh well.\n        const max = this[MAX] = options.max || Infinity;\n        const lc = options.length || naiveLength;\n        this[LENGTH_CALCULATOR] = typeof lc !== \"function\" ? naiveLength : lc;\n        this[ALLOW_STALE] = options.stale || false;\n        if (options.maxAge && typeof options.maxAge !== \"number\") throw new TypeError(\"maxAge must be a number\");\n        this[MAX_AGE] = options.maxAge || 0;\n        this[DISPOSE] = options.dispose;\n        this[NO_DISPOSE_ON_SET] = options.noDisposeOnSet || false;\n        this[UPDATE_AGE_ON_GET] = options.updateAgeOnGet || false;\n        this.reset();\n    }\n    // resize the cache when the max changes.\n    set max(mL) {\n        if (typeof mL !== \"number\" || mL < 0) throw new TypeError(\"max must be a non-negative number\");\n        this[MAX] = mL || Infinity;\n        trim(this);\n    }\n    get max() {\n        return this[MAX];\n    }\n    set allowStale(allowStale) {\n        this[ALLOW_STALE] = !!allowStale;\n    }\n    get allowStale() {\n        return this[ALLOW_STALE];\n    }\n    set maxAge(mA) {\n        if (typeof mA !== \"number\") throw new TypeError(\"maxAge must be a non-negative number\");\n        this[MAX_AGE] = mA;\n        trim(this);\n    }\n    get maxAge() {\n        return this[MAX_AGE];\n    }\n    // resize the cache when the lengthCalculator changes.\n    set lengthCalculator(lC) {\n        if (typeof lC !== \"function\") lC = naiveLength;\n        if (lC !== this[LENGTH_CALCULATOR]) {\n            this[LENGTH_CALCULATOR] = lC;\n            this[LENGTH] = 0;\n            this[LRU_LIST].forEach((hit)=>{\n                hit.length = this[LENGTH_CALCULATOR](hit.value, hit.key);\n                this[LENGTH] += hit.length;\n            });\n        }\n        trim(this);\n    }\n    get lengthCalculator() {\n        return this[LENGTH_CALCULATOR];\n    }\n    get length() {\n        return this[LENGTH];\n    }\n    get itemCount() {\n        return this[LRU_LIST].length;\n    }\n    rforEach(fn, thisp) {\n        thisp = thisp || this;\n        for(let walker = this[LRU_LIST].tail; walker !== null;){\n            const prev = walker.prev;\n            forEachStep(this, fn, walker, thisp);\n            walker = prev;\n        }\n    }\n    forEach(fn, thisp) {\n        thisp = thisp || this;\n        for(let walker = this[LRU_LIST].head; walker !== null;){\n            const next = walker.next;\n            forEachStep(this, fn, walker, thisp);\n            walker = next;\n        }\n    }\n    keys() {\n        return this[LRU_LIST].toArray().map((k)=>k.key);\n    }\n    values() {\n        return this[LRU_LIST].toArray().map((k)=>k.value);\n    }\n    reset() {\n        if (this[DISPOSE] && this[LRU_LIST] && this[LRU_LIST].length) {\n            this[LRU_LIST].forEach((hit)=>this[DISPOSE](hit.key, hit.value));\n        }\n        this[CACHE] = new Map() // hash of items by key\n        ;\n        this[LRU_LIST] = new Yallist() // list of items in order of use recency\n        ;\n        this[LENGTH] = 0 // length of items in the list\n        ;\n    }\n    dump() {\n        return this[LRU_LIST].map((hit)=>isStale(this, hit) ? false : {\n                k: hit.key,\n                v: hit.value,\n                e: hit.now + (hit.maxAge || 0)\n            }).toArray().filter((h)=>h);\n    }\n    dumpLru() {\n        return this[LRU_LIST];\n    }\n    set(key, value, maxAge) {\n        maxAge = maxAge || this[MAX_AGE];\n        if (maxAge && typeof maxAge !== \"number\") throw new TypeError(\"maxAge must be a number\");\n        const now = maxAge ? Date.now() : 0;\n        const len = this[LENGTH_CALCULATOR](value, key);\n        if (this[CACHE].has(key)) {\n            if (len > this[MAX]) {\n                del(this, this[CACHE].get(key));\n                return false;\n            }\n            const node = this[CACHE].get(key);\n            const item = node.value;\n            // dispose of the old one before overwriting\n            // split out into 2 ifs for better coverage tracking\n            if (this[DISPOSE]) {\n                if (!this[NO_DISPOSE_ON_SET]) this[DISPOSE](key, item.value);\n            }\n            item.now = now;\n            item.maxAge = maxAge;\n            item.value = value;\n            this[LENGTH] += len - item.length;\n            item.length = len;\n            this.get(key);\n            trim(this);\n            return true;\n        }\n        const hit = new Entry(key, value, len, now, maxAge);\n        // oversized objects fall out of cache automatically.\n        if (hit.length > this[MAX]) {\n            if (this[DISPOSE]) this[DISPOSE](key, value);\n            return false;\n        }\n        this[LENGTH] += hit.length;\n        this[LRU_LIST].unshift(hit);\n        this[CACHE].set(key, this[LRU_LIST].head);\n        trim(this);\n        return true;\n    }\n    has(key) {\n        if (!this[CACHE].has(key)) return false;\n        const hit = this[CACHE].get(key).value;\n        return !isStale(this, hit);\n    }\n    get(key) {\n        return get(this, key, true);\n    }\n    peek(key) {\n        return get(this, key, false);\n    }\n    pop() {\n        const node = this[LRU_LIST].tail;\n        if (!node) return null;\n        del(this, node);\n        return node.value;\n    }\n    del(key) {\n        del(this, this[CACHE].get(key));\n    }\n    load(arr) {\n        // reset the cache\n        this.reset();\n        const now = Date.now();\n        // A previous serialized cache has the most recent items first\n        for(let l = arr.length - 1; l >= 0; l--){\n            const hit = arr[l];\n            const expiresAt = hit.e || 0;\n            if (expiresAt === 0) // the item was created without expiration in a non aged cache\n            this.set(hit.k, hit.v);\n            else {\n                const maxAge = expiresAt - now;\n                // dont add already expired items\n                if (maxAge > 0) {\n                    this.set(hit.k, hit.v, maxAge);\n                }\n            }\n        }\n    }\n    prune() {\n        this[CACHE].forEach((value, key)=>get(this, key, false));\n    }\n}\nconst get = (self, key, doUse)=>{\n    const node = self[CACHE].get(key);\n    if (node) {\n        const hit = node.value;\n        if (isStale(self, hit)) {\n            del(self, node);\n            if (!self[ALLOW_STALE]) return undefined;\n        } else {\n            if (doUse) {\n                if (self[UPDATE_AGE_ON_GET]) node.value.now = Date.now();\n                self[LRU_LIST].unshiftNode(node);\n            }\n        }\n        return hit.value;\n    }\n};\nconst isStale = (self, hit)=>{\n    if (!hit || !hit.maxAge && !self[MAX_AGE]) return false;\n    const diff = Date.now() - hit.now;\n    return hit.maxAge ? diff > hit.maxAge : self[MAX_AGE] && diff > self[MAX_AGE];\n};\nconst trim = (self)=>{\n    if (self[LENGTH] > self[MAX]) {\n        for(let walker = self[LRU_LIST].tail; self[LENGTH] > self[MAX] && walker !== null;){\n            // We know that we're about to delete this one, and also\n            // what the next least recently used key will be, so just\n            // go ahead and set it now.\n            const prev = walker.prev;\n            del(self, walker);\n            walker = prev;\n        }\n    }\n};\nconst del = (self, node)=>{\n    if (node) {\n        const hit = node.value;\n        if (self[DISPOSE]) self[DISPOSE](hit.key, hit.value);\n        self[LENGTH] -= hit.length;\n        self[CACHE].delete(hit.key);\n        self[LRU_LIST].removeNode(node);\n    }\n};\nclass Entry {\n    constructor(key, value, length, now, maxAge){\n        this.key = key;\n        this.value = value;\n        this.length = length;\n        this.now = now;\n        this.maxAge = maxAge || 0;\n    }\n}\nconst forEachStep = (self, fn, node, thisp)=>{\n    let hit = node.value;\n    if (isStale(self, hit)) {\n        del(self, node);\n        if (!self[ALLOW_STALE]) hit = undefined;\n    }\n    if (hit) fn.call(thisp, hit.value, hit.key, self);\n};\nmodule.exports = LRUCache;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9ub2RlX21vZHVsZXMvbHJ1LWNhY2hlL2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBRUEsb0RBQW9EO0FBQ3BELE1BQU1BLFVBQVVDLG1CQUFPQSxDQUFDO0FBRXhCLE1BQU1DLE1BQU1DLE9BQU87QUFDbkIsTUFBTUMsU0FBU0QsT0FBTztBQUN0QixNQUFNRSxvQkFBb0JGLE9BQU87QUFDakMsTUFBTUcsY0FBY0gsT0FBTztBQUMzQixNQUFNSSxVQUFVSixPQUFPO0FBQ3ZCLE1BQU1LLFVBQVVMLE9BQU87QUFDdkIsTUFBTU0sb0JBQW9CTixPQUFPO0FBQ2pDLE1BQU1PLFdBQVdQLE9BQU87QUFDeEIsTUFBTVEsUUFBUVIsT0FBTztBQUNyQixNQUFNUyxvQkFBb0JULE9BQU87QUFFakMsTUFBTVUsY0FBYyxJQUFNO0FBRTFCLHNEQUFzRDtBQUN0RCwrREFBK0Q7QUFDL0QsMEJBQTBCO0FBQzFCLDZEQUE2RDtBQUM3RCxpQkFBaUI7QUFDakIsRUFBRTtBQUNGLHlEQUF5RDtBQUN6RCwyQkFBMkI7QUFDM0IsTUFBTUM7SUFDSkMsWUFBYUMsT0FBTyxDQUFFO1FBQ3BCLElBQUksT0FBT0EsWUFBWSxVQUNyQkEsVUFBVTtZQUFFQyxLQUFLRDtRQUFRO1FBRTNCLElBQUksQ0FBQ0EsU0FDSEEsVUFBVSxDQUFDO1FBRWIsSUFBSUEsUUFBUUMsR0FBRyxJQUFLLFFBQU9ELFFBQVFDLEdBQUcsS0FBSyxZQUFZRCxRQUFRQyxHQUFHLEdBQUcsSUFDbkUsTUFBTSxJQUFJQyxVQUFVO1FBQ3RCLGdFQUFnRTtRQUNoRSxNQUFNRCxNQUFNLElBQUksQ0FBQ2YsSUFBSSxHQUFHYyxRQUFRQyxHQUFHLElBQUlFO1FBRXZDLE1BQU1DLEtBQUtKLFFBQVFLLE1BQU0sSUFBSVI7UUFDN0IsSUFBSSxDQUFDUixrQkFBa0IsR0FBRyxPQUFRZSxPQUFPLGFBQWNQLGNBQWNPO1FBQ3JFLElBQUksQ0FBQ2QsWUFBWSxHQUFHVSxRQUFRTSxLQUFLLElBQUk7UUFDckMsSUFBSU4sUUFBUU8sTUFBTSxJQUFJLE9BQU9QLFFBQVFPLE1BQU0sS0FBSyxVQUM5QyxNQUFNLElBQUlMLFVBQVU7UUFDdEIsSUFBSSxDQUFDWCxRQUFRLEdBQUdTLFFBQVFPLE1BQU0sSUFBSTtRQUNsQyxJQUFJLENBQUNmLFFBQVEsR0FBR1EsUUFBUVEsT0FBTztRQUMvQixJQUFJLENBQUNmLGtCQUFrQixHQUFHTyxRQUFRUyxjQUFjLElBQUk7UUFDcEQsSUFBSSxDQUFDYixrQkFBa0IsR0FBR0ksUUFBUVUsY0FBYyxJQUFJO1FBQ3BELElBQUksQ0FBQ0MsS0FBSztJQUNaO0lBRUEseUNBQXlDO0lBQ3pDLElBQUlWLElBQUtXLEVBQUUsRUFBRTtRQUNYLElBQUksT0FBT0EsT0FBTyxZQUFZQSxLQUFLLEdBQ2pDLE1BQU0sSUFBSVYsVUFBVTtRQUV0QixJQUFJLENBQUNoQixJQUFJLEdBQUcwQixNQUFNVDtRQUNsQlUsS0FBSyxJQUFJO0lBQ1g7SUFDQSxJQUFJWixNQUFPO1FBQ1QsT0FBTyxJQUFJLENBQUNmLElBQUk7SUFDbEI7SUFFQSxJQUFJNEIsV0FBWUEsVUFBVSxFQUFFO1FBQzFCLElBQUksQ0FBQ3hCLFlBQVksR0FBRyxDQUFDLENBQUN3QjtJQUN4QjtJQUNBLElBQUlBLGFBQWM7UUFDaEIsT0FBTyxJQUFJLENBQUN4QixZQUFZO0lBQzFCO0lBRUEsSUFBSWlCLE9BQVFRLEVBQUUsRUFBRTtRQUNkLElBQUksT0FBT0EsT0FBTyxVQUNoQixNQUFNLElBQUliLFVBQVU7UUFFdEIsSUFBSSxDQUFDWCxRQUFRLEdBQUd3QjtRQUNoQkYsS0FBSyxJQUFJO0lBQ1g7SUFDQSxJQUFJTixTQUFVO1FBQ1osT0FBTyxJQUFJLENBQUNoQixRQUFRO0lBQ3RCO0lBRUEsc0RBQXNEO0lBQ3RELElBQUl5QixpQkFBa0JDLEVBQUUsRUFBRTtRQUN4QixJQUFJLE9BQU9BLE9BQU8sWUFDaEJBLEtBQUtwQjtRQUVQLElBQUlvQixPQUFPLElBQUksQ0FBQzVCLGtCQUFrQixFQUFFO1lBQ2xDLElBQUksQ0FBQ0Esa0JBQWtCLEdBQUc0QjtZQUMxQixJQUFJLENBQUM3QixPQUFPLEdBQUc7WUFDZixJQUFJLENBQUNNLFNBQVMsQ0FBQ3dCLE9BQU8sQ0FBQ0MsQ0FBQUE7Z0JBQ3JCQSxJQUFJZCxNQUFNLEdBQUcsSUFBSSxDQUFDaEIsa0JBQWtCLENBQUM4QixJQUFJQyxLQUFLLEVBQUVELElBQUlFLEdBQUc7Z0JBQ3ZELElBQUksQ0FBQ2pDLE9BQU8sSUFBSStCLElBQUlkLE1BQU07WUFDNUI7UUFDRjtRQUNBUSxLQUFLLElBQUk7SUFDWDtJQUNBLElBQUlHLG1CQUFvQjtRQUFFLE9BQU8sSUFBSSxDQUFDM0Isa0JBQWtCO0lBQUM7SUFFekQsSUFBSWdCLFNBQVU7UUFBRSxPQUFPLElBQUksQ0FBQ2pCLE9BQU87SUFBQztJQUNwQyxJQUFJa0MsWUFBYTtRQUFFLE9BQU8sSUFBSSxDQUFDNUIsU0FBUyxDQUFDVyxNQUFNO0lBQUM7SUFFaERrQixTQUFVQyxFQUFFLEVBQUVDLEtBQUssRUFBRTtRQUNuQkEsUUFBUUEsU0FBUyxJQUFJO1FBQ3JCLElBQUssSUFBSUMsU0FBUyxJQUFJLENBQUNoQyxTQUFTLENBQUNpQyxJQUFJLEVBQUVELFdBQVcsTUFBTztZQUN2RCxNQUFNRSxPQUFPRixPQUFPRSxJQUFJO1lBQ3hCQyxZQUFZLElBQUksRUFBRUwsSUFBSUUsUUFBUUQ7WUFDOUJDLFNBQVNFO1FBQ1g7SUFDRjtJQUVBVixRQUFTTSxFQUFFLEVBQUVDLEtBQUssRUFBRTtRQUNsQkEsUUFBUUEsU0FBUyxJQUFJO1FBQ3JCLElBQUssSUFBSUMsU0FBUyxJQUFJLENBQUNoQyxTQUFTLENBQUNvQyxJQUFJLEVBQUVKLFdBQVcsTUFBTztZQUN2RCxNQUFNSyxPQUFPTCxPQUFPSyxJQUFJO1lBQ3hCRixZQUFZLElBQUksRUFBRUwsSUFBSUUsUUFBUUQ7WUFDOUJDLFNBQVNLO1FBQ1g7SUFDRjtJQUVBQyxPQUFRO1FBQ04sT0FBTyxJQUFJLENBQUN0QyxTQUFTLENBQUN1QyxPQUFPLEdBQUdDLEdBQUcsQ0FBQ0MsQ0FBQUEsSUFBS0EsRUFBRWQsR0FBRztJQUNoRDtJQUVBZSxTQUFVO1FBQ1IsT0FBTyxJQUFJLENBQUMxQyxTQUFTLENBQUN1QyxPQUFPLEdBQUdDLEdBQUcsQ0FBQ0MsQ0FBQUEsSUFBS0EsRUFBRWYsS0FBSztJQUNsRDtJQUVBVCxRQUFTO1FBQ1AsSUFBSSxJQUFJLENBQUNuQixRQUFRLElBQ2IsSUFBSSxDQUFDRSxTQUFTLElBQ2QsSUFBSSxDQUFDQSxTQUFTLENBQUNXLE1BQU0sRUFBRTtZQUN6QixJQUFJLENBQUNYLFNBQVMsQ0FBQ3dCLE9BQU8sQ0FBQ0MsQ0FBQUEsTUFBTyxJQUFJLENBQUMzQixRQUFRLENBQUMyQixJQUFJRSxHQUFHLEVBQUVGLElBQUlDLEtBQUs7UUFDaEU7UUFFQSxJQUFJLENBQUN6QixNQUFNLEdBQUcsSUFBSTBDLE1BQU0sdUJBQXVCOztRQUMvQyxJQUFJLENBQUMzQyxTQUFTLEdBQUcsSUFBSVYsVUFBVSx3Q0FBd0M7O1FBQ3ZFLElBQUksQ0FBQ0ksT0FBTyxHQUFHLEVBQUUsOEJBQThCOztJQUNqRDtJQUVBa0QsT0FBUTtRQUNOLE9BQU8sSUFBSSxDQUFDNUMsU0FBUyxDQUFDd0MsR0FBRyxDQUFDZixDQUFBQSxNQUN4Qm9CLFFBQVEsSUFBSSxFQUFFcEIsT0FBTyxRQUFRO2dCQUMzQmdCLEdBQUdoQixJQUFJRSxHQUFHO2dCQUNWbUIsR0FBR3JCLElBQUlDLEtBQUs7Z0JBQ1pxQixHQUFHdEIsSUFBSXVCLEdBQUcsR0FBSXZCLENBQUFBLElBQUlaLE1BQU0sSUFBSTtZQUM5QixHQUFHMEIsT0FBTyxHQUFHVSxNQUFNLENBQUNDLENBQUFBLElBQUtBO0lBQzdCO0lBRUFDLFVBQVc7UUFDVCxPQUFPLElBQUksQ0FBQ25ELFNBQVM7SUFDdkI7SUFFQW9ELElBQUt6QixHQUFHLEVBQUVELEtBQUssRUFBRWIsTUFBTSxFQUFFO1FBQ3ZCQSxTQUFTQSxVQUFVLElBQUksQ0FBQ2hCLFFBQVE7UUFFaEMsSUFBSWdCLFVBQVUsT0FBT0EsV0FBVyxVQUM5QixNQUFNLElBQUlMLFVBQVU7UUFFdEIsTUFBTXdDLE1BQU1uQyxTQUFTd0MsS0FBS0wsR0FBRyxLQUFLO1FBQ2xDLE1BQU1NLE1BQU0sSUFBSSxDQUFDM0Qsa0JBQWtCLENBQUMrQixPQUFPQztRQUUzQyxJQUFJLElBQUksQ0FBQzFCLE1BQU0sQ0FBQ3NELEdBQUcsQ0FBQzVCLE1BQU07WUFDeEIsSUFBSTJCLE1BQU0sSUFBSSxDQUFDOUQsSUFBSSxFQUFFO2dCQUNuQmdFLElBQUksSUFBSSxFQUFFLElBQUksQ0FBQ3ZELE1BQU0sQ0FBQ3dELEdBQUcsQ0FBQzlCO2dCQUMxQixPQUFPO1lBQ1Q7WUFFQSxNQUFNK0IsT0FBTyxJQUFJLENBQUN6RCxNQUFNLENBQUN3RCxHQUFHLENBQUM5QjtZQUM3QixNQUFNZ0MsT0FBT0QsS0FBS2hDLEtBQUs7WUFFdkIsNENBQTRDO1lBQzVDLG9EQUFvRDtZQUNwRCxJQUFJLElBQUksQ0FBQzVCLFFBQVEsRUFBRTtnQkFDakIsSUFBSSxDQUFDLElBQUksQ0FBQ0Msa0JBQWtCLEVBQzFCLElBQUksQ0FBQ0QsUUFBUSxDQUFDNkIsS0FBS2dDLEtBQUtqQyxLQUFLO1lBQ2pDO1lBRUFpQyxLQUFLWCxHQUFHLEdBQUdBO1lBQ1hXLEtBQUs5QyxNQUFNLEdBQUdBO1lBQ2Q4QyxLQUFLakMsS0FBSyxHQUFHQTtZQUNiLElBQUksQ0FBQ2hDLE9BQU8sSUFBSTRELE1BQU1LLEtBQUtoRCxNQUFNO1lBQ2pDZ0QsS0FBS2hELE1BQU0sR0FBRzJDO1lBQ2QsSUFBSSxDQUFDRyxHQUFHLENBQUM5QjtZQUNUUixLQUFLLElBQUk7WUFDVCxPQUFPO1FBQ1Q7UUFFQSxNQUFNTSxNQUFNLElBQUltQyxNQUFNakMsS0FBS0QsT0FBTzRCLEtBQUtOLEtBQUtuQztRQUU1QyxxREFBcUQ7UUFDckQsSUFBSVksSUFBSWQsTUFBTSxHQUFHLElBQUksQ0FBQ25CLElBQUksRUFBRTtZQUMxQixJQUFJLElBQUksQ0FBQ00sUUFBUSxFQUNmLElBQUksQ0FBQ0EsUUFBUSxDQUFDNkIsS0FBS0Q7WUFFckIsT0FBTztRQUNUO1FBRUEsSUFBSSxDQUFDaEMsT0FBTyxJQUFJK0IsSUFBSWQsTUFBTTtRQUMxQixJQUFJLENBQUNYLFNBQVMsQ0FBQzZELE9BQU8sQ0FBQ3BDO1FBQ3ZCLElBQUksQ0FBQ3hCLE1BQU0sQ0FBQ21ELEdBQUcsQ0FBQ3pCLEtBQUssSUFBSSxDQUFDM0IsU0FBUyxDQUFDb0MsSUFBSTtRQUN4Q2pCLEtBQUssSUFBSTtRQUNULE9BQU87SUFDVDtJQUVBb0MsSUFBSzVCLEdBQUcsRUFBRTtRQUNSLElBQUksQ0FBQyxJQUFJLENBQUMxQixNQUFNLENBQUNzRCxHQUFHLENBQUM1QixNQUFNLE9BQU87UUFDbEMsTUFBTUYsTUFBTSxJQUFJLENBQUN4QixNQUFNLENBQUN3RCxHQUFHLENBQUM5QixLQUFLRCxLQUFLO1FBQ3RDLE9BQU8sQ0FBQ21CLFFBQVEsSUFBSSxFQUFFcEI7SUFDeEI7SUFFQWdDLElBQUs5QixHQUFHLEVBQUU7UUFDUixPQUFPOEIsSUFBSSxJQUFJLEVBQUU5QixLQUFLO0lBQ3hCO0lBRUFtQyxLQUFNbkMsR0FBRyxFQUFFO1FBQ1QsT0FBTzhCLElBQUksSUFBSSxFQUFFOUIsS0FBSztJQUN4QjtJQUVBb0MsTUFBTztRQUNMLE1BQU1MLE9BQU8sSUFBSSxDQUFDMUQsU0FBUyxDQUFDaUMsSUFBSTtRQUNoQyxJQUFJLENBQUN5QixNQUNILE9BQU87UUFFVEYsSUFBSSxJQUFJLEVBQUVFO1FBQ1YsT0FBT0EsS0FBS2hDLEtBQUs7SUFDbkI7SUFFQThCLElBQUs3QixHQUFHLEVBQUU7UUFDUjZCLElBQUksSUFBSSxFQUFFLElBQUksQ0FBQ3ZELE1BQU0sQ0FBQ3dELEdBQUcsQ0FBQzlCO0lBQzVCO0lBRUFxQyxLQUFNQyxHQUFHLEVBQUU7UUFDVCxrQkFBa0I7UUFDbEIsSUFBSSxDQUFDaEQsS0FBSztRQUVWLE1BQU0rQixNQUFNSyxLQUFLTCxHQUFHO1FBQ3BCLDhEQUE4RDtRQUM5RCxJQUFLLElBQUlrQixJQUFJRCxJQUFJdEQsTUFBTSxHQUFHLEdBQUd1RCxLQUFLLEdBQUdBLElBQUs7WUFDeEMsTUFBTXpDLE1BQU13QyxHQUFHLENBQUNDLEVBQUU7WUFDbEIsTUFBTUMsWUFBWTFDLElBQUlzQixDQUFDLElBQUk7WUFDM0IsSUFBSW9CLGNBQWMsR0FDaEIsOERBQThEO1lBQzlELElBQUksQ0FBQ2YsR0FBRyxDQUFDM0IsSUFBSWdCLENBQUMsRUFBRWhCLElBQUlxQixDQUFDO2lCQUNsQjtnQkFDSCxNQUFNakMsU0FBU3NELFlBQVluQjtnQkFDM0IsaUNBQWlDO2dCQUNqQyxJQUFJbkMsU0FBUyxHQUFHO29CQUNkLElBQUksQ0FBQ3VDLEdBQUcsQ0FBQzNCLElBQUlnQixDQUFDLEVBQUVoQixJQUFJcUIsQ0FBQyxFQUFFakM7Z0JBQ3pCO1lBQ0Y7UUFDRjtJQUNGO0lBRUF1RCxRQUFTO1FBQ1AsSUFBSSxDQUFDbkUsTUFBTSxDQUFDdUIsT0FBTyxDQUFDLENBQUNFLE9BQU9DLE1BQVE4QixJQUFJLElBQUksRUFBRTlCLEtBQUs7SUFDckQ7QUFDRjtBQUVBLE1BQU04QixNQUFNLENBQUNZLE1BQU0xQyxLQUFLMkM7SUFDdEIsTUFBTVosT0FBT1csSUFBSSxDQUFDcEUsTUFBTSxDQUFDd0QsR0FBRyxDQUFDOUI7SUFDN0IsSUFBSStCLE1BQU07UUFDUixNQUFNakMsTUFBTWlDLEtBQUtoQyxLQUFLO1FBQ3RCLElBQUltQixRQUFRd0IsTUFBTTVDLE1BQU07WUFDdEIrQixJQUFJYSxNQUFNWDtZQUNWLElBQUksQ0FBQ1csSUFBSSxDQUFDekUsWUFBWSxFQUNwQixPQUFPMkU7UUFDWCxPQUFPO1lBQ0wsSUFBSUQsT0FBTztnQkFDVCxJQUFJRCxJQUFJLENBQUNuRSxrQkFBa0IsRUFDekJ3RCxLQUFLaEMsS0FBSyxDQUFDc0IsR0FBRyxHQUFHSyxLQUFLTCxHQUFHO2dCQUMzQnFCLElBQUksQ0FBQ3JFLFNBQVMsQ0FBQ3dFLFdBQVcsQ0FBQ2Q7WUFDN0I7UUFDRjtRQUNBLE9BQU9qQyxJQUFJQyxLQUFLO0lBQ2xCO0FBQ0Y7QUFFQSxNQUFNbUIsVUFBVSxDQUFDd0IsTUFBTTVDO0lBQ3JCLElBQUksQ0FBQ0EsT0FBUSxDQUFDQSxJQUFJWixNQUFNLElBQUksQ0FBQ3dELElBQUksQ0FBQ3hFLFFBQVEsRUFDeEMsT0FBTztJQUVULE1BQU00RSxPQUFPcEIsS0FBS0wsR0FBRyxLQUFLdkIsSUFBSXVCLEdBQUc7SUFDakMsT0FBT3ZCLElBQUlaLE1BQU0sR0FBRzRELE9BQU9oRCxJQUFJWixNQUFNLEdBQ2pDd0QsSUFBSSxDQUFDeEUsUUFBUSxJQUFLNEUsT0FBT0osSUFBSSxDQUFDeEUsUUFBUTtBQUM1QztBQUVBLE1BQU1zQixPQUFPa0QsQ0FBQUE7SUFDWCxJQUFJQSxJQUFJLENBQUMzRSxPQUFPLEdBQUcyRSxJQUFJLENBQUM3RSxJQUFJLEVBQUU7UUFDNUIsSUFBSyxJQUFJd0MsU0FBU3FDLElBQUksQ0FBQ3JFLFNBQVMsQ0FBQ2lDLElBQUksRUFDbkNvQyxJQUFJLENBQUMzRSxPQUFPLEdBQUcyRSxJQUFJLENBQUM3RSxJQUFJLElBQUl3QyxXQUFXLE1BQU87WUFDOUMsd0RBQXdEO1lBQ3hELHlEQUF5RDtZQUN6RCwyQkFBMkI7WUFDM0IsTUFBTUUsT0FBT0YsT0FBT0UsSUFBSTtZQUN4QnNCLElBQUlhLE1BQU1yQztZQUNWQSxTQUFTRTtRQUNYO0lBQ0Y7QUFDRjtBQUVBLE1BQU1zQixNQUFNLENBQUNhLE1BQU1YO0lBQ2pCLElBQUlBLE1BQU07UUFDUixNQUFNakMsTUFBTWlDLEtBQUtoQyxLQUFLO1FBQ3RCLElBQUkyQyxJQUFJLENBQUN2RSxRQUFRLEVBQ2Z1RSxJQUFJLENBQUN2RSxRQUFRLENBQUMyQixJQUFJRSxHQUFHLEVBQUVGLElBQUlDLEtBQUs7UUFFbEMyQyxJQUFJLENBQUMzRSxPQUFPLElBQUkrQixJQUFJZCxNQUFNO1FBQzFCMEQsSUFBSSxDQUFDcEUsTUFBTSxDQUFDeUUsTUFBTSxDQUFDakQsSUFBSUUsR0FBRztRQUMxQjBDLElBQUksQ0FBQ3JFLFNBQVMsQ0FBQzJFLFVBQVUsQ0FBQ2pCO0lBQzVCO0FBQ0Y7QUFFQSxNQUFNRTtJQUNKdkQsWUFBYXNCLEdBQUcsRUFBRUQsS0FBSyxFQUFFZixNQUFNLEVBQUVxQyxHQUFHLEVBQUVuQyxNQUFNLENBQUU7UUFDNUMsSUFBSSxDQUFDYyxHQUFHLEdBQUdBO1FBQ1gsSUFBSSxDQUFDRCxLQUFLLEdBQUdBO1FBQ2IsSUFBSSxDQUFDZixNQUFNLEdBQUdBO1FBQ2QsSUFBSSxDQUFDcUMsR0FBRyxHQUFHQTtRQUNYLElBQUksQ0FBQ25DLE1BQU0sR0FBR0EsVUFBVTtJQUMxQjtBQUNGO0FBRUEsTUFBTXNCLGNBQWMsQ0FBQ2tDLE1BQU12QyxJQUFJNEIsTUFBTTNCO0lBQ25DLElBQUlOLE1BQU1pQyxLQUFLaEMsS0FBSztJQUNwQixJQUFJbUIsUUFBUXdCLE1BQU01QyxNQUFNO1FBQ3RCK0IsSUFBSWEsTUFBTVg7UUFDVixJQUFJLENBQUNXLElBQUksQ0FBQ3pFLFlBQVksRUFDcEI2QixNQUFNOEM7SUFDVjtJQUNBLElBQUk5QyxLQUNGSyxHQUFHOEMsSUFBSSxDQUFDN0MsT0FBT04sSUFBSUMsS0FBSyxFQUFFRCxJQUFJRSxHQUFHLEVBQUUwQztBQUN2QztBQUVBUSxPQUFPQyxPQUFPLEdBQUcxRSIsInNvdXJjZXMiOlsid2VicGFjazovL21hcmtldHBsYWNlLy4vbm9kZV9tb2R1bGVzL29wZW5pZC1jbGllbnQvbm9kZV9tb2R1bGVzL2xydS1jYWNoZS9pbmRleC5qcz82MjE0Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG4vLyBBIGxpbmtlZCBsaXN0IHRvIGtlZXAgdHJhY2sgb2YgcmVjZW50bHktdXNlZC1uZXNzXG5jb25zdCBZYWxsaXN0ID0gcmVxdWlyZSgneWFsbGlzdCcpXG5cbmNvbnN0IE1BWCA9IFN5bWJvbCgnbWF4JylcbmNvbnN0IExFTkdUSCA9IFN5bWJvbCgnbGVuZ3RoJylcbmNvbnN0IExFTkdUSF9DQUxDVUxBVE9SID0gU3ltYm9sKCdsZW5ndGhDYWxjdWxhdG9yJylcbmNvbnN0IEFMTE9XX1NUQUxFID0gU3ltYm9sKCdhbGxvd1N0YWxlJylcbmNvbnN0IE1BWF9BR0UgPSBTeW1ib2woJ21heEFnZScpXG5jb25zdCBESVNQT1NFID0gU3ltYm9sKCdkaXNwb3NlJylcbmNvbnN0IE5PX0RJU1BPU0VfT05fU0VUID0gU3ltYm9sKCdub0Rpc3Bvc2VPblNldCcpXG5jb25zdCBMUlVfTElTVCA9IFN5bWJvbCgnbHJ1TGlzdCcpXG5jb25zdCBDQUNIRSA9IFN5bWJvbCgnY2FjaGUnKVxuY29uc3QgVVBEQVRFX0FHRV9PTl9HRVQgPSBTeW1ib2woJ3VwZGF0ZUFnZU9uR2V0JylcblxuY29uc3QgbmFpdmVMZW5ndGggPSAoKSA9PiAxXG5cbi8vIGxydUxpc3QgaXMgYSB5YWxsaXN0IHdoZXJlIHRoZSBoZWFkIGlzIHRoZSB5b3VuZ2VzdFxuLy8gaXRlbSwgYW5kIHRoZSB0YWlsIGlzIHRoZSBvbGRlc3QuICB0aGUgbGlzdCBjb250YWlucyB0aGUgSGl0XG4vLyBvYmplY3RzIGFzIHRoZSBlbnRyaWVzLlxuLy8gRWFjaCBIaXQgb2JqZWN0IGhhcyBhIHJlZmVyZW5jZSB0byBpdHMgWWFsbGlzdC5Ob2RlLiAgVGhpc1xuLy8gbmV2ZXIgY2hhbmdlcy5cbi8vXG4vLyBjYWNoZSBpcyBhIE1hcCAob3IgUHNldWRvTWFwKSB0aGF0IG1hdGNoZXMgdGhlIGtleXMgdG9cbi8vIHRoZSBZYWxsaXN0Lk5vZGUgb2JqZWN0LlxuY2xhc3MgTFJVQ2FjaGUge1xuICBjb25zdHJ1Y3RvciAob3B0aW9ucykge1xuICAgIGlmICh0eXBlb2Ygb3B0aW9ucyA9PT0gJ251bWJlcicpXG4gICAgICBvcHRpb25zID0geyBtYXg6IG9wdGlvbnMgfVxuXG4gICAgaWYgKCFvcHRpb25zKVxuICAgICAgb3B0aW9ucyA9IHt9XG5cbiAgICBpZiAob3B0aW9ucy5tYXggJiYgKHR5cGVvZiBvcHRpb25zLm1heCAhPT0gJ251bWJlcicgfHwgb3B0aW9ucy5tYXggPCAwKSlcbiAgICAgIHRocm93IG5ldyBUeXBlRXJyb3IoJ21heCBtdXN0IGJlIGEgbm9uLW5lZ2F0aXZlIG51bWJlcicpXG4gICAgLy8gS2luZCBvZiB3ZWlyZCB0byBoYXZlIGEgZGVmYXVsdCBtYXggb2YgSW5maW5pdHksIGJ1dCBvaCB3ZWxsLlxuICAgIGNvbnN0IG1heCA9IHRoaXNbTUFYXSA9IG9wdGlvbnMubWF4IHx8IEluZmluaXR5XG5cbiAgICBjb25zdCBsYyA9IG9wdGlvbnMubGVuZ3RoIHx8IG5haXZlTGVuZ3RoXG4gICAgdGhpc1tMRU5HVEhfQ0FMQ1VMQVRPUl0gPSAodHlwZW9mIGxjICE9PSAnZnVuY3Rpb24nKSA/IG5haXZlTGVuZ3RoIDogbGNcbiAgICB0aGlzW0FMTE9XX1NUQUxFXSA9IG9wdGlvbnMuc3RhbGUgfHwgZmFsc2VcbiAgICBpZiAob3B0aW9ucy5tYXhBZ2UgJiYgdHlwZW9mIG9wdGlvbnMubWF4QWdlICE9PSAnbnVtYmVyJylcbiAgICAgIHRocm93IG5ldyBUeXBlRXJyb3IoJ21heEFnZSBtdXN0IGJlIGEgbnVtYmVyJylcbiAgICB0aGlzW01BWF9BR0VdID0gb3B0aW9ucy5tYXhBZ2UgfHwgMFxuICAgIHRoaXNbRElTUE9TRV0gPSBvcHRpb25zLmRpc3Bvc2VcbiAgICB0aGlzW05PX0RJU1BPU0VfT05fU0VUXSA9IG9wdGlvbnMubm9EaXNwb3NlT25TZXQgfHwgZmFsc2VcbiAgICB0aGlzW1VQREFURV9BR0VfT05fR0VUXSA9IG9wdGlvbnMudXBkYXRlQWdlT25HZXQgfHwgZmFsc2VcbiAgICB0aGlzLnJlc2V0KClcbiAgfVxuXG4gIC8vIHJlc2l6ZSB0aGUgY2FjaGUgd2hlbiB0aGUgbWF4IGNoYW5nZXMuXG4gIHNldCBtYXggKG1MKSB7XG4gICAgaWYgKHR5cGVvZiBtTCAhPT0gJ251bWJlcicgfHwgbUwgPCAwKVxuICAgICAgdGhyb3cgbmV3IFR5cGVFcnJvcignbWF4IG11c3QgYmUgYSBub24tbmVnYXRpdmUgbnVtYmVyJylcblxuICAgIHRoaXNbTUFYXSA9IG1MIHx8IEluZmluaXR5XG4gICAgdHJpbSh0aGlzKVxuICB9XG4gIGdldCBtYXggKCkge1xuICAgIHJldHVybiB0aGlzW01BWF1cbiAgfVxuXG4gIHNldCBhbGxvd1N0YWxlIChhbGxvd1N0YWxlKSB7XG4gICAgdGhpc1tBTExPV19TVEFMRV0gPSAhIWFsbG93U3RhbGVcbiAgfVxuICBnZXQgYWxsb3dTdGFsZSAoKSB7XG4gICAgcmV0dXJuIHRoaXNbQUxMT1dfU1RBTEVdXG4gIH1cblxuICBzZXQgbWF4QWdlIChtQSkge1xuICAgIGlmICh0eXBlb2YgbUEgIT09ICdudW1iZXInKVxuICAgICAgdGhyb3cgbmV3IFR5cGVFcnJvcignbWF4QWdlIG11c3QgYmUgYSBub24tbmVnYXRpdmUgbnVtYmVyJylcblxuICAgIHRoaXNbTUFYX0FHRV0gPSBtQVxuICAgIHRyaW0odGhpcylcbiAgfVxuICBnZXQgbWF4QWdlICgpIHtcbiAgICByZXR1cm4gdGhpc1tNQVhfQUdFXVxuICB9XG5cbiAgLy8gcmVzaXplIHRoZSBjYWNoZSB3aGVuIHRoZSBsZW5ndGhDYWxjdWxhdG9yIGNoYW5nZXMuXG4gIHNldCBsZW5ndGhDYWxjdWxhdG9yIChsQykge1xuICAgIGlmICh0eXBlb2YgbEMgIT09ICdmdW5jdGlvbicpXG4gICAgICBsQyA9IG5haXZlTGVuZ3RoXG5cbiAgICBpZiAobEMgIT09IHRoaXNbTEVOR1RIX0NBTENVTEFUT1JdKSB7XG4gICAgICB0aGlzW0xFTkdUSF9DQUxDVUxBVE9SXSA9IGxDXG4gICAgICB0aGlzW0xFTkdUSF0gPSAwXG4gICAgICB0aGlzW0xSVV9MSVNUXS5mb3JFYWNoKGhpdCA9PiB7XG4gICAgICAgIGhpdC5sZW5ndGggPSB0aGlzW0xFTkdUSF9DQUxDVUxBVE9SXShoaXQudmFsdWUsIGhpdC5rZXkpXG4gICAgICAgIHRoaXNbTEVOR1RIXSArPSBoaXQubGVuZ3RoXG4gICAgICB9KVxuICAgIH1cbiAgICB0cmltKHRoaXMpXG4gIH1cbiAgZ2V0IGxlbmd0aENhbGN1bGF0b3IgKCkgeyByZXR1cm4gdGhpc1tMRU5HVEhfQ0FMQ1VMQVRPUl0gfVxuXG4gIGdldCBsZW5ndGggKCkgeyByZXR1cm4gdGhpc1tMRU5HVEhdIH1cbiAgZ2V0IGl0ZW1Db3VudCAoKSB7IHJldHVybiB0aGlzW0xSVV9MSVNUXS5sZW5ndGggfVxuXG4gIHJmb3JFYWNoIChmbiwgdGhpc3ApIHtcbiAgICB0aGlzcCA9IHRoaXNwIHx8IHRoaXNcbiAgICBmb3IgKGxldCB3YWxrZXIgPSB0aGlzW0xSVV9MSVNUXS50YWlsOyB3YWxrZXIgIT09IG51bGw7KSB7XG4gICAgICBjb25zdCBwcmV2ID0gd2Fsa2VyLnByZXZcbiAgICAgIGZvckVhY2hTdGVwKHRoaXMsIGZuLCB3YWxrZXIsIHRoaXNwKVxuICAgICAgd2Fsa2VyID0gcHJldlxuICAgIH1cbiAgfVxuXG4gIGZvckVhY2ggKGZuLCB0aGlzcCkge1xuICAgIHRoaXNwID0gdGhpc3AgfHwgdGhpc1xuICAgIGZvciAobGV0IHdhbGtlciA9IHRoaXNbTFJVX0xJU1RdLmhlYWQ7IHdhbGtlciAhPT0gbnVsbDspIHtcbiAgICAgIGNvbnN0IG5leHQgPSB3YWxrZXIubmV4dFxuICAgICAgZm9yRWFjaFN0ZXAodGhpcywgZm4sIHdhbGtlciwgdGhpc3ApXG4gICAgICB3YWxrZXIgPSBuZXh0XG4gICAgfVxuICB9XG5cbiAga2V5cyAoKSB7XG4gICAgcmV0dXJuIHRoaXNbTFJVX0xJU1RdLnRvQXJyYXkoKS5tYXAoayA9PiBrLmtleSlcbiAgfVxuXG4gIHZhbHVlcyAoKSB7XG4gICAgcmV0dXJuIHRoaXNbTFJVX0xJU1RdLnRvQXJyYXkoKS5tYXAoayA9PiBrLnZhbHVlKVxuICB9XG5cbiAgcmVzZXQgKCkge1xuICAgIGlmICh0aGlzW0RJU1BPU0VdICYmXG4gICAgICAgIHRoaXNbTFJVX0xJU1RdICYmXG4gICAgICAgIHRoaXNbTFJVX0xJU1RdLmxlbmd0aCkge1xuICAgICAgdGhpc1tMUlVfTElTVF0uZm9yRWFjaChoaXQgPT4gdGhpc1tESVNQT1NFXShoaXQua2V5LCBoaXQudmFsdWUpKVxuICAgIH1cblxuICAgIHRoaXNbQ0FDSEVdID0gbmV3IE1hcCgpIC8vIGhhc2ggb2YgaXRlbXMgYnkga2V5XG4gICAgdGhpc1tMUlVfTElTVF0gPSBuZXcgWWFsbGlzdCgpIC8vIGxpc3Qgb2YgaXRlbXMgaW4gb3JkZXIgb2YgdXNlIHJlY2VuY3lcbiAgICB0aGlzW0xFTkdUSF0gPSAwIC8vIGxlbmd0aCBvZiBpdGVtcyBpbiB0aGUgbGlzdFxuICB9XG5cbiAgZHVtcCAoKSB7XG4gICAgcmV0dXJuIHRoaXNbTFJVX0xJU1RdLm1hcChoaXQgPT5cbiAgICAgIGlzU3RhbGUodGhpcywgaGl0KSA/IGZhbHNlIDoge1xuICAgICAgICBrOiBoaXQua2V5LFxuICAgICAgICB2OiBoaXQudmFsdWUsXG4gICAgICAgIGU6IGhpdC5ub3cgKyAoaGl0Lm1heEFnZSB8fCAwKVxuICAgICAgfSkudG9BcnJheSgpLmZpbHRlcihoID0+IGgpXG4gIH1cblxuICBkdW1wTHJ1ICgpIHtcbiAgICByZXR1cm4gdGhpc1tMUlVfTElTVF1cbiAgfVxuXG4gIHNldCAoa2V5LCB2YWx1ZSwgbWF4QWdlKSB7XG4gICAgbWF4QWdlID0gbWF4QWdlIHx8IHRoaXNbTUFYX0FHRV1cblxuICAgIGlmIChtYXhBZ2UgJiYgdHlwZW9mIG1heEFnZSAhPT0gJ251bWJlcicpXG4gICAgICB0aHJvdyBuZXcgVHlwZUVycm9yKCdtYXhBZ2UgbXVzdCBiZSBhIG51bWJlcicpXG5cbiAgICBjb25zdCBub3cgPSBtYXhBZ2UgPyBEYXRlLm5vdygpIDogMFxuICAgIGNvbnN0IGxlbiA9IHRoaXNbTEVOR1RIX0NBTENVTEFUT1JdKHZhbHVlLCBrZXkpXG5cbiAgICBpZiAodGhpc1tDQUNIRV0uaGFzKGtleSkpIHtcbiAgICAgIGlmIChsZW4gPiB0aGlzW01BWF0pIHtcbiAgICAgICAgZGVsKHRoaXMsIHRoaXNbQ0FDSEVdLmdldChrZXkpKVxuICAgICAgICByZXR1cm4gZmFsc2VcbiAgICAgIH1cblxuICAgICAgY29uc3Qgbm9kZSA9IHRoaXNbQ0FDSEVdLmdldChrZXkpXG4gICAgICBjb25zdCBpdGVtID0gbm9kZS52YWx1ZVxuXG4gICAgICAvLyBkaXNwb3NlIG9mIHRoZSBvbGQgb25lIGJlZm9yZSBvdmVyd3JpdGluZ1xuICAgICAgLy8gc3BsaXQgb3V0IGludG8gMiBpZnMgZm9yIGJldHRlciBjb3ZlcmFnZSB0cmFja2luZ1xuICAgICAgaWYgKHRoaXNbRElTUE9TRV0pIHtcbiAgICAgICAgaWYgKCF0aGlzW05PX0RJU1BPU0VfT05fU0VUXSlcbiAgICAgICAgICB0aGlzW0RJU1BPU0VdKGtleSwgaXRlbS52YWx1ZSlcbiAgICAgIH1cblxuICAgICAgaXRlbS5ub3cgPSBub3dcbiAgICAgIGl0ZW0ubWF4QWdlID0gbWF4QWdlXG4gICAgICBpdGVtLnZhbHVlID0gdmFsdWVcbiAgICAgIHRoaXNbTEVOR1RIXSArPSBsZW4gLSBpdGVtLmxlbmd0aFxuICAgICAgaXRlbS5sZW5ndGggPSBsZW5cbiAgICAgIHRoaXMuZ2V0KGtleSlcbiAgICAgIHRyaW0odGhpcylcbiAgICAgIHJldHVybiB0cnVlXG4gICAgfVxuXG4gICAgY29uc3QgaGl0ID0gbmV3IEVudHJ5KGtleSwgdmFsdWUsIGxlbiwgbm93LCBtYXhBZ2UpXG5cbiAgICAvLyBvdmVyc2l6ZWQgb2JqZWN0cyBmYWxsIG91dCBvZiBjYWNoZSBhdXRvbWF0aWNhbGx5LlxuICAgIGlmIChoaXQubGVuZ3RoID4gdGhpc1tNQVhdKSB7XG4gICAgICBpZiAodGhpc1tESVNQT1NFXSlcbiAgICAgICAgdGhpc1tESVNQT1NFXShrZXksIHZhbHVlKVxuXG4gICAgICByZXR1cm4gZmFsc2VcbiAgICB9XG5cbiAgICB0aGlzW0xFTkdUSF0gKz0gaGl0Lmxlbmd0aFxuICAgIHRoaXNbTFJVX0xJU1RdLnVuc2hpZnQoaGl0KVxuICAgIHRoaXNbQ0FDSEVdLnNldChrZXksIHRoaXNbTFJVX0xJU1RdLmhlYWQpXG4gICAgdHJpbSh0aGlzKVxuICAgIHJldHVybiB0cnVlXG4gIH1cblxuICBoYXMgKGtleSkge1xuICAgIGlmICghdGhpc1tDQUNIRV0uaGFzKGtleSkpIHJldHVybiBmYWxzZVxuICAgIGNvbnN0IGhpdCA9IHRoaXNbQ0FDSEVdLmdldChrZXkpLnZhbHVlXG4gICAgcmV0dXJuICFpc1N0YWxlKHRoaXMsIGhpdClcbiAgfVxuXG4gIGdldCAoa2V5KSB7XG4gICAgcmV0dXJuIGdldCh0aGlzLCBrZXksIHRydWUpXG4gIH1cblxuICBwZWVrIChrZXkpIHtcbiAgICByZXR1cm4gZ2V0KHRoaXMsIGtleSwgZmFsc2UpXG4gIH1cblxuICBwb3AgKCkge1xuICAgIGNvbnN0IG5vZGUgPSB0aGlzW0xSVV9MSVNUXS50YWlsXG4gICAgaWYgKCFub2RlKVxuICAgICAgcmV0dXJuIG51bGxcblxuICAgIGRlbCh0aGlzLCBub2RlKVxuICAgIHJldHVybiBub2RlLnZhbHVlXG4gIH1cblxuICBkZWwgKGtleSkge1xuICAgIGRlbCh0aGlzLCB0aGlzW0NBQ0hFXS5nZXQoa2V5KSlcbiAgfVxuXG4gIGxvYWQgKGFycikge1xuICAgIC8vIHJlc2V0IHRoZSBjYWNoZVxuICAgIHRoaXMucmVzZXQoKVxuXG4gICAgY29uc3Qgbm93ID0gRGF0ZS5ub3coKVxuICAgIC8vIEEgcHJldmlvdXMgc2VyaWFsaXplZCBjYWNoZSBoYXMgdGhlIG1vc3QgcmVjZW50IGl0ZW1zIGZpcnN0XG4gICAgZm9yIChsZXQgbCA9IGFyci5sZW5ndGggLSAxOyBsID49IDA7IGwtLSkge1xuICAgICAgY29uc3QgaGl0ID0gYXJyW2xdXG4gICAgICBjb25zdCBleHBpcmVzQXQgPSBoaXQuZSB8fCAwXG4gICAgICBpZiAoZXhwaXJlc0F0ID09PSAwKVxuICAgICAgICAvLyB0aGUgaXRlbSB3YXMgY3JlYXRlZCB3aXRob3V0IGV4cGlyYXRpb24gaW4gYSBub24gYWdlZCBjYWNoZVxuICAgICAgICB0aGlzLnNldChoaXQuaywgaGl0LnYpXG4gICAgICBlbHNlIHtcbiAgICAgICAgY29uc3QgbWF4QWdlID0gZXhwaXJlc0F0IC0gbm93XG4gICAgICAgIC8vIGRvbnQgYWRkIGFscmVhZHkgZXhwaXJlZCBpdGVtc1xuICAgICAgICBpZiAobWF4QWdlID4gMCkge1xuICAgICAgICAgIHRoaXMuc2V0KGhpdC5rLCBoaXQudiwgbWF4QWdlKVxuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuICB9XG5cbiAgcHJ1bmUgKCkge1xuICAgIHRoaXNbQ0FDSEVdLmZvckVhY2goKHZhbHVlLCBrZXkpID0+IGdldCh0aGlzLCBrZXksIGZhbHNlKSlcbiAgfVxufVxuXG5jb25zdCBnZXQgPSAoc2VsZiwga2V5LCBkb1VzZSkgPT4ge1xuICBjb25zdCBub2RlID0gc2VsZltDQUNIRV0uZ2V0KGtleSlcbiAgaWYgKG5vZGUpIHtcbiAgICBjb25zdCBoaXQgPSBub2RlLnZhbHVlXG4gICAgaWYgKGlzU3RhbGUoc2VsZiwgaGl0KSkge1xuICAgICAgZGVsKHNlbGYsIG5vZGUpXG4gICAgICBpZiAoIXNlbGZbQUxMT1dfU1RBTEVdKVxuICAgICAgICByZXR1cm4gdW5kZWZpbmVkXG4gICAgfSBlbHNlIHtcbiAgICAgIGlmIChkb1VzZSkge1xuICAgICAgICBpZiAoc2VsZltVUERBVEVfQUdFX09OX0dFVF0pXG4gICAgICAgICAgbm9kZS52YWx1ZS5ub3cgPSBEYXRlLm5vdygpXG4gICAgICAgIHNlbGZbTFJVX0xJU1RdLnVuc2hpZnROb2RlKG5vZGUpXG4gICAgICB9XG4gICAgfVxuICAgIHJldHVybiBoaXQudmFsdWVcbiAgfVxufVxuXG5jb25zdCBpc1N0YWxlID0gKHNlbGYsIGhpdCkgPT4ge1xuICBpZiAoIWhpdCB8fCAoIWhpdC5tYXhBZ2UgJiYgIXNlbGZbTUFYX0FHRV0pKVxuICAgIHJldHVybiBmYWxzZVxuXG4gIGNvbnN0IGRpZmYgPSBEYXRlLm5vdygpIC0gaGl0Lm5vd1xuICByZXR1cm4gaGl0Lm1heEFnZSA/IGRpZmYgPiBoaXQubWF4QWdlXG4gICAgOiBzZWxmW01BWF9BR0VdICYmIChkaWZmID4gc2VsZltNQVhfQUdFXSlcbn1cblxuY29uc3QgdHJpbSA9IHNlbGYgPT4ge1xuICBpZiAoc2VsZltMRU5HVEhdID4gc2VsZltNQVhdKSB7XG4gICAgZm9yIChsZXQgd2Fsa2VyID0gc2VsZltMUlVfTElTVF0udGFpbDtcbiAgICAgIHNlbGZbTEVOR1RIXSA+IHNlbGZbTUFYXSAmJiB3YWxrZXIgIT09IG51bGw7KSB7XG4gICAgICAvLyBXZSBrbm93IHRoYXQgd2UncmUgYWJvdXQgdG8gZGVsZXRlIHRoaXMgb25lLCBhbmQgYWxzb1xuICAgICAgLy8gd2hhdCB0aGUgbmV4dCBsZWFzdCByZWNlbnRseSB1c2VkIGtleSB3aWxsIGJlLCBzbyBqdXN0XG4gICAgICAvLyBnbyBhaGVhZCBhbmQgc2V0IGl0IG5vdy5cbiAgICAgIGNvbnN0IHByZXYgPSB3YWxrZXIucHJldlxuICAgICAgZGVsKHNlbGYsIHdhbGtlcilcbiAgICAgIHdhbGtlciA9IHByZXZcbiAgICB9XG4gIH1cbn1cblxuY29uc3QgZGVsID0gKHNlbGYsIG5vZGUpID0+IHtcbiAgaWYgKG5vZGUpIHtcbiAgICBjb25zdCBoaXQgPSBub2RlLnZhbHVlXG4gICAgaWYgKHNlbGZbRElTUE9TRV0pXG4gICAgICBzZWxmW0RJU1BPU0VdKGhpdC5rZXksIGhpdC52YWx1ZSlcblxuICAgIHNlbGZbTEVOR1RIXSAtPSBoaXQubGVuZ3RoXG4gICAgc2VsZltDQUNIRV0uZGVsZXRlKGhpdC5rZXkpXG4gICAgc2VsZltMUlVfTElTVF0ucmVtb3ZlTm9kZShub2RlKVxuICB9XG59XG5cbmNsYXNzIEVudHJ5IHtcbiAgY29uc3RydWN0b3IgKGtleSwgdmFsdWUsIGxlbmd0aCwgbm93LCBtYXhBZ2UpIHtcbiAgICB0aGlzLmtleSA9IGtleVxuICAgIHRoaXMudmFsdWUgPSB2YWx1ZVxuICAgIHRoaXMubGVuZ3RoID0gbGVuZ3RoXG4gICAgdGhpcy5ub3cgPSBub3dcbiAgICB0aGlzLm1heEFnZSA9IG1heEFnZSB8fCAwXG4gIH1cbn1cblxuY29uc3QgZm9yRWFjaFN0ZXAgPSAoc2VsZiwgZm4sIG5vZGUsIHRoaXNwKSA9PiB7XG4gIGxldCBoaXQgPSBub2RlLnZhbHVlXG4gIGlmIChpc1N0YWxlKHNlbGYsIGhpdCkpIHtcbiAgICBkZWwoc2VsZiwgbm9kZSlcbiAgICBpZiAoIXNlbGZbQUxMT1dfU1RBTEVdKVxuICAgICAgaGl0ID0gdW5kZWZpbmVkXG4gIH1cbiAgaWYgKGhpdClcbiAgICBmbi5jYWxsKHRoaXNwLCBoaXQudmFsdWUsIGhpdC5rZXksIHNlbGYpXG59XG5cbm1vZHVsZS5leHBvcnRzID0gTFJVQ2FjaGVcbiJdLCJuYW1lcyI6WyJZYWxsaXN0IiwicmVxdWlyZSIsIk1BWCIsIlN5bWJvbCIsIkxFTkdUSCIsIkxFTkdUSF9DQUxDVUxBVE9SIiwiQUxMT1dfU1RBTEUiLCJNQVhfQUdFIiwiRElTUE9TRSIsIk5PX0RJU1BPU0VfT05fU0VUIiwiTFJVX0xJU1QiLCJDQUNIRSIsIlVQREFURV9BR0VfT05fR0VUIiwibmFpdmVMZW5ndGgiLCJMUlVDYWNoZSIsImNvbnN0cnVjdG9yIiwib3B0aW9ucyIsIm1heCIsIlR5cGVFcnJvciIsIkluZmluaXR5IiwibGMiLCJsZW5ndGgiLCJzdGFsZSIsIm1heEFnZSIsImRpc3Bvc2UiLCJub0Rpc3Bvc2VPblNldCIsInVwZGF0ZUFnZU9uR2V0IiwicmVzZXQiLCJtTCIsInRyaW0iLCJhbGxvd1N0YWxlIiwibUEiLCJsZW5ndGhDYWxjdWxhdG9yIiwibEMiLCJmb3JFYWNoIiwiaGl0IiwidmFsdWUiLCJrZXkiLCJpdGVtQ291bnQiLCJyZm9yRWFjaCIsImZuIiwidGhpc3AiLCJ3YWxrZXIiLCJ0YWlsIiwicHJldiIsImZvckVhY2hTdGVwIiwiaGVhZCIsIm5leHQiLCJrZXlzIiwidG9BcnJheSIsIm1hcCIsImsiLCJ2YWx1ZXMiLCJNYXAiLCJkdW1wIiwiaXNTdGFsZSIsInYiLCJlIiwibm93IiwiZmlsdGVyIiwiaCIsImR1bXBMcnUiLCJzZXQiLCJEYXRlIiwibGVuIiwiaGFzIiwiZGVsIiwiZ2V0Iiwibm9kZSIsIml0ZW0iLCJFbnRyeSIsInVuc2hpZnQiLCJwZWVrIiwicG9wIiwibG9hZCIsImFyciIsImwiLCJleHBpcmVzQXQiLCJwcnVuZSIsInNlbGYiLCJkb1VzZSIsInVuZGVmaW5lZCIsInVuc2hpZnROb2RlIiwiZGlmZiIsImRlbGV0ZSIsInJlbW92ZU5vZGUiLCJjYWxsIiwibW9kdWxlIiwiZXhwb3J0cyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/openid-client/node_modules/lru-cache/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/openid-client/node_modules/object-hash/index.js":
/*!**********************************************************************!*\
  !*** ./node_modules/openid-client/node_modules/object-hash/index.js ***!
  \**********************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval("\nvar crypto = __webpack_require__(/*! crypto */ \"crypto\");\n/**\n * Exported function\n *\n * Options:\n *\n *  - `algorithm` hash algo to be used by this instance: *'sha1', 'md5'\n *  - `excludeValues` {true|*false} hash object keys, values ignored\n *  - `encoding` hash encoding, supports 'buffer', '*hex', 'binary', 'base64'\n *  - `ignoreUnknown` {true|*false} ignore unknown object types\n *  - `replacer` optional function that replaces values before hashing\n *  - `respectFunctionProperties` {*true|false} consider function properties when hashing\n *  - `respectFunctionNames` {*true|false} consider 'name' property of functions for hashing\n *  - `respectType` {*true|false} Respect special properties (prototype, constructor)\n *    when hashing to distinguish between types\n *  - `unorderedArrays` {true|*false} Sort all arrays before hashing\n *  - `unorderedSets` {*true|false} Sort `Set` and `Map` instances before hashing\n *  * = default\n *\n * @param {object} object value to hash\n * @param {object} options hashing options\n * @return {string} hash value\n * @api public\n */ exports = module.exports = objectHash;\nfunction objectHash(object, options) {\n    options = applyDefaults(object, options);\n    return hash(object, options);\n}\n/**\n * Exported sugar methods\n *\n * @param {object} object value to hash\n * @return {string} hash value\n * @api public\n */ exports.sha1 = function(object) {\n    return objectHash(object);\n};\nexports.keys = function(object) {\n    return objectHash(object, {\n        excludeValues: true,\n        algorithm: \"sha1\",\n        encoding: \"hex\"\n    });\n};\nexports.MD5 = function(object) {\n    return objectHash(object, {\n        algorithm: \"md5\",\n        encoding: \"hex\"\n    });\n};\nexports.keysMD5 = function(object) {\n    return objectHash(object, {\n        algorithm: \"md5\",\n        encoding: \"hex\",\n        excludeValues: true\n    });\n};\n// Internals\nvar hashes = crypto.getHashes ? crypto.getHashes().slice() : [\n    \"sha1\",\n    \"md5\"\n];\nhashes.push(\"passthrough\");\nvar encodings = [\n    \"buffer\",\n    \"hex\",\n    \"binary\",\n    \"base64\"\n];\nfunction applyDefaults(object, sourceOptions) {\n    sourceOptions = sourceOptions || {};\n    // create a copy rather than mutating\n    var options = {};\n    options.algorithm = sourceOptions.algorithm || \"sha1\";\n    options.encoding = sourceOptions.encoding || \"hex\";\n    options.excludeValues = sourceOptions.excludeValues ? true : false;\n    options.algorithm = options.algorithm.toLowerCase();\n    options.encoding = options.encoding.toLowerCase();\n    options.ignoreUnknown = sourceOptions.ignoreUnknown !== true ? false : true; // default to false\n    options.respectType = sourceOptions.respectType === false ? false : true; // default to true\n    options.respectFunctionNames = sourceOptions.respectFunctionNames === false ? false : true;\n    options.respectFunctionProperties = sourceOptions.respectFunctionProperties === false ? false : true;\n    options.unorderedArrays = sourceOptions.unorderedArrays !== true ? false : true; // default to false\n    options.unorderedSets = sourceOptions.unorderedSets === false ? false : true; // default to false\n    options.unorderedObjects = sourceOptions.unorderedObjects === false ? false : true; // default to true\n    options.replacer = sourceOptions.replacer || undefined;\n    options.excludeKeys = sourceOptions.excludeKeys || undefined;\n    if (typeof object === \"undefined\") {\n        throw new Error(\"Object argument required.\");\n    }\n    // if there is a case-insensitive match in the hashes list, accept it\n    // (i.e. SHA256 for sha256)\n    for(var i = 0; i < hashes.length; ++i){\n        if (hashes[i].toLowerCase() === options.algorithm.toLowerCase()) {\n            options.algorithm = hashes[i];\n        }\n    }\n    if (hashes.indexOf(options.algorithm) === -1) {\n        throw new Error('Algorithm \"' + options.algorithm + '\"  not supported. ' + \"supported values: \" + hashes.join(\", \"));\n    }\n    if (encodings.indexOf(options.encoding) === -1 && options.algorithm !== \"passthrough\") {\n        throw new Error('Encoding \"' + options.encoding + '\"  not supported. ' + \"supported values: \" + encodings.join(\", \"));\n    }\n    return options;\n}\n/** Check if the given function is a native function */ function isNativeFunction(f) {\n    if (typeof f !== \"function\") {\n        return false;\n    }\n    var exp = /^function\\s+\\w*\\s*\\(\\s*\\)\\s*{\\s+\\[native code\\]\\s+}$/i;\n    return exp.exec(Function.prototype.toString.call(f)) != null;\n}\nfunction hash(object, options) {\n    var hashingStream;\n    if (options.algorithm !== \"passthrough\") {\n        hashingStream = crypto.createHash(options.algorithm);\n    } else {\n        hashingStream = new PassThrough();\n    }\n    if (typeof hashingStream.write === \"undefined\") {\n        hashingStream.write = hashingStream.update;\n        hashingStream.end = hashingStream.update;\n    }\n    var hasher = typeHasher(options, hashingStream);\n    hasher.dispatch(object);\n    if (!hashingStream.update) {\n        hashingStream.end(\"\");\n    }\n    if (hashingStream.digest) {\n        return hashingStream.digest(options.encoding === \"buffer\" ? undefined : options.encoding);\n    }\n    var buf = hashingStream.read();\n    if (options.encoding === \"buffer\") {\n        return buf;\n    }\n    return buf.toString(options.encoding);\n}\n/**\n * Expose streaming API\n *\n * @param {object} object  Value to serialize\n * @param {object} options  Options, as for hash()\n * @param {object} stream  A stream to write the serializiation to\n * @api public\n */ exports.writeToStream = function(object, options, stream) {\n    if (typeof stream === \"undefined\") {\n        stream = options;\n        options = {};\n    }\n    options = applyDefaults(object, options);\n    return typeHasher(options, stream).dispatch(object);\n};\nfunction typeHasher(options, writeTo, context) {\n    context = context || [];\n    var write = function(str) {\n        if (writeTo.update) {\n            return writeTo.update(str, \"utf8\");\n        } else {\n            return writeTo.write(str, \"utf8\");\n        }\n    };\n    return {\n        dispatch: function(value) {\n            if (options.replacer) {\n                value = options.replacer(value);\n            }\n            var type = typeof value;\n            if (value === null) {\n                type = \"null\";\n            }\n            //console.log(\"[DEBUG] Dispatch: \", value, \"->\", type, \" -> \", \"_\" + type);\n            return this[\"_\" + type](value);\n        },\n        _object: function(object) {\n            var pattern = /\\[object (.*)\\]/i;\n            var objString = Object.prototype.toString.call(object);\n            var objType = pattern.exec(objString);\n            if (!objType) {\n                objType = \"unknown:[\" + objString + \"]\";\n            } else {\n                objType = objType[1]; // take only the class name\n            }\n            objType = objType.toLowerCase();\n            var objectNumber = null;\n            if ((objectNumber = context.indexOf(object)) >= 0) {\n                return this.dispatch(\"[CIRCULAR:\" + objectNumber + \"]\");\n            } else {\n                context.push(object);\n            }\n            if (typeof Buffer !== \"undefined\" && Buffer.isBuffer && Buffer.isBuffer(object)) {\n                write(\"buffer:\");\n                return write(object);\n            }\n            if (objType !== \"object\" && objType !== \"function\" && objType !== \"asyncfunction\") {\n                if (this[\"_\" + objType]) {\n                    this[\"_\" + objType](object);\n                } else if (options.ignoreUnknown) {\n                    return write(\"[\" + objType + \"]\");\n                } else {\n                    throw new Error('Unknown object type \"' + objType + '\"');\n                }\n            } else {\n                var keys = Object.keys(object);\n                if (options.unorderedObjects) {\n                    keys = keys.sort();\n                }\n                // Make sure to incorporate special properties, so\n                // Types with different prototypes will produce\n                // a different hash and objects derived from\n                // different functions (`new Foo`, `new Bar`) will\n                // produce different hashes.\n                // We never do this for native functions since some\n                // seem to break because of that.\n                if (options.respectType !== false && !isNativeFunction(object)) {\n                    keys.splice(0, 0, \"prototype\", \"__proto__\", \"constructor\");\n                }\n                if (options.excludeKeys) {\n                    keys = keys.filter(function(key) {\n                        return !options.excludeKeys(key);\n                    });\n                }\n                write(\"object:\" + keys.length + \":\");\n                var self = this;\n                return keys.forEach(function(key) {\n                    self.dispatch(key);\n                    write(\":\");\n                    if (!options.excludeValues) {\n                        self.dispatch(object[key]);\n                    }\n                    write(\",\");\n                });\n            }\n        },\n        _array: function(arr, unordered) {\n            unordered = typeof unordered !== \"undefined\" ? unordered : options.unorderedArrays !== false; // default to options.unorderedArrays\n            var self = this;\n            write(\"array:\" + arr.length + \":\");\n            if (!unordered || arr.length <= 1) {\n                return arr.forEach(function(entry) {\n                    return self.dispatch(entry);\n                });\n            }\n            // the unordered case is a little more complicated:\n            // since there is no canonical ordering on objects,\n            // i.e. {a:1} < {a:2} and {a:1} > {a:2} are both false,\n            // we first serialize each entry using a PassThrough stream\n            // before sorting.\n            // also: we can’t use the same context array for all entries\n            // since the order of hashing should *not* matter. instead,\n            // we keep track of the additions to a copy of the context array\n            // and add all of them to the global context array when we’re done\n            var contextAdditions = [];\n            var entries = arr.map(function(entry) {\n                var strm = new PassThrough();\n                var localContext = context.slice(); // make copy\n                var hasher = typeHasher(options, strm, localContext);\n                hasher.dispatch(entry);\n                // take only what was added to localContext and append it to contextAdditions\n                contextAdditions = contextAdditions.concat(localContext.slice(context.length));\n                return strm.read().toString();\n            });\n            context = context.concat(contextAdditions);\n            entries.sort();\n            return this._array(entries, false);\n        },\n        _date: function(date) {\n            return write(\"date:\" + date.toJSON());\n        },\n        _symbol: function(sym) {\n            return write(\"symbol:\" + sym.toString());\n        },\n        _error: function(err) {\n            return write(\"error:\" + err.toString());\n        },\n        _boolean: function(bool) {\n            return write(\"bool:\" + bool.toString());\n        },\n        _string: function(string) {\n            write(\"string:\" + string.length + \":\");\n            write(string.toString());\n        },\n        _function: function(fn) {\n            write(\"fn:\");\n            if (isNativeFunction(fn)) {\n                this.dispatch(\"[native]\");\n            } else {\n                this.dispatch(fn.toString());\n            }\n            if (options.respectFunctionNames !== false) {\n                // Make sure we can still distinguish native functions\n                // by their name, otherwise String and Function will\n                // have the same hash\n                this.dispatch(\"function-name:\" + String(fn.name));\n            }\n            if (options.respectFunctionProperties) {\n                this._object(fn);\n            }\n        },\n        _number: function(number) {\n            return write(\"number:\" + number.toString());\n        },\n        _xml: function(xml) {\n            return write(\"xml:\" + xml.toString());\n        },\n        _null: function() {\n            return write(\"Null\");\n        },\n        _undefined: function() {\n            return write(\"Undefined\");\n        },\n        _regexp: function(regex) {\n            return write(\"regex:\" + regex.toString());\n        },\n        _uint8array: function(arr) {\n            write(\"uint8array:\");\n            return this.dispatch(Array.prototype.slice.call(arr));\n        },\n        _uint8clampedarray: function(arr) {\n            write(\"uint8clampedarray:\");\n            return this.dispatch(Array.prototype.slice.call(arr));\n        },\n        _int8array: function(arr) {\n            write(\"uint8array:\");\n            return this.dispatch(Array.prototype.slice.call(arr));\n        },\n        _uint16array: function(arr) {\n            write(\"uint16array:\");\n            return this.dispatch(Array.prototype.slice.call(arr));\n        },\n        _int16array: function(arr) {\n            write(\"uint16array:\");\n            return this.dispatch(Array.prototype.slice.call(arr));\n        },\n        _uint32array: function(arr) {\n            write(\"uint32array:\");\n            return this.dispatch(Array.prototype.slice.call(arr));\n        },\n        _int32array: function(arr) {\n            write(\"uint32array:\");\n            return this.dispatch(Array.prototype.slice.call(arr));\n        },\n        _float32array: function(arr) {\n            write(\"float32array:\");\n            return this.dispatch(Array.prototype.slice.call(arr));\n        },\n        _float64array: function(arr) {\n            write(\"float64array:\");\n            return this.dispatch(Array.prototype.slice.call(arr));\n        },\n        _arraybuffer: function(arr) {\n            write(\"arraybuffer:\");\n            return this.dispatch(new Uint8Array(arr));\n        },\n        _url: function(url) {\n            return write(\"url:\" + url.toString(), \"utf8\");\n        },\n        _map: function(map) {\n            write(\"map:\");\n            var arr = Array.from(map);\n            return this._array(arr, options.unorderedSets !== false);\n        },\n        _set: function(set) {\n            write(\"set:\");\n            var arr = Array.from(set);\n            return this._array(arr, options.unorderedSets !== false);\n        },\n        _file: function(file) {\n            write(\"file:\");\n            return this.dispatch([\n                file.name,\n                file.size,\n                file.type,\n                file.lastModfied\n            ]);\n        },\n        _blob: function() {\n            if (options.ignoreUnknown) {\n                return write(\"[blob]\");\n            }\n            throw Error(\"Hashing Blob objects is currently not supported\\n\" + \"(see https://github.com/puleos/object-hash/issues/26)\\n\" + 'Use \"options.replacer\" or \"options.ignoreUnknown\"\\n');\n        },\n        _domwindow: function() {\n            return write(\"domwindow\");\n        },\n        _bigint: function(number) {\n            return write(\"bigint:\" + number.toString());\n        },\n        /* Node.js standard native objects */ _process: function() {\n            return write(\"process\");\n        },\n        _timer: function() {\n            return write(\"timer\");\n        },\n        _pipe: function() {\n            return write(\"pipe\");\n        },\n        _tcp: function() {\n            return write(\"tcp\");\n        },\n        _udp: function() {\n            return write(\"udp\");\n        },\n        _tty: function() {\n            return write(\"tty\");\n        },\n        _statwatcher: function() {\n            return write(\"statwatcher\");\n        },\n        _securecontext: function() {\n            return write(\"securecontext\");\n        },\n        _connection: function() {\n            return write(\"connection\");\n        },\n        _zlib: function() {\n            return write(\"zlib\");\n        },\n        _context: function() {\n            return write(\"context\");\n        },\n        _nodescript: function() {\n            return write(\"nodescript\");\n        },\n        _httpparser: function() {\n            return write(\"httpparser\");\n        },\n        _dataview: function() {\n            return write(\"dataview\");\n        },\n        _signal: function() {\n            return write(\"signal\");\n        },\n        _fsevent: function() {\n            return write(\"fsevent\");\n        },\n        _tlswrap: function() {\n            return write(\"tlswrap\");\n        }\n    };\n}\n// Mini-implementation of stream.PassThrough\n// We are far from having need for the full implementation, and we can\n// make assumptions like \"many writes, then only one final read\"\n// and we can ignore encoding specifics\nfunction PassThrough() {\n    return {\n        buf: \"\",\n        write: function(b) {\n            this.buf += b;\n        },\n        end: function(b) {\n            this.buf += b;\n        },\n        read: function() {\n            return this.buf;\n        }\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/openid-client/node_modules/object-hash/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/openid-client/package.json":
/*!*************************************************!*\
  !*** ./node_modules/openid-client/package.json ***!
  \*************************************************/
/***/ ((module) => {

module.exports = JSON.parse('{"name":"openid-client","version":"5.7.1","description":"OpenID Connect Relying Party (RP, Client) implementation for Node.js runtime, supports passportjs","keywords":["auth","authentication","basic","certified","client","connect","dynamic","electron","hybrid","identity","implicit","oauth","oauth2","oidc","openid","passport","relying party","strategy"],"homepage":"https://github.com/panva/openid-client","repository":"panva/openid-client","funding":{"url":"https://github.com/sponsors/panva"},"license":"MIT","author":"Filip Skokan <<EMAIL>>","exports":{"types":"./types/index.d.ts","import":"./lib/index.mjs","require":"./lib/index.js"},"main":"./lib/index.js","types":"./types/index.d.ts","files":["lib","types/index.d.ts"],"scripts":{"format":"npx prettier --loglevel silent --write ./lib ./test ./certification ./types","test":"mocha test/**/*.test.js"},"dependencies":{"jose":"^4.15.9","lru-cache":"^6.0.0","object-hash":"^2.2.0","oidc-token-hash":"^5.0.3"},"devDependencies":{"@types/node":"^16.18.106","@types/passport":"^1.0.16","base64url":"^3.0.1","chai":"^4.5.0","mocha":"^10.7.3","nock":"^13.5.5","prettier":"^2.8.8","readable-mock-req":"^0.2.2","sinon":"^9.2.4","timekeeper":"^2.3.1"},"standard-version":{"scripts":{"postchangelog":"sed -i \'\' -e \'s/### \\\\[/## [/g\' CHANGELOG.md"},"types":[{"type":"feat","section":"Features"},{"type":"fix","section":"Fixes"},{"type":"chore","hidden":true},{"type":"docs","hidden":true},{"type":"style","hidden":true},{"type":"refactor","section":"Refactor","hidden":false},{"type":"perf","section":"Performance","hidden":false},{"type":"test","hidden":true}]}}');

/***/ })

};
;